import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as o,f as m,i as l}from"./vendor-2ae44a2e.js";import{A as p,G as u,M as d}from"./index-b2ff2fa1.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const E=()=>{const{dispatch:i}=o.useContext(p);o.useContext(u);const t=m(),[s,f]=l(),n=async()=>{let c=new d;try{let a=s.get("token")??null;const r=await c.magicLoginVerify(a);r.error?t("/user/login"):(i({type:"LOGIN",payload:r}),r.role=="admin"?t("/admin/customers"):t(`/${r.role}/dashboard`))}catch{t("/user/login")}};return o.useEffect(()=>{(async()=>await n())()}),e.jsx(e.Fragment,{children:e.jsx("div",{className:"flex justify-center items-center min-w-full min-h-screen",children:e.jsx("svg",{className:"w-24 h-24 animate-spin",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})})})})};export{E as default};
