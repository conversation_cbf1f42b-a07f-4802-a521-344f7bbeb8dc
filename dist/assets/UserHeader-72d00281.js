import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as o,R as d,f as j,L as f,l as w}from"./vendor-2ae44a2e.js";import{c as N}from"./index.esm-4b383179.js";import{M as v,A as g,t as b,G as _}from"./index-b2ff2fa1.js";import{d as A,e as h,f as y,g as C,h as p,i as E}from"./index.esm-42944128.js";import{b as M,c as S,d as L}from"./index.esm-de9a80b6.js";import{a as u,q as O}from"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";function P({title:s,titleId:i,...n},x){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:x,"aria-labelledby":i},n),s?o.createElement("title",{id:i},s):null,o.createElement("path",{fillRule:"evenodd",d:"M18.685 19.097A9.723 9.723 0 0 0 21.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 0 0 3.065 7.097A9.716 9.716 0 0 0 12 21.75a9.716 9.716 0 0 0 6.685-2.653Zm-12.54-1.285A7.486 7.486 0 0 1 12 15a7.486 7.486 0 0 1 5.855 2.812A8.224 8.224 0 0 1 12 20.25a8.224 8.224 0 0 1-5.855-2.438ZM15.75 9a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z",clipRule:"evenodd"}))}const I=o.forwardRef(P),R=I,T=(s=!1)=>{const i=new v,{state:{profile:n},dispatch:x}=d.useContext(g),[r,m]=d.useState(null),l=d.useCallback(()=>{(async()=>{try{const a=await i.getProfile();console.log(a),a!=null&&a.error||(m(()=>a),x({type:"UPDATE_PROFILE",payload:a}))}catch(a){console.log(a.message),b(x,a.message)}})()},[r]);return d.useEffect(()=>{!n||s?l():m(n)},[n]),[r,m]};let k=new v;const F=[{to:"/user/voice",text:"Voice Assistant Test",icon:e.jsx(A,{className:"text-xl text-[#A8A8A8]"}),value:"voice"},{to:"/user/sms",text:"SMS Assistant Test",icon:e.jsx(h,{className:"text-xl text-[#A8A8A8]"}),value:"sms"},{to:"/user/voice_list",text:"Voices",icon:e.jsx(A,{className:"text-xl text-[#A8A8A8]"}),value:"voice_list"},{to:"/user/assistants",text:"Voice Assistants",icon:e.jsx(y,{className:"text-xl text-[#A8A8A8]"}),value:"assistants"},{to:"/user/numbers",text:"Phone #",icon:e.jsx(C,{className:"text-xl text-[#A8A8A8]"}),value:"numbers"},{to:"/user/outbound_campaigns",text:"OutBound Campaigns",icon:e.jsx(p,{className:"text-xl text-[#A8A8A8]"}),value:"outbound_campaigns"},{to:"/user/inbound_campaigns",text:"Inbound Campaigns",icon:e.jsx(p,{className:"text-xl text-[#A8A8A8]"}),value:"inbound_campaigns"},{to:"/user/sms_outbound_campaigns",text:"SMS OutBound Campaigns",icon:e.jsx(p,{className:"text-xl text-[#A8A8A8]"}),value:"sms_outbound_campaigns"},{to:"/user/sms_inbound_campaigns",text:"SMS Inbound Campaigns",icon:e.jsx(p,{className:"text-xl text-[#A8A8A8]"}),value:"sms_inbound_campaigns"},{to:"/user/outbound_call_logs",text:"Outbound Call Logs",icon:e.jsx(M,{className:"text-xl text-[#A8A8A8]"}),value:"outbound_call_logs"},{to:"/user/inbound_call_logs",text:"Inbound Call Logs",icon:e.jsx(S,{className:"text-xl text-[#A8A8A8]"}),value:"inbound_call_logs"},{to:"/user/test_logs",text:"Sample Voice Call Log",icon:e.jsx(L,{className:"text-xl text-[#A8A8A8]"}),value:"test_call_logs"},{to:"/user/test_sms_logs",text:"Sample SMS Followup Logs",icon:e.jsx(h,{className:"text-xl text-[#A8A8A8]"}),value:"test_sms_logs"},{to:"/user/stripe_subscription",text:"Billing",icon:e.jsx(E,{className:"text-xl text-[#A8A8A8]"}),value:"subscription"},{to:"/user/profile",text:"Profile",icon:e.jsx(N,{className:"text-xl text-[#A8A8A8]"}),value:"profile"}],le=()=>{const{state:{isOpen:s,path:i},dispatch:n}=o.useContext(_);d.useContext(g);const{state:x,dispatch:r}=o.useContext(g);o.useState(!1);const m=j(),[l]=T();let a=t=>{n({type:"OPEN_SIDEBAR",payload:{isOpen:t}})};return o.useEffect(()=>{async function t(){try{const c=await k.getProfile();r({type:"UPDATE_PROFILE",payload:c})}catch(c){console.log("Error",c),b(r,c.response.data.message?c.response.data.message:c.message)}}t()},[]),e.jsx(e.Fragment,{children:e.jsxs("div",{className:`z-50 flex max-h-screen flex-1 flex-col bg-white py-4 text-[#A8A8A8] transition-all ${s?"fixed h-screen w-[18rem] min-w-[18rem] max-w-[18rem] md:relative":"relative min-h-screen w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem] bg-transparent text-white"} `,children:[e.jsxs("div",{className:`text-[#393939] ${s?"flex w-full":"flex items-center justify-center"} `,children:[e.jsx("div",{}),s&&e.jsx("div",{className:"text-2xl font-bold",children:e.jsx(f,{to:"/",children:e.jsx("h4",{className:"flex cursor-pointer items-center px-4 pb-4 font-sans font-bold",children:"AutomateIntel - Voice"})})})]}),e.jsx("div",{className:"h-fit w-auto flex-1",children:e.jsx("div",{className:"sidebar-list w-auto",children:e.jsx("ul",{className:"flex flex-wrap px-2 text-sm",children:F.map(t=>e.jsx("li",{className:"block w-full list-none",children:e.jsx(w,{to:t.to,className:`${i==t.value?"active-nav":""} `,children:e.jsxs("div",{className:"flex items-center gap-3",children:[t.icon,s&&e.jsx("span",{children:t.text})]})})},t.value))})})}),e.jsxs("div",{className:"flex justify-between pl-2",children:[e.jsxs(u,{as:"div",className:"relative inline-block text-left",children:[e.jsx("div",{children:e.jsxs(u.Button,{className:"inline-flex w-full items-center justify-center gap-4 rounded-sm border border-gray-200 bg-gray-50 px-4 py-2 text-base font-medium text-transparent hover:bg-gray-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75",children:[e.jsx(R,{className:"h-5 w-5"}),l==null?void 0:l.first_name," ",l==null?void 0:l.last_name]})}),e.jsx(O,{as:o.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:e.jsx(u.Items,{className:"absolute bottom-0 left-full mt-2 w-56 origin-bottom-left divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-transparent/5 focus:outline-none",children:e.jsxs("div",{className:"px-1 py-1",children:[e.jsx(u.Item,{children:({active:t})=>e.jsx(f,{className:`${t?"bg-gray-500 text-white":"text-gray-900"} group flex w-full items-center rounded-md px-3 py-3`,to:"/user/profile",children:"Account"})}),e.jsx(u.Item,{children:({active:t})=>e.jsx("button",{className:`${t?"bg-gray-500 text-white":"text-gray-900"} group flex w-full items-center rounded-md px-3 py-3`,onClick:()=>{r({type:"LOGOUT"}),m("/user/login")},children:"Log out"})})]})})})]}),e.jsx("div",{className:"mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400",children:e.jsx("span",{onClick:()=>a(!s),children:e.jsx("svg",{className:`transition-transform ${s?"":"rotate-180"}`,xmlns:"http:www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z",fill:"#A8A8A8"})})})})]})]})})};export{le as UserHeader,le as default};
