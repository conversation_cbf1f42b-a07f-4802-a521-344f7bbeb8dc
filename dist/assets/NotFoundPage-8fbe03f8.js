import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as s}from"./vendor-2ae44a2e.js";import{S as i}from"./index-f2c2b086.js";import"./qr-scanner-cf010ec4.js";const m=()=>{const[t,r]=s.useState(!0);return console.log(t),s.useEffect(()=>{setTimeout(()=>{r(!1)},5e3)},[]),e.jsx(e.Fragment,{children:t?e.jsx("div",{className:"flex justify-center items-center py-5 w-full min-w-full max-w-full max-h-fit min-h-fit",children:e.jsx(i,{size:100,color:"#0EA5E9"})}):e.jsx("div",{className:"flex justify-center items-center w-full h-screen text-7xl text-gray-700",children:"Not Found"})})};export{m as default};
