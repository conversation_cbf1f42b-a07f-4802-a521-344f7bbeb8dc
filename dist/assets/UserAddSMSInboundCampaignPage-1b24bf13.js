import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as m,r as j}from"./vendor-2ae44a2e.js";import{u as de}from"./react-hook-form-47c010f8.js";import{o as me}from"./yup-5abd4662.js";import{c as xe,a as M}from"./yup-5c93ed04.js";import{M as he,G as ue,A as pe,X as ge,s as L,T as fe,t as we}from"./index-b2ff2fa1.js";import{M as A}from"./MkdInput-a584fac2.js";import{I as be}from"./InteractiveButton-bff38983.js";import{u as je}from"./react-dropzone-7ee839ba.js";import{P as Ne}from"./pizzip-fcee35b8.js";import{D as ve}from"./@xmldom/xmldom-6a8067e2.js";import{_ as ye}from"./react-pdftotext-3aea4f3a.js";import{D as y,I as Te}from"./InformationCircleIcon-620be23d.js";import{e as Se,f as Ce}from"./lucide-react-1246a7ed.js";import{q as N,_ as T}from"./@headlessui/react-7bce1936.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";import"./@fullcalendar/core-a789a586.js";import"./react-pdf-9659a983.js";import"./@react-pdf-viewer/core-ad80e4c3.js";import"./@craftjs/core-9da1c17f.js";import"./react-calendar-366f51e4.js";let v=new he;const q={"English (USA)":"English (USA)","English (UK)":"English (UK)","English (Australia)":"English (Australia)","English (Canada)":"English (Canada)",Japanese:"Japanese",Chinese:"Chinese",German:"German",Hindi:"Hindi","French (France)":"French (France)","French (Canada)":"French (Canada)",Korean:"Korean","Portuguese (Brazil)":"Portuguese (Brazil)","Portuguese (Portugal)":"Portuguese (Portugal)",Italian:"Italian","Spanish (Spain)":"Spanish (Spain)","Spanish (Mexico)":"Spanish (Mexico)",Indonesian:"Indonesian",Dutch:"Dutch",Turkish:"Turkish",Filipino:"Filipino",Polish:"Polish",Swedish:"Swedish",Bulgarian:"Bulgarian",Romanian:"Romanian","Arabic (Saudi Arabia)":"Arabic (Saudi Arabia)","Arabic (UAE)":"Arabic (UAE)",Czech:"Czech",Greek:"Greek",Finnish:"Finnish",Croatian:"Croatian",Malay:"Malay",Slovak:"Slovak",Danish:"Danish",Tamil:"Tamil",Ukrainian:"Ukrainian",Russian:"Russian"},ct=({isOpen:Z,closeSidebar:I})=>{const{dispatch:S}=m.useContext(ue),{state:C,dispatch:$}=m.useContext(pe),W=xe({name:M(),assistant_id:M(),language:M().required("Language is required")}).required(),[z,H]=m.useState([]),[r,J]=m.useState(null),[R,_]=m.useState(!1),[U,V]=m.useState([]),[Q,X]=m.useState(""),[Y,B]=m.useState(!1),[_e,ee]=m.useState([]),[p,te]=m.useState(2e3),k={claude:{totalTokens:15e4,usableTokensForPrompt:112500,MAX_CONTENT_SIZE_LIMIT:84375},gpt_3:{totalTokens:4096,usableTokensForPrompt:3072,MAX_CONTENT_SIZE_LIMIT:2304},gpt_4:{totalTokens:8192,usableTokensForPrompt:6144,MAX_CONTENT_SIZE_LIMIT:4608},gpt_4_extended:{totalTokens:32768,usableTokensForPrompt:24576,MAX_CONTENT_SIZE_LIMIT:18432}},se=()=>{const s=(r==null?void 0:r.reduce((n,x)=>n+x.wordCount,0))||0,t=s>p;return e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Document Preview"}),e.jsxs("p",{className:"text-sm text-white/70",children:["Max Words Allowed: ",p.toLocaleString()," words"]})]}),e.jsxs("div",{className:"mb-4 grid grid-cols-3 gap-4",children:[e.jsxs("div",{className:"rounded-lg bg-[#2d3947] p-3",children:[e.jsx("div",{className:"text-sm text-white/70",children:"Total Files"}),e.jsx("div",{className:"text-lg font-semibold text-white",children:(r==null?void 0:r.length)||0})]}),e.jsxs("div",{className:"rounded-lg bg-[#2d3947] p-3",children:[e.jsx("div",{className:"text-sm text-white/70",children:"Total Words"}),e.jsx("div",{className:"text-lg font-semibold text-white",children:s.toLocaleString()})]}),e.jsxs("div",{className:"rounded-lg bg-[#2d3947] p-3",children:[e.jsx("div",{className:"text-sm text-white/70",children:"Status"}),e.jsx("div",{className:`text-lg font-semibold ${t?"text-red-400":"text-green-400"}`,children:t?"Limit Exceeded":"Within Limit"})]})]}),t&&e.jsxs("div",{className:"mb-4 rounded-lg border border-red-500/30 bg-red-500/20 p-4",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-red-400"}),e.jsx("h4",{className:"font-semibold text-red-400",children:"Content Size Limit Exceeded"})]}),e.jsxs("p",{className:"mb-1 text-sm text-red-300",children:["Content size exceeds the limit of ",p.toLocaleString()," ","words."]}),e.jsx("p",{className:"text-sm text-red-300",children:"Please reduce the number of files or choose smaller documents."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"mb-3 text-lg font-semibold text-white",children:"Uploaded Documents"}),(r==null?void 0:r.length)>0?e.jsx("div",{className:"space-y-2",children:r.map((n,x)=>e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-white/10 bg-[#2d3947] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(y,{className:"h-5 w-5 text-white/70"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:n.name}),e.jsxs("div",{className:"text-sm text-white/70",children:[(n.size/1024).toFixed(2)," KB"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"font-medium text-white",children:[n.wordCount.toLocaleString()," words"]}),e.jsxs("div",{className:"text-sm text-white/70",children:[(n.wordCount/s*100).toFixed(1),"% of total"]})]})]},x))}):e.jsxs("div",{className:"py-8 text-center text-white/70",children:[e.jsx(y,{className:"mx-auto mb-2 h-12 w-12 text-white/50"}),e.jsx("p",{children:"No documents uploaded yet"})]})]}),s>0&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-white/70",children:"Usage"}),e.jsxs("span",{className:"text-sm text-white/70",children:[s.toLocaleString()," / ",p.toLocaleString()," ","words"]})]}),e.jsx("div",{className:"h-2 w-full rounded-full bg-gray-700",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${t?"bg-red-500":s>p*.8?"bg-yellow-500":"bg-green-500"}`,style:{width:`${Math.min(s/p*100,100)}%`}})})]})]})},ae=({allow_preview:s})=>{const[t,n]=m.useState(null),x=(i,l)=>{navigator.clipboard.writeText(i),n(l),setTimeout(()=>n(null),2e3)},g=i=>new Promise((l,f)=>{const h=new FileReader;h.onload=async d=>{try{const a=d.target.result,c=P(a),o=c.split(/\s+/).length;console.log(c,"from docx"),l({content:c,wordCount:o})}catch(a){f(a)}},h.onerror=d=>f(d),h.readAsArrayBuffer(i)}),w=i=>new Promise((l,f)=>{const h=new FileReader;h.onload=function(){try{const d=h.result,a=d.split(/\s+/).length;console.log(d,"from docx"),l({content:d,wordCount:a})}catch(d){f(d)}},h.readAsText(i)});function b(i){return i.charCodeAt(0)===65279&&(i=i.substr(1)),new ve().parseFromString(i,"text/xml")}function P(i){const l=new Ne(i),h=b(l.files["word/document.xml"].asText()).getElementsByTagName("w:p"),d=[];for(let a=0,u=h.length;a<u;a++){let c="";const o=h[a].getElementsByTagName("w:t");for(let D=0,ce=o.length;D<ce;D++){const K=o[D];K.childNodes&&(c+=K.childNodes[0].nodeValue)}c&&d.push(c)}return d.join(" ")}const le=j.useCallback(async i=>{const l=[];let f="";for(const a of i){let u=0,c="";if(a.type==="application/pdf")try{const o=await ye(a);u=o.split(/\s+/).length,c=o}catch(o){console.error("Error reading PDF file:",o)}else if(a.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document"){const o=await g(a);c=o.content,u=o.wordCount}else if(a.type==="text/plain"){const o=await w(a);c=o.content,u=o.wordCount,console.log("Word Count:",u,c,"from txt")}console.log(u,"wordCount"),f+=c,l.push({name:a.name,size:a.size,wordCount:u,content:c})}l.reduce((a,u)=>a+u.wordCount,0)>p?(X(""),B(!1),L(S,"Word Limit Exceeded",5e3,"error")):(X(f),B(!0)),J(l),_(!0)},[]),{getRootProps:oe,getInputProps:re}=je({onDrop:le,accept:{"application/pdf":[".pdf"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"]}});return e.jsxs("div",{className:"mt-5",children:[e.jsxs("label",{className:"mb-2 flex items-center gap-2 text-sm font-bold text-white",children:["Upload Documents",e.jsxs("span",{className:"group relative",children:[e.jsx(Te,{className:"h-5 w-5 cursor-pointer text-white/70"}),e.jsx("div",{className:"absolute bottom-full left-0 mb-2 hidden w-64 rounded-md bg-[#2d3947] p-2 text-sm text-white shadow-lg group-hover:block",children:"Documents forming the knowledge base for the AI assistant"})]})]}),e.jsxs("div",{className:"mb-4 flex gap-3 text-sm text-white/70",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(y,{className:"h-4 w-4"}),"Supported formats:"]}),e.jsx("span",{className:"rounded bg-[#2d3947] px-2 py-0.5",children:"PDF"}),e.jsx("span",{className:"rounded bg-[#2d3947] px-2 py-0.5",children:"DOCX"}),e.jsx("span",{className:"rounded bg-[#2d3947] px-2 py-0.5",children:"TXT"})]}),e.jsxs("div",{...oe({className:"dropzone"}),children:[e.jsx("input",{...re()}),e.jsx("div",{className:"flex h-48 w-full flex-col items-center justify-center rounded-lg border-2 border-dashed border-white/30 p-8 transition-colors hover:border-[#19b2f6]/50",children:e.jsxs("div",{className:"flex flex-col items-center text-center text-white",children:[e.jsx(y,{className:"mb-2 h-8 w-8 text-white/70"}),e.jsx("p",{className:"mb-2 text-lg",children:"Drag & drop files here"}),e.jsx("p",{className:"text-sm text-white/70",children:"or click to select files"})]})})]}),r&&r.length>0&&e.jsxs("div",{className:"mt-4 rounded-lg bg-[#2d3947] p-4",children:[e.jsxs("h3",{className:"mb-3 text-sm font-semibold text-white",children:["Uploaded Files (",r.length,")"]}),e.jsx("div",{className:"space-y-2",children:r.map((i,l)=>e.jsxs("div",{className:"flex items-center justify-between rounded bg-[#1d2937] p-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4 text-white/70"}),e.jsx("span",{className:"text-sm text-white",children:i.name})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("span",{className:"text-sm text-white/70",children:[(i.size/1024).toFixed(2)," KB"]}),e.jsxs("span",{className:"text-sm text-white/70",children:[i.wordCount," words"]}),e.jsx("button",{onClick:()=>x(i.name,l),className:"rounded p-1 transition-colors hover:bg-[#2d3947]",title:"Copy filename",children:t===l?e.jsx(Se,{className:"h-4 w-4 text-green-400"}):e.jsx(Ce,{className:"h-4 w-4 text-white/70 hover:text-white"})})]})]},l))}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx("button",{type:"button",className:"rounded-md bg-[#19b2f6]/80 px-4 py-2 text-sm text-white transition-colors hover:bg-[#19b2f6]",onClick:()=>_(!0),children:"Preview Documents"})})]}),e.jsxs("div",{className:"mt-2 text-sm text-white/70",children:["Maximum ",p.toLocaleString()," words allowed"]})]})},{register:F,handleSubmit:ie,setError:O,formState:{errors:E,isSubmitting:G}}=de({resolver:me(W),defaultValues:{name:"",from_number_id:"",assistant_id:"",language:""}}),ne=async s=>{try{const n=await new fe().create("campaign",{user_id:C.user,name:s.name,knowledge_field:Q,assistant_id:s.assistant_id,campaign_type:4,status:1,from_number_id:s.from_number_id,language:s.language});if(!n.error)L(S,"Added"),I&&I();else if(n.validation){const x=Object.keys(n.validation);for(let g=0;g<x.length;g++){const w=x[g];O(w,{type:"manual",message:n.validation[w]})}}}catch(t){console.log("Error",t),we($,t.message),L(S,t.message,5e3,"error"),O("name",{type:"manual",message:t.message})}};return m.useEffect(()=>{S({type:"SETPATH",payload:{path:"sms_inbound_campaigns"}}),async function(){v.setTable("assistants");const t=await v.callRestAPI({user_id:C.user,filter:[`user_id,eq,${C.user}`]},"GETALL");t.error||H(t.list)}(),async function(){v.setTable("numbers");const t=await v.callRestAPI({user_id:C.user,filter:["status,eq,1"]},"GETALL");t.error||V(t.list)}(),async function(){var n,x,g,w;v.setTable("setting");const t=await v.callRestAPI({},"GETALL");if(!t.error){ee(t.list);const b=t.list.filter(P=>P.setting_key==="llm");b.length&&(console.log("setting",[(n=b[0])==null?void 0:n.setting_value]),console.log("setting",k[(x=b[0])==null?void 0:x.setting_value]),console.log("setting",k[(g=b[0])==null?void 0:g.setting_value].MAX_CONTENT_SIZE_LIMIT),te(k[(w=b[0])==null?void 0:w.setting_value].MAX_CONTENT_SIZE_LIMIT))}}()},[]),e.jsxs("div",{className:"",children:[e.jsx(N,{appear:!1,show:Z,as:j.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[101]",onClose:()=>{R||I()},children:[e.jsx(N.Child,{as:j.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(N.Child,{as:j.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 translate-x-full",enterTo:"opacity-100 translate-x-0",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-x-0",leaveTo:"opacity-0 translate-x-full",children:e.jsx(T.Panel,{className:"h-[95vh] w-full max-w-3xl transform overflow-y-auto bg-[#1d2937]  p-6 text-left align-middle shadow-xl transition-all",children:e.jsxs("div",{className:"p-5",children:[e.jsx("h4",{className:"text-3xl font-medium text-white",children:"Add New SMS Inbound Campaign"}),e.jsxs("form",{className:"mt-7 flex w-full flex-col gap-2",onSubmit:ie(ne),children:[e.jsx(A,{type:"text",page:"add",name:"name",errors:E,label:"Campaign Name",placeholder:"Name of the SMS campaign",register:F,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsx(A,{type:"mapping",page:"add",name:"assistant_id",errors:E,label:"SMS Assistant",placeholder:"Select an Assistant",options:z.map(s=>s.id),mapping:z.reduce((s,t)=>(s[t.id]=t.assistant_name,s),{}),register:F,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsx(A,{type:"mapping",page:"add",name:"from_number_id",errors:E,label:"Inbound Receiving Phone #",placeholder:"Choose Phone Number",options:U.map(s=>s.id),mapping:U.reduce((s,t)=>(s[t.id]=t.number,s),{}),register:F,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsx(A,{type:"mapping",page:"add",name:"language",errors:E,label:"Language",placeholder:"Select Language",options:Object.keys(q),mapping:q,register:F,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsx(ae,{allow_preview:Y}),e.jsx(be,{type:"submit",loading:G,disabled:G,className:"focus:shadow-outline mt-6 rounded bg-[#19b2f6]/80  px-4 py-2 font-semibold text-white focus:outline-none",children:"Submit"})]})]})})})})})]})}),e.jsx(N,{appear:!1,show:R,as:j.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[102]",onClose:()=>_(!1),children:[e.jsx(N.Child,{as:j.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(N.Child,{as:j.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"mx-auto  h-[95vh] w-full max-w-4xl transform overflow-y-auto rounded-xl bg-[#1d2937] p-6 text-left align-middle text-white shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{className:"text-3xl font-medium",children:"Preview Document"}),e.jsx("button",{type:"button",onClick:()=>_(!1),children:e.jsx(ge,{className:"h-6 w-6"})})]}),e.jsx(se,{})]})})})})]})})]})};export{ct as default};
