import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as p,R as u,f as Y}from"./vendor-2ae44a2e.js";import{u as z}from"./react-hook-form-47c010f8.js";import{o as J}from"./yup-5abd4662.js";import{c as Q,a as r}from"./yup-5c93ed04.js";import{A as X,G as Z,M,s as x,t as ee}from"./index-b2ff2fa1.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const _e=({setSidebar:n,closeSidebar:h})=>{var j,w,N,k,_,S,C,P,A,T,E,F,$,q;const[R,U]=p.useState("one_time"),[I,D]=p.useState([]),[g,o]=p.useState(!1),G=Q({product_id:r().required(),name:r().required(),amount:r().required(),type:r().required(),interval:r().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),interval_count:r(),usage_type:r().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),usage_limit:r(),trial_days:r()}).required(),{dispatch:y}=u.useContext(X),{dispatch:d}=u.useContext(Z),H=Y(),{register:l,handleSubmit:b,setError:O,setValue:f,trigger:V,resetField:te,getValues:se,formState:{errors:s}}=z({resolver:J(G)}),B=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"one_time",display:"One Time"},{key:2,value:"recurring",display:"Recurring"}],K=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"licenced",display:"Upfront"},{key:2,value:"metered",display:"Metered"}],W=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"day",display:"Day"},{key:2,value:"week",display:"Week"},{key:3,value:"month",display:"Month"},{key:4,value:"year",display:"Year"},{key:5,value:"lifetime",display:"Lifetime"}],v=async t=>{let i=new M;console.log(t),o(!0);try{const a=await i.addStripePrice(t);if(!a.error)x(d,"Price Added"),H("/admin/stripe_price"),o(!1),h&&h();else{if(a.validation){const L=Object.keys(a.validation);for(let c=0;c<L.length;c++){const m=L[c];console.log(m),O(m,{type:"manual",message:a.validation[m]})}}o(!1)}}catch(a){console.log("Error",a),x(d,a.message),ee(y,a.message)}o(!1)};return u.useEffect(()=>{d({type:"SETPATH",payload:{path:"prices"}}),(async()=>{let t=new M;const{list:i,error:a}=await t.getStripeProducts({limit:"all"});if(a){x(y,"Something went wrong while fetching products list");return}D(i)})()},[]),e.jsxs("div",{className:" mx-auto  rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>n(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add Price"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]",onClick:()=>n(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm",onClick:async()=>{await b(v)(),n(!1)},disabled:g,children:g?"Saving":"Save"})]})]}),e.jsxs("form",{className:" w-full max-w-lg p-4 text-left",onSubmit:b(v),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"product_id",children:"Product"}),e.jsxs("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("product_id"),children:[e.jsx("option",{value:"",children:"Nothing selected"},"prod_default"),I.map((t,i)=>e.jsx("option",{value:t.id,children:t.name},`prod_${t.id}`))]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=s.product_id)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...l("name"),className:`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${(w=s.name)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(N=s.name)==null?void 0:N.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"amount",children:"Amount"}),e.jsx("input",{type:"number",min:.1,step:"any",placeholder:"Amount",...l("amount"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(k=s.amount)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(_=s.amount)==null?void 0:_.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"type",children:"Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("type"),onChange:t=>{const i=t.target.value;U(i),f("type",i),V("type")},children:B.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(S=s.type)==null?void 0:S.message})]}),R==="recurring"?e.jsxs("div",{className:"ml-6",children:[e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"interval",children:"Interval"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("interval"),placeholder:"Select",children:W.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(C=s.interval)==null?void 0:C.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"interval_count",children:"Interval Count"}),e.jsx("input",{type:"number",step:"1",placeholder:"Interval Count",...l("interval_count"),...f("interval_count",1),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(P=s.interval_count)!=null&&P.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(A=s.interval_count)==null?void 0:A.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"usage_type",children:"Usage Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("usage_type"),placeholder:"Select",children:K.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(T=s.usage_type)==null?void 0:T.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Trial Days"}),e.jsx("input",{type:"number",step:"1",placeholder:"0",...l("trial_days"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(E=s.trial_days)!=null&&E.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(F=s.trial_days)==null?void 0:F.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Usage Limit"}),e.jsx("input",{type:"number",step:"1",placeholder:"1000",...l("usage_limit"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${($=s.usage_limit)!=null&&$.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(q=s.usage_limit)==null?void 0:q.message})]})]}):""]})]})};export{_e as default};
