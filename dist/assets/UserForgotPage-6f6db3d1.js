import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{r as S,R,L as B}from"./vendor-2ae44a2e.js";import{u as E}from"./react-hook-form-47c010f8.js";import{o as L}from"./yup-5abd4662.js";import{c as C,a as I}from"./yup-5c93ed04.js";import{G as N,M as F,s as P,t as T}from"./index-b2ff2fa1.js";import{I as $}from"./InteractiveButton-bff38983.js";import{a as q}from"./automate-icon-06435bcb.js";import{b as z}from"./wbg-a13cc141.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";const pe=()=>{const[i,s]=S.useState(!1),b=C({email:I().email().required()}).required(),{register:y,handleSubmit:j,setError:a,formState:{errors:r}}=E({resolver:L(b)}),{dispatch:n}=R.useContext(N),w=async k=>{var l,m,p,d,c,g,x,u;let v=new F;try{s(!0);const e=await v.forgot(k.email,"user");if(!e.error)P(n,"Reset Code Sent",4e3,"success");else if(e.validation){const f=Object.keys(e.validation);for(let o=0;o<f.length;o++){const h=f[o];a(h,{type:"manual",message:e.validation[h]})}}s(!1)}catch(e){s(!1),console.log("Error",e),a("email",{type:"manual",message:(m=(l=e==null?void 0:e.response)==null?void 0:l.data)!=null&&m.message?(d=(p=e==null?void 0:e.response)==null?void 0:p.data)==null?void 0:d.message:e==null?void 0:e.message}),T(n,(g=(c=e==null?void 0:e.response)==null?void 0:c.data)!=null&&g.message?(u=(x=e==null?void 0:e.response)==null?void 0:x.data)==null?void 0:u.message:e==null?void 0:e.message)}};return t.jsx("div",{style:{display:"flex",height:"100vh",backgroundImage:`url(${z})`,backgroundRepeat:"no-repeat",backgroundSize:"cover"},className:"",children:t.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[rgb(255,255,255,0.01)] backdrop-blur-md",children:t.jsx("div",{style:{flex:1,display:"flex",justifyContent:"center",alignItems:"center"},children:t.jsxs("div",{style:{width:"600px",backgroundColor:"#1d2937",padding:"40px",borderRadius:"12px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1)"},children:[t.jsx("img",{src:q,alt:"Logo",style:{width:"50px",height:"50px",marginBottom:"15px",display:"block",marginLeft:"auto",marginRight:"auto"}}),t.jsx("h2",{style:{marginBottom:"20px",color:"#fff",textAlign:"center",fontSize:"1.5rem",fontWeight:"bold"},children:"Forgot Password"}),t.jsxs("form",{onSubmit:j(w),children:[t.jsxs("div",{style:{marginBottom:"15px"},children:[t.jsx("label",{style:{display:"block",color:"white",marginBottom:"15px",fontWeight:"bold"},children:"Email"}),t.jsx("input",{type:"email",className:"h-[42px] bg-transparent",...y("email"),style:{width:"100%",color:"white",padding:"10px",borderRadius:"5px",border:"1px solid #ccc",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"}}),r.email&&t.jsx("p",{style:{color:"red",marginTop:"5px"},children:r.email.message})]}),t.jsx($,{type:"submit",className:"hover: flex h-[42px] w-full items-center justify-center rounded-md bg-[#19b2f6]/80 py-2 tracking-wide text-white outline-none hover:bg-[#19b2f6]/60 focus:outline-none",loading:i,disabled:i,children:t.jsx("span",{children:"Reset Password"})}),t.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",marginBottom:"15px",marginTop:"15px",textAlign:"center"},children:t.jsx(B,{to:"/user/login",style:{color:"white",textDecoration:"none"},children:"Back to Login"})})]})]})})})})};export{pe as default};
