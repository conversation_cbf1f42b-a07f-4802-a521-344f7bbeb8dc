import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r}from"./vendor-2ae44a2e.js";import{_ as l}from"./qr-scanner-cf010ec4.js";import{S as i}from"./index-f2c2b086.js";const o=r.lazy(()=>l(()=>import("./PublicHeader-7b229cab.js"),["assets/PublicHeader-7b229cab.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/index-b2ff2fa1.js","assets/react-confirm-alert-783bc3ae.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-7bce1936.js","assets/react-icons-f29df01f.js","assets/lucide-react-1246a7ed.js","assets/react-loading-skeleton-f53ed7d1.js","assets/react-papaparse-b60a38ab.js","assets/papaparse-2d1475f9.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-9cc92aaf.js","assets/@fortawesome/react-fontawesome-27c5bed3.js","assets/@fortawesome/fontawesome-svg-core-294d29ff.js","assets/@fortawesome/free-solid-svg-icons-11dbc67c.js","assets/@fortawesome/free-regular-svg-icons-3e88f209.js","assets/@fortawesome/free-brands-svg-icons-2414b431.js","assets/index-3134cf1f.css"])),t=({children:s})=>e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsx(o,{}),e.jsx("div",{className:"min-h-screen grow",children:e.jsx(r.Suspense,{fallback:e.jsx("div",{className:"flex h-screen w-full items-center justify-center",children:e.jsx(i,{size:100,color:"#0EA5E9"})}),children:s})})]}),p=r.memo(t);export{p as default};
