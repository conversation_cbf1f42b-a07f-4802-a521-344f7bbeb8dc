import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as D,r}from"./vendor-2ae44a2e.js";import{u as ie}from"./react-hook-form-47c010f8.js";import{o as oe}from"./yup-5abd4662.js";import{c as ne,a as re}from"./yup-5c93ed04.js";import{M as me,G as V,t as ee,s as P}from"./index-b2ff2fa1.js";import{I}from"./InteractiveButton-bff38983.js";import de from"./ModalPrompt-fb38cd0a.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";import"./index-f2c2b086.js";let v=new me;const Ie=()=>{var K,J,Q,X,Y;const T=ne({email:re().email().required()}).required(),{dispatch:c}=D.useContext(V),[g,$]=r.useState(""),[m,M]=D.useState({}),[A,_]=r.useState(!1),[R,i]=r.useState(!1),[y,p]=r.useState(!1),[q,f]=r.useState(!1),[N,w]=r.useState("");r.useState("");const[x,u]=r.useState(!1),[j,k]=r.useState("Profile"),[t,U]=r.useState({}),{dispatch:se}=D.useContext(V),{register:F,handleSubmit:E,setError:C,setValue:H,formState:{errors:S}}=ie({resolver:oe(T)}),te=(s,l,b=!1)=>{let a=m;console.log(l),b?a[s]?a[s]=[...a[s],{file:l.files[0],tempFile:{url:URL.createObjectURL(l.files[0]),name:l.files[0].name,type:l.files[0].type}}]:a[s]=[{file:l.files[0],tempFile:{url:URL.createObjectURL(l.files[0]),name:l.files[0].name,type:l.files[0].type}}]:a[s]={file:l.files[0],tempURL:URL.createObjectURL(l.files[0])},M({...a})};async function B(){try{const s=await v.getProfile();U(s),H("email",s==null?void 0:s.email),H("first_name",s==null?void 0:s.first_name),H("last_name",s==null?void 0:s.last_name),$(s==null?void 0:s.email),w(s==null?void 0:s.photo)}catch(s){console.log("Error",s),ee(c,s.response.data.message?s.response.data.message:s.message)}}const L=async s=>{var l,b;U(s);try{if(u(!0),m&&m.photo&&((l=m.photo)!=null&&l.file)){let o=new FormData;o.append("file",(b=m.photo)==null?void 0:b.file);let n=await v.uploadImage(o);console.log("uploadResult"),console.log(n),s.photo=n.url,P(c,"Profile Photo Updated",1e3)}const a=await v.updateProfile({first_name:s.first_name||(t==null?void 0:t.first_name),last_name:s.last_name||(t==null?void 0:t.last_name),photo:s.photo||N});if(!a.error)P(c,"Profile Updated",4e3),h();else{if(a.validation){const o=Object.keys(a.validation);for(let n=0;n<o.length;n++){const d=o[n];C(d,{type:"manual",message:a.validation[d]})}}h()}if(g!==s.email){const o=await v.updateEmail(s.email);if(!o.error)P(c,"Email Updated",1e3);else if(o.validation){const n=Object.keys(o.validation);for(let d=0;d<n.length;d++){const O=n[d];C(O,{type:"manual",message:o.validation[O]})}}h()}if(s.password.length>0){const o=await v.updatePassword(s.password);if(!o.error)P(c,"Password Updated",2e3);else if(o.validation){const n=Object.keys(o.validation);for(let d=0;d<n.length;d++){const O=n[d];C(O,{type:"manual",message:o.validation[O]})}}}await B(),u(!1)}catch(a){u(!1),console.log("Error",a),C("email",{type:"manual",message:a.response.data.message?a.response.data.message:a.message}),ee(c,a.response.data.message?a.response.data.message:a.message)}};D.useEffect(()=>{se({type:"SETPATH",payload:{path:"profile"}}),B()},[]);const ae=()=>{_(!0)},z=()=>{i(!0)},G=()=>{p(!0)},W=()=>{f(!0)},h=()=>{_(!1),i(!1),p(!1),f(!1)},le=async()=>{try{u(!0);const s=await v.updateProfile({first_name:t==null?void 0:t.first_name,last_name:t==null?void 0:t.last_name,photo:""});if(!s.error)P(c,"Profile Picture Deleted",1e3);else if(s.validation){const l=Object.keys(s.validation);for(let b=0;b<l.length;b++){const a=l[b];C(a,{type:"manual",message:s.validation[a]})}}await B(),u(!1),h()}catch(s){u(!1),console.log("Error",s)}};return e.jsxs("div",{className:"mt-6 w-10/12 rounded-md border",children:[e.jsx("div",{className:"flex items-center border-b border-b-[#E0E0E0] px-8 py-3 text-[#ffffffd1]",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${j==="Profile"?"bg-[#f4f4f4] text-[#525252]":""} `,onClick:()=>k("Profile"),children:"Profile"}),e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${j==="Security"?"bg-[#f4f4f4] text-[#525252]":""} `,onClick:()=>k("Security"),children:"Security"})]})}),e.jsxs("main",{children:[j==="Profile"&&e.jsx("div",{className:"rounded bg-white",children:e.jsxs("form",{onSubmit:E(L),children:[e.jsxs("div",{className:"mb-8 flex items-center border-b border-b-[#E0E0E0] px-10",children:[e.jsxs("div",{className:"relative mb-4 h-[100px] w-fit py-5",children:[m&&((K=m.photo)!=null&&K.tempURL)||N?e.jsx("div",{className:"flex h-[80px] w-[80px] items-center rounded-2xl",children:e.jsx("img",{className:"h-[80px] w-[80px] rounded-2xl object-cover",src:((J=m.photo)==null?void 0:J.tempURL)||N,alt:""})}):null,N||(Q=m.photo)!=null&&Q.file?null:e.jsx("div",{className:"flex h-[80px] w-full items-center justify-center bg-slate-300",children:"Select a picture"}),e.jsx("input",{className:"focus:shadow-outline absolute left-0 top-0 h-full w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 opacity-0 shadow focus:outline-none",id:"photo",type:"file",placeholder:"Photo",name:"photo",onChange:s=>te("photo",s.target)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(X=S.photo)==null?void 0:X.message})]}),e.jsx("div",{className:"ml-6	mr-4	text-sm font-semibold text-black",children:e.jsx(I,{type:"submit",loading:x,disabled:x,children:"Update"})}),e.jsx("div",{className:"ml-3	cursor-pointer	text-sm font-semibold text-gray-600",onClick:ae,children:"Remove"})]}),e.jsxs("div",{className:"mx-10 max-w-lg",children:[e.jsx("p",{className:"mb-3	text-base	font-medium text-gray-900",children:"Personal Details"}),e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"First Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.first_name})]}),e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-black",onClick:z,children:"Edit"})]}),e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"Last Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.last_name})]}),e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-black",onClick:G,children:"Edit"})]}),e.jsxs("div",{className:"mb-6 flex items-center justify-between text-left",children:[e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx("p",{className:"mr-28	text-base	font-medium text-gray-600",children:"Email"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:g})]}),e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-black",onClick:W,children:"Edit"})]})]})]})}),j==="Security"&&e.jsx("div",{className:"rounded bg-white px-10 py-6",children:e.jsx("form",{onSubmit:E(L),className:"max-w-lg",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Password"}),e.jsx("input",{...F("password"),name:"password",className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"password",type:"password",placeholder:"******************"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(Y=S.password)==null?void 0:Y.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(I,{className:"focus:shadow-outline black hover:black rounded px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:x,disabled:x,children:"Update"})})]})})}),A&&e.jsx(de,{actionHandler:le,closeModalFunction:h,title:"Are you sure ? ",message:"Are you sure you want to delete profile picture ? ",acceptText:"DELETE",rejectText:"CANCEL"}),R&&e.jsx(Z,{title:"Edit information",label:"First Name",buttonName:"Save and close",isOpen:z,onClose:h,handleSubmit:E,onSubmit:L,register:F,id:"first_name",submitLoading:x,errors:S}),y&&e.jsx(Z,{title:"Edit information",label:"Last Name",buttonName:"Save and close",isOpen:G,onClose:h,handleSubmit:E,onSubmit:L,register:F,id:"last_name",submitLoading:x,errors:S}),q&&e.jsx(Z,{title:"Change Email",label:"Email",buttonName:"Submit",isOpen:W,onClose:h,handleSubmit:E,onSubmit:L,register:F,id:"email",submitLoading:x,errors:S,defaultValues:t})]})]})},Z=T=>{var j,k;const{title:c,label:g,buttonName:$,isOpen:m,onClose:M,handleSubmit:A,onSubmit:_,register:R,id:i,submitLoading:y,errors:p,defaultValues:q}=T,[f,N]=r.useState(!1),[w,x]=r.useState({email:""}),u=t=>U=>{t==="email"&&x({...w,[t]:U.target.value})};return e.jsx("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:e.jsx("div",{className:`fixed inset-0 z-10 overflow-y-auto ${m?"block":"hidden"} `,children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-lg font-semibold leading-6 text-gray-900",children:c}),e.jsx("button",{className:"text-gray-500 hover:text-gray-700 focus:outline-none",onClick:M,children:e.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("form",{onSubmit:A(_),className:"max-w-lg",children:[f===!0&&e.jsxs("div",{className:"mt-3 flex",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.0003 1.66663C5.39795 1.66663 1.66699 5.39759 1.66699 9.99996C1.66699 14.6023 5.39795 18.3333 10.0003 18.3333C14.6027 18.3333 18.3337 14.6023 18.3337 9.99996C18.3337 5.39759 14.6027 1.66663 10.0003 1.66663ZM8.33366 9.16663C8.33366 8.82145 8.61348 8.54163 8.95866 8.54163H10.0003C10.3455 8.54163 10.6253 8.82145 10.6253 9.16663L10.6253 13.5416C10.6253 13.8868 10.3455 14.1666 10.0003 14.1666C9.65515 14.1666 9.37533 13.8868 9.37533 13.5416L9.37532 9.79163H8.95866C8.61348 9.79163 8.33366 9.5118 8.33366 9.16663ZM10.0003 6.04163C9.65515 6.04163 9.37533 6.32145 9.37533 6.66663C9.37533 7.0118 9.65515 7.29163 10.0003 7.29163C10.3455 7.29163 10.6253 7.0118 10.6253 6.66663C10.6253 6.32145 10.3455 6.04163 10.0003 6.04163Z",fill:"black"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1	text-sm	font-medium text-gray-600",children:"We've send an email to:"}),e.jsx("p",{className:"mb-2	text-sm	font-semibold text-gray-900",children:w==null?void 0:w.email}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"In order to complete the email update click the confirmation link."}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"(the link expires in 24 hours)"})]})]}),f===!1&&(i==="first_name"||i==="last_name")&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:g}),e.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:i,type:"text",placeholder:`Enter ${g} `,name:i,...R(i)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=p==null?void 0:p.id)==null?void 0:j.message})]}),f===!1&&i==="email"&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:g}),e.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:i,type:"text",placeholder:`Enter ${g}`,name:i,...R(i),onChange:u("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(k=p==null?void 0:p.id)==null?void 0:k.message})]}),e.jsxs("div",{className:"mt-4 flex justify-between",children:[e.jsx("button",{className:"mr-2 w-full rounded-md border border-gray-300 px-4 py-2 text-gray-700	",onClick:M,children:"Cancel"}),(i==="first_name"||i==="last_name"||f===!0)&&e.jsx(I,{className:"focus:shadow-outline hover:black w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:y,disabled:y,children:$}),i==="email"&&!f&&e.jsx(I,{className:"focus:shadow-outline hover:black w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:y,disabled:y,onClick:()=>N(!0),children:"Submit"})]})]})]})]})})})};export{Z as EditInfoModal,Ie as default};
