import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as r,r as x}from"./vendor-2ae44a2e.js";import{u as De}from"./react-hook-form-47c010f8.js";import{o as Ie}from"./yup-5abd4662.js";import{c as Me,a as T}from"./yup-5c93ed04.js";import{M as ae,G as Pe,A as ze,X as ee,s as U,t as Le}from"./index-b2ff2fa1.js";import{M as N}from"./MkdInput-a584fac2.js";import{I as Ge}from"./InteractiveButton-bff38983.js";import{z as Oe}from"./react-papaparse-b60a38ab.js";import{u as Ue}from"./react-dropzone-7ee839ba.js";import{P as Re}from"./pizzip-fcee35b8.js";import{D as qe}from"./@xmldom/xmldom-6a8067e2.js";import{_ as He}from"./react-pdftotext-3aea4f3a.js";import{m as te}from"./moment-timezone-69cd48a8.js";import{I as g,D as Ve}from"./InformationCircleIcon-620be23d.js";import{T as Xe}from"./TrashIcon-aa291073.js";import{q as f,_ as y}from"./@headlessui/react-7bce1936.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./papaparse-2d1475f9.js";import"./MoonLoader-62b0139a.js";import"./@fullcalendar/core-a789a586.js";import"./react-pdf-9659a983.js";import"./@react-pdf-viewer/core-ad80e4c3.js";import"./@craftjs/core-9da1c17f.js";import"./react-calendar-366f51e4.js";import"./moment-55cb88ed.js";let S=new ae;const R=["phone","first_name","last_name"],se={"English (USA)":"English (USA)","English (UK)":"English (UK)","English (Australia)":"English (Australia)","English (Canada)":"English (Canada)",Japanese:"Japanese",Chinese:"Chinese",German:"German",Hindi:"Hindi","French (France)":"French (France)","French (Canada)":"French (Canada)",Korean:"Korean","Portuguese (Brazil)":"Portuguese (Brazil)","Portuguese (Portugal)":"Portuguese (Portugal)",Italian:"Italian","Spanish (Spain)":"Spanish (Spain)","Spanish (Mexico)":"Spanish (Mexico)",Indonesian:"Indonesian",Dutch:"Dutch",Turkish:"Turkish",Filipino:"Filipino",Polish:"Polish",Swedish:"Swedish",Bulgarian:"Bulgarian",Romanian:"Romanian","Arabic (Saudi Arabia)":"Arabic (Saudi Arabia)","Arabic (UAE)":"Arabic (UAE)",Czech:"Czech",Greek:"Greek",Finnish:"Finnish",Croatian:"Croatian",Malay:"Malay",Slovak:"Slovak",Danish:"Danish",Tamil:"Tamil",Ukrainian:"Ukrainian",Russian:"Russian"},Dt=({isOpen:ne,closeSidebar:P})=>{const{dispatch:E}=r.useContext(Pe),{state:_,dispatch:le}=r.useContext(ze),oe=Me({name:T(),assistant_id:T(),start_time:T(),valid_time_opening:T(),valid_time_closing:T(),days:T(),language:T().required("Language is required")}).required(),[q,re]=r.useState([]),{CSVReader:ie}=Oe(),[b,H]=r.useState(null),[V,D]=r.useState(!1),[z,ce]=r.useState(null),[X,I]=r.useState(!1),[B,me]=r.useState([]),[de,$]=r.useState(""),[ue,K]=r.useState(!1),[Be,pe]=r.useState([]),[M,he]=r.useState(2e3),[k,xe]=r.useState(""),[$e,ge]=r.useState(""),[F,fe]=r.useState(""),[L,be]=r.useState(""),[G,we]=r.useState(""),Z={anthropic:{totalTokens:15e4,usableTokensForPrompt:112500,MAX_CONTENT_SIZE_LIMIT:84375},openai:{totalTokens:4096,usableTokensForPrompt:3072,MAX_CONTENT_SIZE_LIMIT:2304},gpt_4:{totalTokens:8192,usableTokensForPrompt:6144,MAX_CONTENT_SIZE_LIMIT:4608},gpt_4_extended:{totalTokens:32768,usableTokensForPrompt:24576,MAX_CONTENT_SIZE_LIMIT:18432}},W={0:"All",1:"Sundays only",2:"Mondays only",3:"Tuesdays only",4:"Wednesdays only",5:"Thursdays only",6:"Fridays only",7:"Saturdays only",8:"Weekdays only",9:"Weekends only"},A=x.useMemo(()=>{const t=(b==null?void 0:b.data[0])??[];return{headers:t,data:(b==null?void 0:b.data.slice(1).filter(s=>!(s.length==1&&s[0]==="")).map(s=>{let a={};return s.forEach((n,l)=>{a[t[l]]=n}),a}))??[]}},[b]),je=({allow_preview:t})=>{const s=c=>new Promise((u,v)=>{const p=new FileReader;p.onload=async d=>{try{const o=d.target.result,m=l(o),i=m.split(/\s+/).length;console.log(m,"from docx"),u({content:m,wordCount:i})}catch(o){v(o)}},p.onerror=d=>v(d),p.readAsArrayBuffer(c)}),a=c=>new Promise((u,v)=>{const p=new FileReader;p.onload=function(){try{const d=p.result,o=d.split(/\s+/).length;console.log(d,"from docx"),u({content:d,wordCount:o})}catch(d){v(d)}},p.readAsText(c)});function n(c){return c.charCodeAt(0)===65279&&(c=c.substr(1)),new qe().parseFromString(c,"text/xml")}function l(c){const u=new Re(c),p=n(u.files["word/document.xml"].asText()).getElementsByTagName("w:p"),d=[];for(let o=0,h=p.length;o<h;o++){let m="";const i=p[o].getElementsByTagName("w:t");for(let O=0,Ee=i.length;O<Ee;O++){const Q=i[O];Q.childNodes&&(m+=Q.childNodes[0].nodeValue)}m&&d.push(m)}return d.join(" ")}const C=x.useCallback(async c=>{const u=[];let v="";for(const o of c){let h=0,m="";if(o.type==="application/pdf")try{const i=await He(o);h=i.split(/\s+/).length,m=i}catch(i){console.error("Error reading PDF file:",i)}else if(o.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document"){const i=await s(o);m=i.content,h=i.wordCount}else if(o.type==="text/plain"){const i=await a(o);m=i.content,h=i.wordCount,console.log("Word Count:",h,m,"from txt")}console.log(h,"wordCount"),v+=m,u.push({name:o.name,size:o.size,wordCount:h,content:m})}u.reduce((o,h)=>o+h.wordCount,0)>M?($(""),K(!1),U(E,"Word Limit Exceeded",5e3,"error")):($(v),K(!0)),ce(u),I(!0)},[]),{getRootProps:Y,getInputProps:Ae}=Ue({onDrop:C,accept:{"application/pdf":[".pdf"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"]}});return e.jsxs("div",{className:"mt-5 cursor-pointer",children:[e.jsx("label",{className:"mb-2 block cursor-pointer text-sm font-bold text-white"}),e.jsxs("div",{...Y({className:"dropzone"}),children:[e.jsx("input",{...Ae()}),e.jsx("div",{className:"flex h-60 w-full items-center justify-center rounded-md border-2 border-gray-600 p-4",children:e.jsx("p",{className:"text-center text-white",children:"Drag 'n' drop some files here, or click to select files"})})]}),t&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Uploaded Documents:"}),e.jsx("ul",{children:z.map((c,u)=>e.jsx("li",{className:"mt-2 text-sm text-white",children:c.name},u))}),e.jsx("button",{type:"button",className:"mt-4 rounded-md bg-[#2cc9d5] px-4 py-2 text-white hover:bg-blue-600 focus:outline-none",onClick:()=>I(!0),children:"Preview Documents"})]})]})},{register:w,handleSubmit:ve,setError:Ne,formState:{errors:j,isSubmitting:J},reset:ye}=De({resolver:Ie(oe),defaultValues:{name:"",assistant_id:"",from_number_id:"",days:8}}),Te=()=>{const s=z.reduce((a,n)=>a+n.wordCount,0)>M;return e.jsx("div",{className:"fixed relative inset-0 z-[100] overflow-y-auto bg-[#1d2937] bg-opacity-90 p-4",children:e.jsxs("div",{className:"mx-auto max-w-4xl",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-xl font-bold",children:"Document Preview"}),e.jsxs("p",{children:["Max Words Allowed: ",M," words."]})]}),s&&e.jsxs("div",{className:"mt-4 rounded bg-red-200 p-2 text-red-700",children:[e.jsxs("p",{children:["Content size exceeds the limit of ",M," words."]}),e.jsx("p",{children:"Consider reducing the number of files."})]}),e.jsx("div",{className:"mt-4",children:z.map((a,n)=>e.jsxs("div",{className:"flex items-center justify-between border-b py-2",children:[e.jsx("div",{children:a.name}),e.jsxs("div",{children:[(a.size/1024).toFixed(2)," KB"]}),e.jsxs("div",{children:[a.wordCount," words"]})]},n))})]})})},Ce=async t=>{let s=new ae;try{console.log(t),await s.callRawAPI("/v3/api/custom/voiceoutreach/user/outbound_campaign/create",{name:t.name,contacts:A.data.filter(a=>R.map(n=>a[n]).some(n=>n!==""&&n!==void 0)),assistant_id:t.assistant_id,from_number_id:t.from_number_id,start_time:new Date(t.start_time).toISOString(),valid_time_opening:L,valid_time_closing:G,days:t.days,knowledgeField:de,language:t.language},"POST"),U(E,"Added"),P&&P(),ye()}catch(a){console.log("Error",a),Le(le,a.message),U(E,a.message,5e3,"error"),Ne("name",{type:"manual",message:a.message})}};r.useEffect(()=>{if(k){console.log("conversion started");const s=te(k,"HH:mm").clone().tz("Etc/GMT");be(s.format("HH:mm")),console.log(s,L)}if(F){console.log("conversion started");const s=te(F,"HH:mm").clone().tz("Etc/GMT");we(s.format("HH:mm")),console.log(s,G)}},[k,F]);const Se=t=>{xe(t.target.value)},_e=t=>{ge(t.target.value)},ke=t=>{fe(t.target.value)};r.useEffect(()=>{E({type:"SETPATH",payload:{path:"outbound_campaigns"}}),async function(){S.setTable("assistants");const s=await S.callRestAPI({user_id:_.user,filter:[`user_id,eq,${_.user}`]},"GETALL");s.error||re(s.list)}(),async function(){S.setTable("numbers");const s=await S.callRestAPI({user_id:_.user,filter:["status,eq,1"]},"GETALL");s.error||me(s.list)}(),async function(){var a,n;S.setTable("user_settings");const s=await S.callRestAPI({user_id:_.user,filter:[`user_id,eq,${_.user}`]},"GETALL");if(!s.error){pe(s.list);const l=(a=s.list[0])==null?void 0:a.llm_settings;let C="";try{C=(n=JSON.parse(l))==null?void 0:n.provider}catch{C="anthropic"}console.log("setting",Z[C].MAX_CONTENT_SIZE_LIMIT),he(Z[C].MAX_CONTENT_SIZE_LIMIT)}}()},[]);const Fe=()=>{const t=new Date,s=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),n=t.getDate().toString().padStart(2,"0");return`${s}-${a}-${n}`};return e.jsxs("div",{className:"",children:[e.jsx(f,{appear:!1,show:ne,as:x.Fragment,children:e.jsxs(y,{as:"div",className:"relative z-[100]",onClose:()=>{!V&&!X&&P()},children:[e.jsx(f.Child,{as:x.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(f.Child,{as:x.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 translate-x-full",enterTo:"opacity-100 translate-x-0",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-x-0",leaveTo:"opacity-0 translate-x-full",children:e.jsx(y.Panel,{className:" h-[95vh] w-full max-w-3xl transform overflow-y-auto  bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:e.jsxs("div",{className:"p-5",children:[e.jsx("h4",{className:"text-3xl font-medium text-white",children:"Add New Outbound Campaign"}),e.jsxs("form",{className:"mt-7 flex w-full flex-col gap-2",onSubmit:ve(Ce),children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Campaign Name",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(g,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Enter the name of the campaign."})]})]}),e.jsx(N,{type:"text",page:"add",name:"name",errors:j,placeholder:"Name of the campaign",register:w,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Assistant",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(g,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the assistant responsible for the campaign."})]})]}),e.jsx(N,{type:"mapping",page:"add",name:"assistant_id",errors:j,placeholder:"Assistant",options:q.map(t=>t.id),mapping:q.reduce((t,s)=>(t[s.id]=s.assistant_name,t),{}),register:w,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Choose a number",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(g,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the phone number to be used for the campaign."})]})]}),e.jsx(N,{type:"mapping",page:"add",name:"from_number_id",errors:j,placeholder:"Choose a number",options:B.map(t=>t.id),mapping:B.reduce((t,s)=>(t[s.id]=s.number,t),{}),register:w,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Start Date",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(g,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the start date for the campaign."})]})]}),e.jsx(N,{type:"date",page:"add",name:"start_time",errors:j,placeholder:"Start Date",min:Fe(),onChange:_e,dateTime:!0,register:w,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Calling window start time",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(g,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the start time for the calling window."})]})]}),e.jsx(N,{type:"time",page:"add",name:"valid_time_opening",errors:j,label:"",onChange:Se,time:!0,placeholder:"Calling window start time",register:w,className:"bg-[#1d2937] placeholder:text-gray-300"}),k&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Local Time: ",k," (GMT: ",L,"; The Call is made according to GMT Time.)"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Calling window end time",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(g,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the end time for the calling window."})]})]}),e.jsx(N,{type:"time",page:"add",name:"valid_time_closing",errors:j,label:"",onChange:ke,time:!0,placeholder:"Calling window end time",register:w,className:"bg-[#1d2937] placeholder:text-gray-300"}),F&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Local Time: ",F," (GMT: ",G,"; The Call is made according to GMT Time.)"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Language",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(g,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the language for the campaign"})]})]}),e.jsx(N,{type:"mapping",page:"add",name:"language",errors:j,placeholder:"Select Language",options:Object.keys(se),mapping:se,register:w,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Days AI can call",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(g,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Choose the specific days when the AI is allowed to make calls."})]})]}),e.jsx(N,{type:"mapping",page:"add",name:"days",errors:j,label:"",placeholder:"Days AI can call",options:Object.keys(W),mapping:W,register:w,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsxs("p",{className:"mt-2 text-sm text-white",children:["Need a template?"," ",e.jsx("a",{href:"/contact_template.csv",download:!0,className:"text-blue-600 underline",children:"Download sample CSV format"})]})]}),e.jsxs("div",{className:"mt-5",children:[e.jsxs("label",{className:"mb-2 block cursor-pointer text-sm font-bold text-white",children:["Upload CSV"," ",e.jsx("span",{className:"text-sm font-normal text-white",children:"(Please ensure to include the 'phone' and 'name' columns, as they are required.)"})]}),e.jsx(ie,{onUploadAccepted:t=>{console.log("---------------------------"),console.log(t),console.log("---------------------------"),H(t),D(!0)},children:({getRootProps:t,acceptedFile:s,ProgressBar:a,getRemoveFileProps:n})=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("button",{type:"button",...t(),className:"flex h-[10.375rem] w-full cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-600 bg-transparent text-white ",children:s?e.jsxs("div",{className:"flex flex-col items-center font-bold",children:[e.jsx(Ve,{className:"h-5 w-5"}),s.name]}):e.jsx("div",{className:"bg-transparent font-bold",children:"Select CSV File"})}),s?e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("button",{className:"focus:shadow-outline mt-6 rounded bg-transparent px-4 py-2 font-bold text-white focus:outline-none",type:"button",onClick:()=>D(!0),children:"Preview CSV"}),e.jsxs("button",{...n(),onClick:l=>{H(null),n().onClick(l)},type:"button",className:"mt-3 flex items-center gap-3",children:["Remove ",e.jsx(Xe,{className:"h-6 w-6"})]})]}):null]}),e.jsx(a,{})]})})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Upload Documents ",e.jsx("br",{}),"(Documents forming the companies knowledge base)",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(g,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Upload Documents to build the Assistants knowledge base for the call."})]})]}),e.jsx(je,{allow_preview:ue})]}),e.jsx(Ge,{type:"submit",loading:J,disabled:J,className:"focus:shadow-outline  mt-6 rounded bg-[#19b2f6]/80  px-4 py-2 font-bold text-white hover:bg-[#19b2f6]/60 focus:outline-none",children:"Submit"})]})]})})})})})]})}),e.jsx(f,{appear:!1,show:V&&b!==null,as:x.Fragment,children:e.jsxs(y,{as:"div",className:"relative z-[100]",onClose:()=>D(!1),children:[e.jsx(f.Child,{as:x.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4",children:e.jsx(f.Child,{as:x.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(y.Panel,{className:"mx-auto w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(y.Title,{className:"text-3xl font-medium",children:"Preview CSV"}),e.jsx("button",{type:"button",onClick:()=>D(!1),children:e.jsx(ee,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-5 space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"h-6 w-6 bg-red-300"}),"Missing one or more required columns"]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"h-6 w-6 bg-yellow-300"}),"Missing one or more optional columns"]})]}),e.jsx("div",{className:"small-scroll mt-5 max-h-[60vh] overflow-x-auto overflow-y-auto",children:e.jsxs("table",{className:"w-full border",children:[e.jsx("thead",{className:"border-b bg-gray-50",children:e.jsx("tr",{children:A.headers.map(t=>{const s=R.includes(t);return e.jsxs("th",{className:"whitespace-nowrap px-4 py-2",children:[s?"*":""," ",t]},t)})})}),e.jsx("tbody",{children:A.data.map((t,s)=>{const a=A.headers.map(l=>t[l]).some(l=>l===""||l===void 0),n=R.map(l=>t[l]).some(l=>l===""||l===void 0);return e.jsx("tr",{className:`border-b ${n?"!bg-red-300":""} ${a?"bg-yellow-300":""}`,children:A.headers.map(l=>e.jsx("td",{className:"whitespace-nowrap px-4 py-2",children:t[l]},l))},s)})})]})})]})})})})]})}),e.jsx(f,{appear:!1,show:X,as:x.Fragment,children:e.jsxs(y,{as:"div",className:"relative z-[100]",onClose:()=>I(!1),children:[e.jsx(f.Child,{as:x.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4",children:e.jsx(f.Child,{as:x.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(y.Panel,{className:"mx-auto w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(y.Title,{className:"text-3xl font-medium",children:"Preview Document"}),e.jsx("button",{type:"button",onClick:()=>I(!1),children:e.jsx(ee,{className:"h-6 w-6"})})]}),e.jsx(Te,{})]})})})})]})})]})};export{Dt as default};
