import{j as s}from"./@react-google-maps/api-c55ecefa.js";import{R as o,f as I,r as a,h as P}from"./vendor-2ae44a2e.js";import{u as A}from"./react-hook-form-47c010f8.js";import{o as R}from"./yup-5abd4662.js";import{c as C,a as L}from"./yup-5c93ed04.js";import{M,A as _,G,t as U,s as V}from"./index-b2ff2fa1.js";import"./react-quill-d06fcfc9.js";import{M as B}from"./MkdInput-a584fac2.js";import{I as D}from"./InteractiveButton-bff38983.js";import{S as F}from"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let i=new M;const yt=()=>{const{dispatch:h}=o.useContext(_),g=C({name:L()}).required(),{dispatch:p}=o.useContext(G);o.useState({});const[d,m]=o.useState(!1),[S,n]=o.useState(!1),b=I(),[$,y]=a.useState(""),[q,v]=a.useState(""),[H,E]=a.useState(""),[K,T]=a.useState(""),{register:j,handleSubmit:w,setError:u,setValue:r,formState:{errors:N}}=A({resolver:R(g)}),l=P();a.useEffect(function(){(async function(){try{n(!0),i.setTable("voice_list");const t=await i.callRestAPI({id:Number(l==null?void 0:l.id)},"GET");t.error||(r("transcript",t.model.transcript),r("temp974",t.model.temp974),r("filename",t.model.filename),r("status",t.model.status),y(t.model.transcript),v(t.model.temp974),E(t.model.filename),T(t.model.status),setId(t.model.id),n(!1))}catch(t){n(!1),console.log("error",t),U(h,t.message)}})()},[]);const k=async t=>{m(!0);try{i.setTable("voice_list");const e=await i.callRestAPI({id,name:t.name},"PUT");if(!e.error)V(p,"Updated"),b("/user/voice_list");else if(e.validation){const f=Object.keys(e.validation);for(let c=0;c<f.length;c++){const x=f[c];u(x,{type:"manual",message:e.validation[x]})}}m(!1)}catch(e){m(!1),console.log("Error",e),u("transcript",{type:"manual",message:e.message})}};return o.useEffect(()=>{p({type:"SETPATH",payload:{path:"voice_list"}})},[]),s.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[s.jsx("h4",{className:"text-2xl font-medium",children:"Edit Voice Name"}),S?s.jsx(F,{}):s.jsxs("form",{className:" w-full max-w-lg",onSubmit:w(k),children:[s.jsx(B,{type:"text",page:"edit",name:"transcript",errors:N,label:"Transcript",placeholder:"Transcript",register:j,className:""}),s.jsx(D,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:d,disable:d,children:"Submit"})]})]})};export{yt as default};
