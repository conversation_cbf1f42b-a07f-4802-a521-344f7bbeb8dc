import{R as o}from"./vendor-2ae44a2e.js";const C=o.createContext({}),x=!0;function N({baseColor:i,highlightColor:l,width:s,height:a,borderRadius:r,circle:y,direction:m,duration:c,enableAnimation:f=x}){const e={};return m==="rtl"&&(e["--animation-direction"]="reverse"),typeof c=="number"&&(e["--animation-duration"]=`${c}s`),f||(e["--pseudo-element-display"]="none"),(typeof s=="string"||typeof s=="number")&&(e.width=s),(typeof a=="string"||typeof a=="number")&&(e.height=a),(typeof r=="string"||typeof r=="number")&&(e.borderRadius=r),y&&(e.borderRadius="50%"),typeof i<"u"&&(e["--base-color"]=i),typeof l<"u"&&(e["--highlight-color"]=l),e}function A({count:i=1,wrapper:l,className:s,containerClassName:a,containerTestId:r,circle:y=!1,style:m,...c}){var f,e,u;const O=o.useContext(C),v={...c};for(const[t,n]of Object.entries(c))typeof n>"u"&&delete v[t];const d={...O,...v,circle:y},$={...m,...N(d)};let h="react-loading-skeleton";s&&(h+=` ${s}`);const g=(f=d.inline)!==null&&f!==void 0?f:!1,p=[],b=Math.ceil(i);for(let t=0;t<b;t++){let n=$;if(b>i&&t===b-1){const k=(e=n.width)!==null&&e!==void 0?e:"100%",S=i%1,w=typeof k=="number"?k*S:`calc(${k} * ${S})`;n={...n,width:w}}const E=o.createElement("span",{className:h,style:n,key:t},"‌");g?p.push(E):p.push(o.createElement(o.Fragment,{key:t},E,o.createElement("br",null)))}return o.createElement("span",{className:a,"data-testid":r,"aria-live":"polite","aria-busy":(u=d.enableAnimation)!==null&&u!==void 0?u:x},l?p.map((t,n)=>o.createElement(l,{key:n},t)):p)}export{A as S};
