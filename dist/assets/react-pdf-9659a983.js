import{c as v}from"./vendor-2ae44a2e.js";import"./@react-pdf-viewer/core-ad80e4c3.js";var g={};(function(n){var e=v&&v.__spreadArray||function(t,o,a){if(a||arguments.length===2)for(var r=0,l=o.length,u;r<l;r++)(u||!(r in o))&&(u||(u=Array.prototype.slice.call(o,0,r)),u[r]=o[r]);return t.concat(u||Array.prototype.slice.call(o))};Object.defineProperty(n,"__esModule",{value:!0}),n.allEvents=n.changeEvents=n.otherEvents=n.transitionEvents=n.animationEvents=n.wheelEvents=n.uiEvents=n.pointerEvents=n.touchEvents=n.selectionEvents=n.dragEvents=n.mouseEvents=n.mediaEvents=n.keyboardEvents=n.imageEvents=n.formEvents=n.focusEvents=n.compositionEvents=n.clipboardEvents=void 0,n.clipboardEvents=["onCopy","onCut","onPaste"],n.compositionEvents=["onCompositionEnd","onCompositionStart","onCompositionUpdate"],n.focusEvents=["onFocus","onBlur"],n.formEvents=["onInput","onInvalid","onReset","onSubmit"],n.imageEvents=["onLoad","onError"],n.keyboardEvents=["onKeyDown","onKeyPress","onKeyUp"],n.mediaEvents=["onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onError","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting"],n.mouseEvents=["onClick","onContextMenu","onDoubleClick","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp"],n.dragEvents=["onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop"],n.selectionEvents=["onSelect"],n.touchEvents=["onTouchCancel","onTouchEnd","onTouchMove","onTouchStart"],n.pointerEvents=["onPointerDown","onPointerMove","onPointerUp","onPointerCancel","onGotPointerCapture","onLostPointerCapture","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut"],n.uiEvents=["onScroll"],n.wheelEvents=["onWheel"],n.animationEvents=["onAnimationStart","onAnimationEnd","onAnimationIteration"],n.transitionEvents=["onTransitionEnd"],n.otherEvents=["onToggle"],n.changeEvents=["onChange"],n.allEvents=e(e(e(e(e(e(e(e(e(e(e(e(e(e(e(e(e(e([],n.clipboardEvents,!0),n.compositionEvents,!0),n.focusEvents,!0),n.formEvents,!0),n.imageEvents,!0),n.keyboardEvents,!0),n.mediaEvents,!0),n.mouseEvents,!0),n.dragEvents,!0),n.selectionEvents,!0),n.touchEvents,!0),n.pointerEvents,!0),n.uiEvents,!0),n.wheelEvents,!0),n.animationEvents,!0),n.transitionEvents,!0),n.changeEvents,!0),n.otherEvents,!0);function i(t,o){var a={};return n.allEvents.forEach(function(r){var l=t[r];l&&(o?a[r]=function(u){return l(u,o(r))}:a[r]=l)}),a}n.default=i})(g);var f={};Object.defineProperty(f,"__esModule",{value:!0});function h(n){var e=!1,i=new Promise(function(t,o){n.then(function(a){return!e&&t(a)}).catch(function(a){return!e&&o(a)})});return{promise:i,cancel:function(){e=!0}}}f.default=h;var m={},c=Object.prototype.hasOwnProperty;function E(n,e,i){for(i of n.keys())if(s(i,e))return i}function s(n,e){var i,t,o;if(n===e)return!0;if(n&&e&&(i=n.constructor)===e.constructor){if(i===Date)return n.getTime()===e.getTime();if(i===RegExp)return n.toString()===e.toString();if(i===Array){if((t=n.length)===e.length)for(;t--&&s(n[t],e[t]););return t===-1}if(i===Set){if(n.size!==e.size)return!1;for(t of n)if(o=t,o&&typeof o=="object"&&(o=E(e,o),!o)||!e.has(o))return!1;return!0}if(i===Map){if(n.size!==e.size)return!1;for(t of n)if(o=t[0],o&&typeof o=="object"&&(o=E(e,o),!o)||!s(t[1],e.get(o)))return!1;return!0}if(i===ArrayBuffer)n=new Uint8Array(n),e=new Uint8Array(e);else if(i===DataView){if((t=n.byteLength)===e.byteLength)for(;t--&&n.getInt8(t)===e.getInt8(t););return t===-1}if(ArrayBuffer.isView(n)){if((t=n.byteLength)===e.byteLength)for(;t--&&n[t]===e[t];);return t===-1}if(!i||typeof n=="object"){t=0;for(i in n)if(c.call(n,i)&&++t&&!c.call(e,i)||!(i in e)||!s(n[i],e[i]))return!1;return Object.keys(e).length===t}}return n!==n&&e!==e}m.dequal=s;var d={};Object.defineProperty(d,"__esModule",{value:!0});function y(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var i=n.filter(Boolean);if(i.length<=1){var t=i[0];return t||null}return function(a){i.forEach(function(r){typeof r=="function"?r(a):r&&(r.current=a)})}}d.default=y;export{f as a,d as b,g as c,m as d};
