import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{r as o,R as a}from"./vendor-2ae44a2e.js";import{A as r,G as p}from"./index-b2ff2fa1.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const C=()=>{o.useContext(r);const{state:s,dispatch:e}=o.useContext(p);return a.useEffect(()=>{e({type:"SETPATH",payload:{path:"integrations"}})},[]),t.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:t.jsxs("div",{className:"grid max-w-[700px] grid-cols-1 gap-[20px] md:grid-cols-3",children:[t.jsx("button",{className:"mt-10 rounded-[3px] bg-[black]  px-10 py-3 text-white",children:"Integrate Slack"}),t.jsx("button",{className:"mt-10 rounded-[3px] bg-[black]  px-10 py-3 text-white",children:"Integrate Whatsapp"}),t.jsx("button",{className:"mt-10 rounded-[3px] bg-[black]  px-10 py-3 text-white",children:"Integrate SMS"})]})})};export{C as default};
