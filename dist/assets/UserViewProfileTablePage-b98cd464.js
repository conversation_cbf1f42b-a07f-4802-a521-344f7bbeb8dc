import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as r,h as n}from"./vendor-2ae44a2e.js";import"./yup-5c93ed04.js";import{M as x,G as m,t as f}from"./index-b2ff2fa1.js";import{S as p}from"./index-a74110af.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let l=new x;const F=()=>{r.useContext(m);const{dispatch:c}=r.useContext(m),[s,o]=r.useState({}),[d,t]=r.useState(!0),a=n();return r.useEffect(function(){(async function(){try{t(!0),l.setTable("profile");const i=await l.callRestAPI({id:Number(a==null?void 0:a.id),join:""},"GET");i.error||(o(i.model),t(!1))}catch(i){t(!1),console.log("error",i),f(c,i.message)}})()},[]),e.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:d?e.jsx(p,{}):e.jsxs(e.Fragment,{children:[e.jsx("h4",{className:"text-2xl font-medium",children:"View Profile"}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"User Id"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.user_id})]})}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"Fcm Token"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.fcm_token})]})}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"Device Id"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.device_id})]})}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"Device Type"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.device_type})]})})]})})};export{F as default};
