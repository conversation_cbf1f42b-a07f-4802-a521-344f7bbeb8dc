import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as i,r as g}from"./vendor-2ae44a2e.js";import{u as De}from"./react-hook-form-47c010f8.js";import{o as Ie}from"./yup-5abd4662.js";import{c as Pe,a as _}from"./yup-5c93ed04.js";import{M as ne,G as Le,A as ze,X as te,s as U,t as Ge}from"./index-b2ff2fa1.js";import{M as S}from"./MkdInput-a584fac2.js";import{I as Oe}from"./InteractiveButton-bff38983.js";import{z as Ue}from"./react-papaparse-b60a38ab.js";import{u as $e}from"./react-dropzone-7ee839ba.js";import{P as Re}from"./pizzip-fcee35b8.js";import{D as qe}from"./@xmldom/xmldom-6a8067e2.js";import{_ as He}from"./react-pdftotext-3aea4f3a.js";import{m as se}from"./moment-timezone-69cd48a8.js";import{I as b,D as $}from"./InformationCircleIcon-620be23d.js";import{T as Ve}from"./TrashIcon-aa291073.js";import{q as f,_ as T}from"./@headlessui/react-7bce1936.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./papaparse-2d1475f9.js";import"./MoonLoader-62b0139a.js";import"./@fullcalendar/core-a789a586.js";import"./react-pdf-9659a983.js";import"./@react-pdf-viewer/core-ad80e4c3.js";import"./@craftjs/core-9da1c17f.js";import"./react-calendar-366f51e4.js";import"./moment-55cb88ed.js";let M=new ne;const R=["phone","first_name","last_name"],ae={"English (USA)":"English (USA)","English (UK)":"English (UK)","English (Australia)":"English (Australia)","English (Canada)":"English (Canada)",Japanese:"Japanese",Chinese:"Chinese",German:"German",Hindi:"Hindi","French (France)":"French (France)","French (Canada)":"French (Canada)",Korean:"Korean","Portuguese (Brazil)":"Portuguese (Brazil)","Portuguese (Portugal)":"Portuguese (Portugal)",Italian:"Italian","Spanish (Spain)":"Spanish (Spain)","Spanish (Mexico)":"Spanish (Mexico)",Indonesian:"Indonesian",Dutch:"Dutch",Turkish:"Turkish",Filipino:"Filipino",Polish:"Polish",Swedish:"Swedish",Bulgarian:"Bulgarian",Romanian:"Romanian","Arabic (Saudi Arabia)":"Arabic (Saudi Arabia)","Arabic (UAE)":"Arabic (UAE)",Czech:"Czech",Greek:"Greek",Finnish:"Finnish",Croatian:"Croatian",Malay:"Malay",Slovak:"Slovak",Danish:"Danish",Tamil:"Tamil",Ukrainian:"Ukrainian",Russian:"Russian"},At=({isOpen:le,closeSidebar:L})=>{const{dispatch:D}=i.useContext(Le),{state:k,dispatch:oe}=i.useContext(ze),ie=Pe({name:_(),assistant_id:_(),start_time:_(),valid_time_opening:_(),valid_time_closing:_(),days:_(),language:_().required("Language is required")}).required(),[q,re]=i.useState([]),{CSVReader:ce}=Ue(),[w,H]=i.useState(null),[V,I]=i.useState(!1),[m,de]=i.useState(null),[X,P]=i.useState(!1),[B,me]=i.useState([]),[he,W]=i.useState(""),[xe,K]=i.useState(!1),[Xe,ue]=i.useState([]),[C,pe]=i.useState(2e3),[F,ge]=i.useState(""),[Be,be]=i.useState(""),[E,fe]=i.useState(""),[z,we]=i.useState(""),[G,je]=i.useState(""),Z={anthropic:{totalTokens:15e4,usableTokensForPrompt:112500,MAX_CONTENT_SIZE_LIMIT:84375},openai:{totalTokens:4096,usableTokensForPrompt:3072,MAX_CONTENT_SIZE_LIMIT:2304},gpt_4:{totalTokens:8192,usableTokensForPrompt:6144,MAX_CONTENT_SIZE_LIMIT:4608},gpt_4_extended:{totalTokens:32768,usableTokensForPrompt:24576,MAX_CONTENT_SIZE_LIMIT:18432}},J={0:"All",1:"Sundays only",2:"Mondays only",3:"Tuesdays only",4:"Wednesdays only",5:"Thursdays only",6:"Fridays only",7:"Saturdays only",8:"Weekdays only",9:"Weekends only"},A=g.useMemo(()=>{const t=(w==null?void 0:w.data[0])??[];return{headers:t,data:(w==null?void 0:w.data.slice(1).filter(s=>!(s.length==1&&s[0]==="")).map(s=>{let a={};return s.forEach((n,l)=>{a[t[l]]=n}),a}))??[]}},[w]),Ne=({allow_preview:t})=>{const s=c=>new Promise((x,y)=>{const u=new FileReader;u.onload=async h=>{try{const o=h.target.result,d=l(o),r=d.split(/\s+/).length;console.log(d,"from docx"),x({content:d,wordCount:r})}catch(o){y(o)}},u.onerror=h=>y(h),u.readAsArrayBuffer(c)}),a=c=>new Promise((x,y)=>{const u=new FileReader;u.onload=function(){try{const h=u.result,o=h.split(/\s+/).length;console.log(h,"from docx"),x({content:h,wordCount:o})}catch(h){y(h)}},u.readAsText(c)});function n(c){return c.charCodeAt(0)===65279&&(c=c.substr(1)),new qe().parseFromString(c,"text/xml")}function l(c){const x=new Re(c),u=n(x.files["word/document.xml"].asText()).getElementsByTagName("w:p"),h=[];for(let o=0,p=u.length;o<p;o++){let d="";const r=u[o].getElementsByTagName("w:t");for(let O=0,Ae=r.length;O<Ae;O++){const ee=r[O];ee.childNodes&&(d+=ee.childNodes[0].nodeValue)}d&&h.push(d)}return h.join(" ")}const v=g.useCallback(async c=>{const x=[];let y="";for(const o of c){let p=0,d="";if(o.type==="application/pdf")try{const r=await He(o);p=r.split(/\s+/).length,d=r}catch(r){console.error("Error reading PDF file:",r)}else if(o.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document"){const r=await s(o);d=r.content,p=r.wordCount}else if(o.type==="text/plain"){const r=await a(o);d=r.content,p=r.wordCount,console.log("Word Count:",p,d,"from txt")}console.log(p,"wordCount"),y+=d,x.push({name:o.name,size:o.size,wordCount:p,content:d})}x.reduce((o,p)=>o+p.wordCount,0)>C?(W(""),K(!1),U(D,"Word Limit Exceeded",5e3,"error")):(W(y),K(!0)),de(x),P(!0)},[]),{getRootProps:Q,getInputProps:Ee}=$e({onDrop:v,accept:{"application/pdf":[".pdf"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"]}});return e.jsxs("div",{className:"mt-5 cursor-pointer",children:[e.jsx("label",{className:"mb-2 block cursor-pointer text-sm font-bold text-white"}),e.jsxs("div",{...Q({className:"dropzone"}),children:[e.jsx("input",{...Ee()}),e.jsx("div",{className:"flex h-60 w-full items-center justify-center rounded-md border-2 border-gray-600 p-4",children:e.jsx("p",{className:"text-center text-white",children:"Drag 'n' drop some files here, or click to select files"})})]}),t&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Uploaded Documents:"}),e.jsx("ul",{children:m==null?void 0:m.map((c,x)=>e.jsx("li",{className:"mt-2 text-sm text-white",children:c.name},x))}),e.jsx("button",{type:"button",className:"mt-4 rounded-md bg-[#2cc9d5] px-4 py-2 text-white hover:bg-blue-600 focus:outline-none",onClick:()=>P(!0),children:"Preview Documents"})]})]})},{register:j,handleSubmit:ve,setError:ye,formState:{errors:N,isSubmitting:Y},reset:Se}=De({resolver:Ie(ie),defaultValues:{name:"",assistant_id:"",from_number_id:"",days:8}}),Te=()=>{const t=(m==null?void 0:m.reduce((a,n)=>a+n.wordCount,0))||0,s=t>C;return e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Document Preview"}),e.jsxs("p",{className:"text-sm text-white/70",children:["Max Words Allowed: ",C.toLocaleString()," words"]})]}),e.jsxs("div",{className:"mb-4 grid grid-cols-3 gap-4",children:[e.jsxs("div",{className:"rounded-lg bg-[#2d3947] p-3",children:[e.jsx("div",{className:"text-sm text-white/70",children:"Total Files"}),e.jsx("div",{className:"text-lg font-semibold text-white",children:(m==null?void 0:m.length)||0})]}),e.jsxs("div",{className:"rounded-lg bg-[#2d3947] p-3",children:[e.jsx("div",{className:"text-sm text-white/70",children:"Total Words"}),e.jsx("div",{className:"text-lg font-semibold text-white",children:t.toLocaleString()})]}),e.jsxs("div",{className:"rounded-lg bg-[#2d3947] p-3",children:[e.jsx("div",{className:"text-sm text-white/70",children:"Status"}),e.jsx("div",{className:`text-lg font-semibold ${s?"text-red-400":"text-green-400"}`,children:s?"Limit Exceeded":"Within Limit"})]})]}),s&&e.jsxs("div",{className:"mb-4 rounded-lg border border-red-500/30 bg-red-500/20 p-4",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-red-400"}),e.jsx("h4",{className:"font-semibold text-red-400",children:"Content Size Limit Exceeded"})]}),e.jsxs("p",{className:"mb-1 text-sm text-red-300",children:["Content size exceeds the limit of ",C.toLocaleString()," ","words."]}),e.jsx("p",{className:"text-sm text-red-300",children:"Please reduce the number of files or choose smaller documents."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"mb-3 text-lg font-semibold text-white",children:"Uploaded Documents"}),(m==null?void 0:m.length)>0?e.jsx("div",{className:"space-y-2",children:m.map((a,n)=>e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-white/10 bg-[#2d3947] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx($,{className:"h-5 w-5 text-white/70"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:a.name}),e.jsxs("div",{className:"text-sm text-white/70",children:[(a.size/1024).toFixed(2)," KB"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"font-medium text-white",children:[a.wordCount.toLocaleString()," words"]}),e.jsxs("div",{className:"text-sm text-white/70",children:[(a.wordCount/t*100).toFixed(1),"% of total"]})]})]},n))}):e.jsxs("div",{className:"py-8 text-center text-white/70",children:[e.jsx($,{className:"mx-auto mb-2 h-12 w-12 text-white/50"}),e.jsx("p",{children:"No documents uploaded yet"})]})]}),t>0&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-white/70",children:"Usage"}),e.jsxs("span",{className:"text-sm text-white/70",children:[t.toLocaleString()," / ",C.toLocaleString()," ","words"]})]}),e.jsx("div",{className:"h-2 w-full rounded-full bg-gray-700",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${s?"bg-red-500":t>C*.8?"bg-yellow-500":"bg-green-500"}`,style:{width:`${Math.min(t/C*100,100)}%`}})})]})]})},Ce=()=>{const t=new Date,s=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),n=t.getDate().toString().padStart(2,"0"),l=t.getHours().toString().padStart(2,"0"),v=t.getMinutes().toString().padStart(2,"0");return`${s}-${a}-${n}T${l}:${v}`},_e=async t=>{let s=new ne;try{console.log(t),await s.callRawAPI("/v3/api/custom/voiceoutreach/user/sms_outbound_campaign/create",{name:t.name,contacts:A.data.filter(a=>R.map(n=>a[n]).some(n=>n!==""&&n!==void 0)),assistant_id:t.assistant_id,from_number_id:t.from_number_id,start_time:new Date(t.start_time).toISOString(),valid_time_opening:z,valid_time_closing:G,days:t.days,knowledgeField:he,language:t.language},"POST"),U(D,"Added"),L&&L(),Se()}catch(a){console.log("Error",a),Ge(oe,a.message),U(D,a.message,5e3,"error"),ye("name",{type:"manual",message:a.message})}};i.useEffect(()=>{if(F){console.log("conversion started");const s=se(F,"HH:mm").clone().tz("Etc/GMT");we(s.format("HH:mm")),console.log(s,z)}if(E){console.log("conversion started");const s=se(E,"HH:mm").clone().tz("Etc/GMT");je(s.format("HH:mm")),console.log(s,G)}},[F,E]);const Me=t=>{ge(t.target.value)},ke=t=>{be(t.target.value)},Fe=t=>{fe(t.target.value)};return i.useEffect(()=>{D({type:"SETPATH",payload:{path:"sms_outbound_campaigns"}}),async function(){M.setTable("assistants");const s=await M.callRestAPI({user_id:k.user,filter:[`user_id,eq,${k.user}`]},"GETALL");s.error||re(s.list)}(),async function(){M.setTable("numbers");const s=await M.callRestAPI({user_id:k.user,filter:["status,eq,1"]},"GETALL");s.error||me(s.list)}(),async function(){var a,n;M.setTable("user_settings");const s=await M.callRestAPI({user_id:k.user,filter:[`user_id,eq,${k.user}`]},"GETALL");if(!s.error){ue(s.list);const l=(a=s.list[0])==null?void 0:a.llm_settings;let v="";try{v=(n=JSON.parse(l))==null?void 0:n.provider}catch{v="anthropic"}console.log("setting",Z[v].MAX_CONTENT_SIZE_LIMIT),pe(Z[v].MAX_CONTENT_SIZE_LIMIT)}}()},[]),e.jsxs("div",{className:"",children:[e.jsx(f,{appear:!1,show:le,as:g.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[100]",onClose:()=>{!V&&!X&&L()},children:[e.jsx(f.Child,{as:g.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(f.Child,{as:g.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 translate-x-full",enterTo:"opacity-100 translate-x-0",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-x-0",leaveTo:"opacity-0 translate-x-full",children:e.jsx(T.Panel,{className:" h-[95vh] w-full max-w-3xl transform overflow-y-auto  bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:e.jsxs("div",{className:"p-5",children:[e.jsx("h4",{className:"text-3xl font-medium text-white",children:"Add New SMS Outbound Campaign"}),e.jsxs("form",{className:"mt-7 flex w-full flex-col gap-2",onSubmit:ve(_e),children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Campaign Name",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(b,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Enter the name of the SMS campaign."})]})]}),e.jsx(S,{type:"text",page:"add",name:"name",errors:N,placeholder:"Name of the SMS campaign",register:j,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Assistant",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(b,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the assistant responsible for the SMS campaign."})]})]}),e.jsx(S,{type:"mapping",page:"add",name:"assistant_id",errors:N,placeholder:"Assistant",options:q.map(t=>t.id),mapping:q.reduce((t,s)=>(t[s.id]=s.assistant_name,t),{}),register:j,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Choose a number",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(b,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the phone number to be used for the SMS campaign."})]})]}),e.jsx(S,{type:"mapping",page:"add",name:"from_number_id",errors:N,placeholder:"Choose a number",options:B.map(t=>t.id),mapping:B.reduce((t,s)=>(t[s.id]=s.number,t),{}),register:j,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Language",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(b,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the language for the SMS campaign"})]})]}),e.jsx(S,{type:"mapping",page:"add",name:"language",errors:N,placeholder:"Select Language",options:Object.keys(ae),mapping:ae,register:j,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Start Date",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(b,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the start date for the SMS campaign."})]})]}),e.jsx(S,{type:"date",page:"add",name:"start_time",errors:N,placeholder:"Start Date",min:Ce(),onChange:ke,dateTime:!0,register:j,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["SMS window start time",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(b,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the start time for the SMS window."})]})]}),e.jsx(S,{type:"time",page:"add",name:"valid_time_opening",errors:N,label:"",onChange:Me,time:!0,placeholder:"SMS window start time",register:j,className:"bg-[#1d2937] placeholder:text-gray-300"}),F&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Local Time: ",F," (GMT: ",z,"; The SMS is sent according to GMT Time.)"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["SMS window end time",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(b,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Select the end time for the SMS window."})]})]}),e.jsx(S,{type:"time",page:"add",name:"valid_time_closing",errors:N,label:"",onChange:Fe,time:!0,placeholder:"SMS window end time",register:j,className:"bg-[#1d2937] placeholder:text-gray-300"}),E&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Local Time: ",E," (GMT: ",G,"; The SMS is sent according to GMT Time.)"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Days AI can send SMS",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(b,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Choose the specific days when the AI is allowed to send SMS messages."})]})]}),e.jsx(S,{type:"mapping",page:"add",name:"days",errors:N,label:"",placeholder:"Days AI can send SMS",options:Object.keys(J),mapping:J,register:j,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("p",{className:"mt-2 text-sm text-white",children:["Need a template?"," ",e.jsx("a",{href:"/contact_template.csv",download:!0,className:"text-blue-600 underline",children:"Download sample CSV format"})]}),e.jsxs("div",{className:"mt-5",children:[e.jsxs("label",{className:"mb-2 block cursor-pointer text-sm font-bold text-white",children:["Upload CSV"," ",e.jsx("span",{className:"text-sm font-normal text-white",children:"(Please ensure to include the 'phone' and 'name' columns, as they are required.)"})]}),e.jsx(ce,{onUploadAccepted:t=>{console.log("---------------------------"),console.log(t),console.log("---------------------------"),H(t),I(!0)},children:({getRootProps:t,acceptedFile:s,ProgressBar:a,getRemoveFileProps:n})=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("button",{type:"button",...t(),className:"flex h-[10.375rem] w-full cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-600 bg-transparent text-white ",children:s?e.jsxs("div",{className:"flex flex-col items-center font-bold",children:[e.jsx($,{className:"h-5 w-5"}),s.name]}):e.jsx("div",{className:"bg-transparent font-bold",children:"Select CSV File"})}),s?e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("button",{className:"focus:shadow-outline mt-6 rounded bg-transparent px-4 py-2 font-bold text-white focus:outline-none",type:"button",onClick:()=>I(!0),children:"Preview CSV"}),e.jsxs("button",{...n(),onClick:l=>{H(null),n().onClick(l)},type:"button",className:"mt-3 flex items-center gap-3",children:["Remove ",e.jsx(Ve,{className:"h-6 w-6"})]})]}):null]}),e.jsx(a,{})]})})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Upload Documents ",e.jsx("br",{}),"(Documents forming the companies knowledge base)",e.jsxs("span",{className:"group relative ml-1",children:[e.jsx(b,{className:"h-5 w-5 cursor-pointer text-white"}),e.jsx("div",{className:"absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block",children:"Upload Documents to build the Assistants knowledge base for the SMS conversation."})]})]}),e.jsx(Ne,{allow_preview:xe})]}),e.jsx(Oe,{type:"submit",loading:Y,disabled:Y,className:"focus:shadow-outline  mt-6 rounded bg-[#19b2f6]/80  px-4 py-2 font-bold text-white hover:bg-[#19b2f6]/60 focus:outline-none",children:"Submit"})]})]})})})})})]})}),e.jsx(f,{appear:!1,show:V&&w!==null,as:g.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[100]",onClose:()=>I(!1),children:[e.jsx(f.Child,{as:g.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4",children:e.jsx(f.Child,{as:g.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"mx-auto w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{className:"text-3xl font-medium",children:"Preview CSV"}),e.jsx("button",{type:"button",onClick:()=>I(!1),children:e.jsx(te,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-5 space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"h-6 w-6 bg-red-300"}),"Missing one or more required columns"]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"h-6 w-6 bg-yellow-300"}),"Missing one or more optional columns"]})]}),e.jsx("div",{className:"small-scroll mt-5 max-h-[60vh] overflow-x-auto overflow-y-auto",children:e.jsxs("table",{className:"w-full border",children:[e.jsx("thead",{className:"border-b bg-gray-50",children:e.jsx("tr",{children:A.headers.map(t=>{const s=R.includes(t);return e.jsxs("th",{className:"whitespace-nowrap px-4 py-2",children:[s?"*":""," ",t]},t)})})}),e.jsx("tbody",{children:A.data.map((t,s)=>{const a=A.headers.map(l=>t[l]).some(l=>l===""||l===void 0),n=R.map(l=>t[l]).some(l=>l===""||l===void 0);return e.jsx("tr",{className:`border-b ${n?"!bg-red-300":""} ${a?"bg-yellow-300":""}`,children:A.headers.map(l=>e.jsx("td",{className:"whitespace-nowrap px-4 py-2",children:t[l]},l))},s)})})]})})]})})})})]})}),e.jsx(f,{appear:!1,show:X,as:g.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[100]",onClose:()=>P(!1),children:[e.jsx(f.Child,{as:g.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4",children:e.jsx(f.Child,{as:g.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"mx-auto w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{className:"text-3xl font-medium",children:"Preview Document"}),e.jsx("button",{type:"button",onClick:()=>P(!1),children:e.jsx(te,{className:"h-6 w-6"})})]}),e.jsx(Te,{})]})})})})]})})]})};export{At as default};
