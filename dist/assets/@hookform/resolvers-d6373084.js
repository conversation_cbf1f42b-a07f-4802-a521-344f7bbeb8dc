import{g as l}from"../react-hook-form-47c010f8.js";var y=function(r,t,i){if(r&&"reportValidity"in r){var n=l(i,t);r.setCustomValidity(n&&n.message||""),r.reportValidity()}},p=function(r,t){var i=function(f){var e=t.fields[f];e&&e.ref&&"reportValidity"in e.ref?y(e.ref,f,r):e.refs&&e.refs.forEach(function(o){return y(o,f,r)})};for(var n in t.fields)i(n)},g=function(r){return r instanceof Date},m=function(r){return r==null},A=function(r){return typeof r=="object"},V=function(r){return!m(r)&&!Array.isArray(r)&&A(r)&&!g(r)},b=function(r){return/^\w*$/.test(r)},c=function(r,t,i){for(var n=-1,f=b(t)?[t]:function(d){return u=d.replace(/["|']|\]/g,"").split(/\.|\[/),Array.isArray(u)?u.filter(Boolean):[];var u}(t),e=f.length,o=e-1;++n<e;){var a=f[n],v=i;if(n!==o){var s=r[a];v=V(s)||Array.isArray(s)?s:isNaN(+f[n+1])?{}:[]}r[a]=v,r=r[a]}return r},N=function(r,t){t.shouldUseNativeValidation&&p(r,t);var i={};for(var n in r){var f=l(t.fields,n),e=Object.assign(r[n]||{},{ref:f&&f.ref});if(h(t.names||Object.keys(r),n)){var o=Object.assign({},l(i,n));c(o,"root",e),c(i,n,o)}else c(i,n,e)}return i},h=function(r,t){return r.some(function(i){return i.startsWith(t+".")})};export{p as n,N as u};
