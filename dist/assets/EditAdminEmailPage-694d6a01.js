import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as E,f as $,r as m,h as q}from"./vendor-2ae44a2e.js";import{u as C}from"./react-hook-form-47c010f8.js";import{o as G}from"./yup-5abd4662.js";import{c as I,a as c}from"./yup-5c93ed04.js";import{M as B,A as D,G as M,t as v,s as O}from"./index-b2ff2fa1.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let n=new B;const ce=()=>{var h,b,g,f,j,y;const S=I({subject:c().required(),html:c().required(),tag:c().required()}).required(),{dispatch:d}=E.useContext(D),{dispatch:u}=E.useContext(M),k=$(),[T,A]=m.useState(0),[p,P]=m.useState(""),{register:o,handleSubmit:F,setError:x,setValue:l,formState:{errors:a}}=C({resolver:G(S)}),r=q();m.useEffect(function(){u({type:"SETPATH",payload:{path:"email"}}),async function(){try{n.setTable("email");const t=await n.callRestAPI({id:Number(r==null?void 0:r.id)},"GET");t.error||(l("subject",t.model.subject),l("html",t.model.html),l("tag",t.model.tag),P(t.model.slug),A(t.model.id))}catch(t){console.log("error",t),v(d,t.message)}}()},[]);const R=async t=>{try{const s=await n.callRestAPI({id:T,slug:p,subject:t.subject,html:t.html,tag:t.tag},"PUT");if(!s.error)O(u,"Updated"),k("/admin/email");else if(s.validation){const w=Object.keys(s.validation);for(let i=0;i<w.length;i++){const N=w[i];x(N,{type:"manual",message:s.validation[N]})}}}catch(s){console.log("Error",s),x("html",{type:"manual",message:s.message}),v(d,s.message)}};return e.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Edit Email"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:F(R),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"slug",children:"Email Type"}),e.jsx("input",{type:"text",placeholder:"Email Type",value:p,readOnly:!0,className:"focus:shadow-outline} mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Subject"}),e.jsx("input",{type:"text",placeholder:"subject",...o("subject"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(h=a.subject)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(b=a.subject)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"tag",children:"Tags"}),e.jsx("input",{type:"text",placeholder:"tag",...o("tag"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(g=a.tag)!=null&&g.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=a.tag)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"html",children:"Email Body"}),e.jsx("textarea",{placeholder:"Email Body",className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(j=a.html)!=null&&j.message?"border-red-500":""}`,...o("html"),rows:15}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=a.html)==null?void 0:y.message})]}),e.jsx("button",{type:"submit",className:"focus:shadow-outline rounded bg-[#2cc9d5] px-4 py-2 font-bold text-white hover:bg-[#2cc9d5]/70 focus:outline-none",children:"Submit"})]})]})};export{ce as default};
