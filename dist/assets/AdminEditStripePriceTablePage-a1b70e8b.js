import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as n,f as F,r as a,h as G}from"./vendor-2ae44a2e.js";import{u as $}from"./react-hook-form-47c010f8.js";import{o as B}from"./yup-5abd4662.js";import{c as q,a as i}from"./yup-5c93ed04.js";import{M as H,A as K,G as V,t as z,s as J}from"./index-b2ff2fa1.js";import"./react-quill-d06fcfc9.js";import{M as l}from"./MkdInput-a584fac2.js";import{I as Q}from"./InteractiveButton-bff38983.js";import{S as W}from"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let c=new H;const Oe=()=>{var j,S;const{dispatch:N}=n.useContext(K),I=q({name:i(),product_id:i(),stripe_id:i(),is_usage_metered:i(),usage_limit:i(),object:i(),amount:i(),trial_days:i(),type:i(),status:i()}).required(),{dispatch:f}=n.useContext(V),[x,X]=n.useState({}),[_,g]=n.useState(!1),[w,b]=n.useState(!1),T=F(),[Y,E]=a.useState(""),[Z,P]=a.useState(0),[ee,v]=a.useState(""),[te,k]=a.useState(""),[se,A]=a.useState(0),[ae,U]=a.useState(""),[oe,D]=a.useState(0),[re,L]=a.useState(0),[ie,M]=a.useState(""),[le,O]=a.useState(""),{register:o,handleSubmit:R,setError:h,setValue:r,formState:{errors:s}}=$({resolver:B(I)}),y=G();a.useEffect(function(){(async function(){try{b(!0),c.setTable("stripe_price");const e=await c.callRestAPI({id:Number(y==null?void 0:y.id)},"GET");e.error||(r("name",e.model.name),r("product_id",e.model.product_id),r("stripe_id",e.model.stripe_id),r("is_usage_metered",e.model.is_usage_metered),r("usage_limit",e.model.usage_limit),r("object",e.model.object),r("amount",e.model.amount),r("trial_days",e.model.trial_days),r("type",e.model.type),r("status",e.model.status),E(e.model.name),P(e.model.product_id),v(e.model.stripe_id),k(e.model.is_usage_metered),A(e.model.usage_limit),U(e.model.object),D(e.model.amount),L(e.model.trial_days),M(e.model.type),O(e.model.status),setId(e.model.id),b(!1))}catch(e){b(!1),console.log("error",e),z(N,e.message)}})()},[]);const C=async e=>{g(!0);try{c.setTable("stripe_price");for(let p in x){let d=new FormData;d.append("file",x[p].file);let u=await c.uploadImage(d);e[p]=u.url}const m=await c.callRestAPI({id,name:e.name,product_id:e.product_id,stripe_id:e.stripe_id,is_usage_metered:e.is_usage_metered,usage_limit:e.usage_limit,object:e.object,amount:e.amount,trial_days:e.trial_days,type:e.type,status:e.status},"PUT");if(!m.error)J(f,"Updated"),T("/admin/stripe_price");else if(m.validation){const p=Object.keys(m.validation);for(let d=0;d<p.length;d++){const u=p[d];h(u,{type:"manual",message:m.validation[u]})}}g(!1)}catch(m){g(!1),console.log("Error",m),h("name",{type:"manual",message:m.message})}};return n.useEffect(()=>{f({type:"SETPATH",payload:{path:"stripe_price"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Stripe Price"}),w?t.jsx(W,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:R(C),children:[t.jsx(l,{type:"text",page:"edit",name:"name",errors:s,label:"Name",placeholder:"Name",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"product_id",errors:s,label:"Product Id",placeholder:"Product Id",register:o,className:""}),t.jsx(l,{type:"text",page:"edit",name:"stripe_id",errors:s,label:"Stripe Id",placeholder:"Stripe Id",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"is_usage_metered",errors:s,label:"Is Usage Metered",placeholder:"Is Usage Metered",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"usage_limit",errors:s,label:"Usage Limit",placeholder:"Usage Limit",register:o,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"object",children:"Object"}),t.jsx("textarea",{placeholder:"Object",...o("object"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(j=s.object)!=null&&j.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(S=s.object)==null?void 0:S.message})]}),t.jsx(l,{type:"number",page:"edit",name:"amount",errors:s,label:"Amount",placeholder:"Amount",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"trial_days",errors:s,label:"Trial Days",placeholder:"Trial Days",register:o,className:""}),t.jsx(l,{type:"text",page:"edit",name:"type",errors:s,label:"Type",placeholder:"Type",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"status",errors:s,label:"Status",placeholder:"Status",register:o,className:""}),t.jsx(Q,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:_,disable:_,children:"Submit"})]})]})};export{Oe as default};
