import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as s}from"./vendor-2ae44a2e.js";import{M as N,A as S,G as w,s as x,t as p}from"./index-b2ff2fa1.js";import{u as k}from"./react-hook-form-47c010f8.js";import{o as A}from"./yup-5abd4662.js";import{c as E,d as m}from"./yup-5c93ed04.js";import{S as C}from"./index-a74110af.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";let a=new N;const M=E({pricing:m().required("Pricing is required"),max_active_outbound:m().required("Max active outbound is required"),max_retry:m().required("Max retry is required")}),ee=()=>{const{dispatch:u}=s.useContext(S),{dispatch:c}=s.useContext(w),[g,b]=s.useState(1),[n,o]=s.useState(!1),{register:l,handleSubmit:f,setValue:d,formState:{errors:t}}=k({resolver:A(M)}),h=async i=>{try{o(!0),a.setTable("setting"),await a.callRestAPI({id:g,pricing:i.pricing,max_active_outbound:i.max_active_outbound,max_retry:i.max_retry},"PUT"),x(c,"Settings updated successfully",4e3,"success")}catch(r){console.error(r),x(c,"Failed to update settings",4e3,"error"),p(u,r.message)}finally{o(!1)}};return s.useEffect(()=>{c({type:"SETPATH",payload:{path:"settings"}}),(async()=>{try{o(!0),a.setTable("setting");const r=await a.callRestAPI({},"GETALL");if(!r.error&&r.list.length>0){const{pricing:y,max_active_outbound:j,max_retry:_,id:v}=r.list[0];d("pricing",y),d("max_active_outbound",j),d("max_retry",_),b(v||8)}}catch(r){console.error("Error",r),p(u,r.message)}finally{o(!1)}})()},[]),e.jsx("div",{className:"px-8 py-6 min-h-screen bg-white",children:n?e.jsx(C,{}):e.jsxs("form",{onSubmit:f(h),className:"space-y-8",children:[e.jsxs("div",{className:"p-6 bg-white rounded-lg border border-gray-200 shadow-sm",children:[e.jsx("h2",{className:"mb-6 text-2xl font-semibold text-gray-800",children:"General Settings"}),e.jsxs("div",{className:"grid gap-6 text-black md:grid-cols-2 lg:grid-cols-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Pricing per minute ($)"}),e.jsx("input",{type:"number",step:"0.01",...l("pricing"),className:`w-full rounded-md border ${t.pricing?"border-red-500":"border-gray-300"} px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500`,placeholder:"0.00"}),t.pricing&&e.jsx("p",{className:"text-sm text-red-500",children:t.pricing.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Max Active Outbounds per User"}),e.jsx("input",{type:"number",...l("max_active_outbound"),className:`w-full rounded-md border ${t.max_active_outbound?"border-red-500":"border-gray-300"} px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500`,placeholder:"Enter max active outbounds"}),t.max_active_outbound&&e.jsx("p",{className:"text-sm text-red-500",children:t.max_active_outbound.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Max Retries for Missed Calls"}),e.jsx("input",{type:"number",...l("max_retry"),className:`w-full rounded-md border ${t.max_retry?"border-red-500":"border-gray-300"} px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500`,placeholder:"Enter max retries"}),t.max_retry&&e.jsx("p",{className:"text-sm text-red-500",children:t.max_retry.message})]})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{type:"submit",disabled:n,className:"inline-flex items-center rounded-md bg-[#2cc9d5] px-6 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-[#28b8c3] focus:outline-none focus:ring-2 focus:ring-[#2cc9d5] focus:ring-offset-2 disabled:opacity-60",children:n?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"mr-2 w-4 h-4 animate-spin",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):"Update Settings"})})]})})};export{ee as default};
