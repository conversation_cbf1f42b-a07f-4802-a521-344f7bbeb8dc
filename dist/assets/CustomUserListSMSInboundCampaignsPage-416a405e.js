import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as r,i as Z,r as ee,L as te}from"./vendor-2ae44a2e.js";import{M as se,A as ae,G as re,T as ie,t as A,s as h,B as ne,a as le,b as oe,l as de,R as ce,c as ue,m as pe,n as me,P as xe,y as he}from"./index-b2ff2fa1.js";import{A as ge}from"./index-e429b426.js";import{C as be}from"./CustomDeleteModal-536cae45.js";import{u as fe}from"./react-hook-form-47c010f8.js";import{X as je}from"./lucide-react-1246a7ed.js";import{C as j,q as ye}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let y=new se;const B={0:"Inactive",1:"Active",2:"Paused"},L={inactive:0,active:1,paused:2,INACTIVE:0,ACTIVE:1,PAUSED:2,Inactive:0,Active:1,Paused:2},P=[{header:"Action",accessor:""},{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Assistant name",accessor:"assistant_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Contacts",accessor:"called",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:B}],U=P.filter(w=>!["","assistant_name","called"].includes(w.accessor)),O={INACTIVE:0,ACTIVE:1,PAUSED:2},$e=()=>{const{state:w,dispatch:g}=r.useContext(ae),{dispatch:d}=r.useContext(re),[$,D]=r.useState(!1),[l,b]=r.useState([]),[z,u]=r.useState([]),[v,E]=r.useState(""),[T,I]=r.useState(!1),[f,c]=r.useState(!1),[k,G]=r.useState([]),[K,S]=r.useState(!1),[_,M]=r.useState(null),[F,we]=Z(),[W,H]=r.useState({currentPage:0,pageSize:0,totalNumber:0,totalPages:0});fe({defaultValues:{}});const[V,p]=r.useState({}),[N,X]=r.useState("eq");r.useEffect(()=>{d({type:"SETPATH",payload:{path:"sms_inbound_campaigns"}}),x()},[z,v]);const m=(t,a,i)=>{if(!i){u(o=>o.filter(q=>!q.startsWith(t+",")));return}let s=i,n=a||"eq";if(t==="status"){const o=i.toLowerCase();L.hasOwnProperty(o)&&(s=L[o])}else n==="eq"?s=i:s=i.toLowerCase();const C=`${t},${n},${s}`;u(o=>[...o.filter(Y=>!Y.startsWith(t+",")),C])};async function x(){try{c(!0);const t=new ie,a=await t.getPaginate("campaign",{size:F.get("limit")??50,page:F.get("page")??1,filter:["campaign_type,eq,4",`${t.getProjectId()}_campaign.user_id,eq,${w.user}`],join:"assistants|assistant_id"}),{list:i,total:s,limit:n,num_pages:C,page:o}=a;G(i),H({currentPage:o,pageSize:n,totalNumber:s,totalPages:C})}catch(t){console.log("ERROR",t),A(g,t.message),h(d,t.message,5e3,"error")}c(!1)}async function R(t,a){c(!0);try{y.setTable("campaign"),await y.callRestAPI({status:a,id:t},"PUT"),h(d,"Updated"),x()}catch(i){A(g,i.message),h(d,i.message,5e3,"error")}c(!1)}async function J(t){c(!0);try{y.setTable("campaign"),await y.callRestAPI({id:t},"DELETE"),h(d,"Deleted"),x()}catch(a){A(g,a.message),h(d,a.message,5e3,"error")}c(!1)}const Q=()=>{b([]),u([]),p({}),x()};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5",children:[e.jsx("h3",{className:"my-4 px-8 text-2xl font-bold text-white",children:"SMS Inbound Campaigns"}),e.jsxs("div",{className:"bg-[#1d2937] px-8 py-4",children:[e.jsxs("div",{className:"flex h-fit items-center justify-between gap-3",children:[e.jsx("div",{className:"flex w-[200px] min-w-[200px] items-center justify-between",children:e.jsx("div",{className:"relative z-10 rounded bg-[#1d2937]",children:e.jsxs(j,{children:[e.jsxs("div",{className:"flex items-center gap-4 bg-[#1d2937] text-white",children:[e.jsxs(j.Button,{className:"flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white focus-visible:outline-0 focus-visible:outline-transparent",children:[e.jsx(ne,{}),e.jsx("span",{children:"Filters"}),l.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:l.length})]}),e.jsxs("div",{className:"focus-within:border-gray-40 flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white",children:[e.jsx(le,{className:"text-xl text-white"}),e.jsx("input",{type:"text",placeholder:"search by name",className:"border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none",style:{boxShadow:"0 0 transparent"},value:v,onChange:t=>{E(t.target.value),m("name","cs",t.target.value)}}),v&&e.jsx(oe,{className:"cursor-pointer text-lg text-white",onClick:()=>{E(""),m("name","cs","")}})]})]}),e.jsx(ye,{as:ee.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(j.Panel,{children:e.jsxs("div",{className:"filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10",children:[e.jsx("span",{className:"absolute left-5 top-2 font-medium text-white",children:"Filters"}),e.jsx(j.Button,{onClick:()=>{console.log("clicked"),b([]),u([]),p({})},children:e.jsx(je,{className:"absolute right-2 top-2 cursor-pointer text-white"})}),l==null?void 0:l.map((t,a)=>{var i;return e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("button",{type:"button",className:"block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none",title:t,style:{WebkitTouchCallout:"none"},children:((i=U.find(s=>s.accessor===t))==null?void 0:i.header)||t}),e.jsxs("select",{className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none",value:N,onChange:s=>{X(s.target.value),m(t,s.target.value,V[t])},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),t==="status"?e.jsxs("select",{value:V[t]||"",onChange:s=>{p(n=>({...n,[t]:s.target.value})),m(t,N,s.target.value)},className:"h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",children:[e.jsx("option",{value:"",children:"Select Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]}):e.jsx(de,{type:"text",labelClassName:"!mb-0",placeholder:"Enter value",setValue:()=>{},showIcon:!1,className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",onReady:s=>m(t,N,s)}),e.jsx(ce,{className:"cursor-pointer text-2xl text-red-600",onClick:()=>{b(s=>s.filter(n=>n!==t)),u(s=>s.filter(n=>!n.includes(t))),p(s=>{const n={...s};return delete n[t],n})}})]},a)}),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>I(!T),children:[e.jsx(ue,{}),"Add filter"]}),T&&e.jsx("div",{className:"absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:U.map(t=>e.jsx("li",{className:`${l.includes(t.accessor)?"cursor-not-allowed text-gray-100":"cursor-pointer text-gray-400 hover:text-white"}`,onClick:()=>{l.includes(t.accessor)||(b(a=>[...a,t.accessor]),p(a=>({...a,[t.accessor]:""}))),I(!1)},children:t.header},t.accessor))})}),l.length>0&&e.jsx("div",{onClick:Q,className:"inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white",children:"Clear all filter"})]})]})})})]})})}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsx(ge,{onClick:()=>{D(!0)},showChildren:!0,className:"relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]",children:"Add New"})})]}),e.jsx("div",{className:"mt-4 overflow-x-auto  bg-[#1d2937]",children:e.jsx("div",{className:f?"":"overflow-x-auto border-b border-gray-200 shadow",children:f&&k.length===0?e.jsx(pe,{columns:P}):e.jsxs("table",{className:"min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]",children:[e.jsx("thead",{className:"bg-[#1d2937]",children:e.jsx(me,{actionPosition:"onTable",onSort:()=>{},columns:P,actions:{view:{show:!0}}})}),e.jsx("tbody",{className:"divide-y divide-gray-400 bg-[#1d2937]",children:k.map(t=>{var a;return e.jsxs("tr",{className:"text-white",children:[e.jsx("td",{className:"pl-3",children:e.jsxs("div",{className:"flex max-w-[260px] items-center justify-between gap-3 text-sm",children:[t.status===1?e.jsx("button",{title:"Pause Campaign",className:"rounded-[30px] bg-orange-500/20 p-1.5 px-5 font-medium text-orange-500 transition-colors hover:bg-orange-500/30",disabled:f,onClick:()=>R(t.id,O.PAUSED),children:"Pause"}):null,t.status!==1?e.jsx("button",{title:"Start Campaign",className:"rounded-[30px] bg-green-500/20 p-1.5 px-5 font-medium text-green-500 transition-colors hover:bg-green-500/30",disabled:f,onClick:()=>R(t.id,O.ACTIVE),children:"Start"}):null,e.jsx("button",{title:"Delete Campaign",className:"rounded-[30px] bg-red-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-red-500/30",onClick:()=>{S(!0),M(t.id)},children:"Delete"}),e.jsx(te,{to:`/user/sms_inbound_logs?campaign_id=${t.id}`,children:e.jsx("button",{title:"View SMS Logs",className:"rounded-[30px] bg-blue-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-blue-500/30",type:"button",children:"View"})})]})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.id}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.name}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:((a=t.assistants)==null?void 0:a.assistant_name)??"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.contacts??"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:B[t.status]??"N/A"})]},t.id)})})]})})}),e.jsx(xe,{paginationData:W})]})]}),e.jsx(he,{closeSidebar:()=>{D(!1),x()},isOpen:$}),e.jsx(be,{isOpen:K&&!!_,closeModal:()=>S(!1),onDelete:async()=>{await J(_),M(null),S(!1)}})]})};export{$e as default};
