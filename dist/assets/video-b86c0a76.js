import{j as n}from"./@react-google-maps/api-c55ecefa.js";import{r as i}from"./vendor-2ae44a2e.js";import{T as C}from"./twilio-video-59a889f9.js";import I from"./videoItem-9f768090.js";function O(){var f,b;const[o,k]=i.useState(null),[m,x]=i.useState("room"),[B,j]=i.useState("room"),[p,u]=i.useState(null),[y,g]=i.useState(null),[v,l]=i.useState("");i.useEffect(()=>{const t=c=>(c.preventDefault(),console.log(o),console.log("beforeunload event triggered"),document.getElementById("local-participant")&&(document.getElementById("local-participant").style.display="none"),document.getElementById("remote-participant")&&(document.getElementById("remote-participant").style.display="none"),document.getElementById("leave-btn")&&(document.getElementById("leave-btn").style.display="none"),o&&o.disconnect());return window.addEventListener("beforeunload",t),()=>{o&&o.disconnect(),window.removeEventListener("beforeunload",t)}},[o]);const S=async()=>{try{let t=function(e,s){document.getElementById("remote-participant")&&(document.getElementById("remote-participant").style.display="none")},c=function(e,s){e.track&&(track.kind==="video"?g(e.track):u(e.track)),e.on("subscribed",r=>{r.kind==="video"?g(e.track):u(e.track),document.getElementById("local-participant")&&(document.getElementById("local-participant").style.display="block"),document.getElementById("remote-participant")&&(document.getElementById("remote-participant").style.display="block"),document.getElementById("leave-btn")&&(document.getElementById("leave-btn").style.display="block")})},E=function(e,s){e.tracks.forEach(r=>{c(r,e)}),e.on("trackPublished",r=>{c(r,e)})};l("");const w=await(await fetch("http://localhost:3048/v2/api/lambda/video/token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({identity:B+Math.random().toFixed(2),room:m})})).json(),d={bandwidthProfile:{video:{dominantSpeakerPriority:"high",mode:"collaboration",clientTrackSwitchOffControl:"auto",contentPreferencesMode:"auto"}},dominantSpeaker:!0,maxAudioBitrate:16e3,preferredVideoCodecs:[{codec:"VP8",simulcast:!0}],video:{height:720,frameRate:24,width:1280}},h=await navigator.mediaDevices.enumerateDevices(),N=h.filter(e=>e.kind==="audioinput"),P=h.filter(e=>e.kind==="videoinput");d.audio={deviceId:{exact:N.deviceId}},d.name=m,d.video.deviceId={exact:P.deviceId};const a=await C.connect(w.data,d);k(a),document.getElementById("local-participant")&&(document.getElementById("local-participant").style.display="block"),document.getElementById("leave-btn")&&(document.getElementById("leave-btn").style.display="block"),a.participants.forEach(e=>{E(e,a)}),a.on("participantConnected",e=>{E(e,a)}),a.on("participantDisconnected",e=>{t(e,a)}),a.once("disconnected",(e,s)=>{t()})}catch(t){t.message==="Room contains too many Participants"?l("Room contains too many Participants."):l("Something went wrong."),document.getElementById("start-btn")&&(document.getElementById("start-btn").style.display="block"),console.error(t)}};return n.jsxs(n.Fragment,{children:[n.jsxs("div",{className:"flex gap-10 mb-10 justify-center",children:[n.jsx("input",{type:"text",placeholder:"User Name",onChange:t=>{j(t.target.value)}}),n.jsx("input",{type:"text",placeholder:"Room Name",onChange:t=>{x(t.target.value)}}),n.jsx("button",{className:"btn bg-green-400 font-bold",id:"start-btn",onClick:t=>{t.target.style.display="none",S()},children:"Start"})]}),v&&n.jsx("div",{className:"text-red-400 text-center text-2xl js__error",children:v+" Please try again later!"}),o&&n.jsxs("div",{className:"flex flex-col w-1/2 gap-3 mx-auto",children:[n.jsx("div",{id:"local-participant",children:n.jsx(I,{muted:!0,videoStream:(f=Array.from(o.localParticipant.videoTracks.values())[0])==null?void 0:f.track,audioStream:(b=Array.from(o.localParticipant.audioTracks.values())[0])==null?void 0:b.track})}),p&&y&&n.jsx("div",{id:"remote-participant",children:n.jsx(I,{muted:!1,videoStream:y,audioStream:p})}),n.jsx("button",{id:"leave-btn",className:"btn bg-red-400 font-bold",onClick:t=>{o.disconnect(),document.getElementById("local-participant")&&(document.getElementById("local-participant").style.display="none"),document.getElementById("remote-participant")&&(document.getElementById("remote-participant").style.display="none"),t.target.style.display="none",document.getElementById("start-btn").style.display="block"},children:"Leave"})]})]})}export{O as default};
