import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as a,f as q}from"./vendor-2ae44a2e.js";import{M as J,G as Q,A as W,t as X,g as l}from"./index-b2ff2fa1.js";import{o as Z}from"./yup-5abd4662.js";import{u as ee}from"./react-hook-form-47c010f8.js";import{c as se,a as d}from"./yup-5c93ed04.js";import{P as te}from"./index-132fbad2.js";import"./index-e429b426.js";import{S as ae}from"./index-a74110af.js";import"./index-9aa09a5c.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";let ie=new J;const P=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Stripe Id",accessor:"stripe_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Price Id",accessor:"price_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User Id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Object",accessor:"object",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Is Lifetime",accessor:"is_lifetime",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],Re=()=>{var S,N,v,k;const{dispatch:_}=a.useContext(Q),{dispatch:D}=a.useContext(W);a.useState("");const[b,A]=a.useState([]),[o,y]=a.useState(10),[j,C]=a.useState(0),[re,I]=a.useState(0),[p,F]=a.useState(0),[R,T]=a.useState(!1),[z,L]=a.useState(!1),[u,w]=a.useState(!1),[oe,O]=a.useState(!1);a.useState(!1);const[ne,V]=a.useState();q();const G=se({stripe_id:d(),name:d(),status:d(),product_name:d(),amount:d(),type:d()}),{register:n,handleSubmit:M,formState:{errors:m}}=ee({resolver:Z(G)}),U=[{key:"",value:"All"},{key:0,value:"Archived"},{key:1,value:"Active"}],$=[{key:"",value:"All"},{key:"one_time",value:"One time"},{key:"recurring",value:"Recurring"}];function B(s){(async function(){y(s),await c(1,s)})()}function H(){(async function(){await c(p-1>1?p-1:1,o)})()}function K(){(async function(){await c(p+1<=j?p+1:1,o)})()}async function c(s,r,t){try{w(!0);const i=await ie.getStripeSubscriptions({page:s,limit:r},t),{list:x,total:h,limit:g,num_pages:E,page:f}=i;A(x),y(+g),C(+E),F(+f),I(+h),T(+f>1),L(+f+1<=+E)}catch(i){console.log("ERROR",i),X(D,i.message)}w(!1)}const Y=s=>{const r=l(s.stripe_id),t=l(s.product_name),i=l(s.name),x=l(s.amount),h=l(s.type),g=l(s.status);c(1,o,{stripe_id:r,product_name:t,name:i,amount:x,type:h,status:g})};return a.useEffect(()=>{_({type:"SETPATH",payload:{path:"prices"}}),async function(){await c(1,o)}()},[]),e.jsxs("div",{className:"px-8",children:[e.jsx("div",{className:"flex items-center justify-between py-3 text-black",children:e.jsxs("form",{className:"mb-10 rounded bg-white p-5 shadow",onSubmit:M(Y),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsxs("div",{className:"filter-form-holder mt-10 flex flex-wrap",children:[e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Stripe Id"}),e.jsx("input",{type:"text",placeholder:"Stripe Id",...n("stripe_id"),className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(S=m.stripe_id)==null?void 0:S.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Product"}),e.jsx("input",{type:"text",placeholder:"Product Name",...n("product_name"),className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(N=m.product_name)==null?void 0:N.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...n("name"),className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(v=m.name)==null?void 0:v.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Amount"}),e.jsx("input",{type:"number",placeholder:"Amount",...n("amount"),className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(k=m.amount)==null?void 0:k.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...n("type"),children:$.map(s=>e.jsx("option",{value:s.key,defaultValue:"",children:s.value},s.key))}),e.jsx("p",{className:"text-xs italic text-red-500"})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...n("status"),children:U.map(s=>e.jsx("option",{value:s.key,defaultValue:"",children:s.value},s.key))}),e.jsx("p",{className:"text-xs italic text-red-500"})]})]}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block rounded bg-[#2cc9d5] px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-[#2cc9d5]/70 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>c(1,o),className:"inline-block rounded bg-red-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-500 hover:shadow-lg focus:bg-[red]/70 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-[red]/70 active:shadow-lg",children:"Reset"})]})]})}),u?e.jsx(ae,{}):e.jsxs("div",{className:"overflow-x-auto border-b border-gray-200 shadow",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 text-black",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:P.map((s,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[s.header,e.jsx("span",{children:s.isSorted?s.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:b.map((s,r)=>e.jsx("tr",{children:P.map((t,i)=>t.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"text-[black]",onClick:()=>{V(s.id),O(!0)},children:[" ","Edit"]})},i):t.mapping&&t.accessor==="status"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm",children:s[t.accessor]===1?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:t.mapping[s[t.accessor]]}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:t.mapping[s[t.accessor]]})},i):t.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.mapping[s[t.accessor]]},i):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[t.accessor]},i))},r))})]}),u&&e.jsx(e.Fragment,{children:e.jsx("p",{className:"px-10 py-3 text-xl capitalize",children:"Loading..."})}),!u&&b.length===0&&e.jsx(e.Fragment,{children:e.jsx("p",{className:"px-10 py-3 text-xl capitalize",children:"You Don't have any Subscriptions"})})]}),e.jsx(te,{currentPage:p,pageCount:j,pageSize:o,canPreviousPage:R,canNextPage:z,updatePageSize:B,previousPage:H,nextPage:K})]})};export{Re as default};
