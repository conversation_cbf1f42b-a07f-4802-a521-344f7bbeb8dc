import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as o,f as _}from"./vendor-2ae44a2e.js";import{u as v}from"./react-hook-form-47c010f8.js";import{o as k}from"./yup-5abd4662.js";import{c as E,a as l}from"./yup-5c93ed04.js";import{G as j,M as O,s as P,t as A}from"./index-b2ff2fa1.js";import"./react-quill-d06fcfc9.js";import{M as p}from"./MkdInput-a584fac2.js";import{I as R}from"./InteractiveButton-bff38983.js";import"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";const pe=()=>{var h,g;const{dispatch:u}=o.useContext(j),S=E({stripe_id:l(),price_id:l(),user_id:l(),object:l(),status:l(),is_lifetime:l()}).required(),{dispatch:y}=o.useContext(j),[b,T]=o.useState({}),[f,c]=o.useState(!1),w=_(),{register:a,handleSubmit:I,setError:x,setValue:D,formState:{errors:s}}=v({resolver:k(S)});o.useState([]);const N=async i=>{let n=new O;c(!0);try{for(let m in b){let r=new FormData;r.append("file",b[m].file);let d=await n.uploadImage(r);i[m]=d.url}n.setTable("stripe_subscription");const t=await n.callRestAPI({stripe_id:i.stripe_id,price_id:i.price_id,user_id:i.user_id,object:i.object,status:i.status,is_lifetime:i.is_lifetime},"POST");if(!t.error)P(u,"Added"),w("/admin/stripe_subscription");else if(t.validation){const m=Object.keys(t.validation);for(let r=0;r<m.length;r++){const d=m[r];x(d,{type:"manual",message:t.validation[d]})}}c(!1)}catch(t){c(!1),console.log("Error",t),x("stripe_id",{type:"manual",message:t.message}),A(y,t.message)}};return o.useEffect(()=>{u({type:"SETPATH",payload:{path:"stripe_subscription"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Stripe Subscription"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:I(N),children:[e.jsx(p,{type:"text",page:"add",name:"stripe_id",errors:s,label:"Stripe Id",placeholder:"Stripe Id",register:a,className:""}),e.jsx(p,{type:"text",page:"add",name:"price_id",errors:s,label:"Price Id",placeholder:"Price Id",register:a,className:""}),e.jsx(p,{type:"number",page:"add",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:a,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"object",children:"Object"}),e.jsx("textarea",{placeholder:"Object",...a("object"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(h=s.object)!=null&&h.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(g=s.object)==null?void 0:g.message})]}),e.jsx(p,{type:"text",page:"add",name:"status",errors:s,label:"Status",placeholder:"Status",register:a,className:""}),e.jsx(p,{type:"number",page:"add",name:"is_lifetime",errors:s,label:"Is Lifetime",placeholder:"Is Lifetime",register:a,className:""}),e.jsx(R,{type:"submit",loading:f,disabled:f,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{pe as default};
