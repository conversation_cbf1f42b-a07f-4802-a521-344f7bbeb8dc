import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as n,f as A,h as L,r as y}from"./vendor-2ae44a2e.js";import{u as M}from"./react-hook-form-47c010f8.js";import{o as $}from"./yup-5abd4662.js";import{c as q,a as R,b as T}from"./yup-5c93ed04.js";import{M as D,A as F,G,s as w,t as k}from"./index-b2ff2fa1.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let v=new D;const ne=({activeId:r,setSidebar:o})=>{var h,b;const E=q({name:R().required(),status:T().required()}).required(),{dispatch:c}=n.useContext(F),{dispatch:i}=n.useContext(G);A(),L();const[H,N]=y.useState(0),[m,a]=y.useState(!1),{register:d,handleSubmit:p,setError:S,setValue:u,formState:{errors:x}}=M({resolver:$(E)}),C=[{key:"0",value:"Inactive"},{key:"1",value:"Active"}],f=async t=>{a(!0);try{const s=await v.updateStripePrice(r,t);if(!s.error)w(i,"Edited",4e3),a(!1),closeSidebar&&closeSidebar();else{if(s.validation){const g=Object.keys(s.validation);for(let l=0;l<g.length;l++){const j=g[l];S(j,{type:"manual",message:s.validation[j]})}}a(!1)}}catch(s){console.log("Error",s),w(i,s.message,4e3),k(c,s.message)}a(!1)};async function P(){try{const t=await v.getStripePrice(r);if(!t.error){const s=t.model.object;u("name",s.nickname),u("status",t.model.status),N(t.model.id)}}catch(t){console.log("Error",t),k(c,t.message)}}return n.useEffect(()=>{i({type:"SETPATH",payload:{path:"prices"}}),P()},[r]),e.jsxs("div",{className:"mx-auto   rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>o(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Edit Plan"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]",onClick:()=>o(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(f)(),o(!1)},disabled:m,children:m?"Saving":"Save"})]})]}),e.jsxs("form",{className:"w-full max-w-lg p-4 text-left",onSubmit:p(f),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",...d("name"),className:`"appearance-none focus:shadow-outline w-full rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${(h=x.name)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(b=x.name)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3  w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none",...d("status"),children:C.map(t=>e.jsx("option",{value:t.key,children:t.value},t.key))})]})]})]})};export{ne as default};
