import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as i,i as H,r as X}from"./vendor-2ae44a2e.js";import{M as J,A as Q,G as Y,T as Z,t as ee,s as te,B as se,a as ae,b as re,l as ie,R as ne,c as le,m as oe,n as ce,P as de}from"./index-b2ff2fa1.js";import{C as ue}from"./react-papaparse-b60a38ab.js";import{u as xe}from"./react-hook-form-47c010f8.js";import{X as pe}from"./lucide-react-1246a7ed.js";import{C as g,q as he}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./papaparse-2d1475f9.js";new J;const V={0:"Inactive",1:"Active"},I={inactive:0,active:1,INACTIVE:0,ACTIVE:1,Inactive:0,Active:1},N=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Number",accessor:"number",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:V}],R=N.filter(S=>![""].includes(S.accessor)),Re=()=>{const{state:S,dispatch:C}=i.useContext(Q),{dispatch:P}=i.useContext(Y),[c,$]=H(),[l,h]=i.useState([]),[b,d]=i.useState([]),[u,T]=i.useState(""),[f,A]=i.useState(!1),[k,D]=i.useState(!1),[y,q]=i.useState([]),{CSVDownloader:B}=ue();console.log(f);const[O,z]=i.useState({currentPage:0,pageSize:0,totalNumber:0,totalPages:0});xe({defaultValues:{}});const[E,x]=i.useState({}),[j,L]=i.useState("eq");i.useEffect(()=>{P({type:"SETPATH",payload:{path:"numbers"}}),F()},[b,u]);const p=(t,n,a)=>{if(!a){d(o=>o.filter(m=>!m.startsWith(t+",")));return}let s=a,r=n||"eq";if(t==="status"){const o=a.toLowerCase();I.hasOwnProperty(o)&&(s=I[o])}else r==="eq"?s=a:s=a.toLowerCase();const w=`${t},${r},${s}`;d(o=>[...o.filter(v=>!v.startsWith(t+",")),w])};async function F(){try{D(!0);const t=new Z,n=`${t.getProjectId()}_numbers`;let a=[];if(u&&a.push(`${n}.number,cs,${u}`),b.length>0){const _=b.map(G=>{const[K,U,W]=G.split(",");return`${n}.${K},${U},${W}`});a=[...a,..._]}const s=await t.getPaginate("numbers",{size:c.get("limit")??50,page:c.get("page")??1,filter:a}),{list:r,total:w,limit:o,num_pages:m,page:v}=s;q(r),z({currentPage:v,pageSize:o,totalNumber:w,totalPages:m})}catch(t){console.log("ERROR",t),ee(C,t.message),te(P,t.message,5e3,"error")}D(!1)}const M=()=>{h([]),d([]),x({});for(const t of c.keys())c.delete(t);$(c),F()};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5",children:[e.jsx("h3",{className:"px-8 my-4 text-2xl font-bold text-white",children:"Phone Numbers"}),e.jsxs("div",{className:"bg-[#1d2937] px-8 py-4",children:[e.jsxs("div",{className:"flex gap-3 justify-between items-center h-fit",children:[e.jsx("div",{className:"flex w-[200px] min-w-[200px] items-center justify-between",children:e.jsx("div",{className:"relative z-10 rounded bg-[#1d2937]",children:e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4 bg-[#1d2937] text-white",children:[e.jsxs(g.Button,{className:"flex gap-3 justify-between items-center px-3 py-1 text-white bg-transparent rounded-md border cursor-pointer border-white/50ss border-white/50 focus-visible:outline-0 focus-visible:outline-transparent",children:[e.jsx(se,{}),e.jsx("span",{children:"Filters"}),l.length>0&&e.jsx("span",{className:"flex justify-center items-center w-6 h-6 text-white bg-gray-800 rounded-full text-start",children:l.length})]}),e.jsxs("div",{className:"flex gap-3 justify-between items-center px-2 py-1 text-white bg-transparent rounded-md border cursor-pointer border-white/50 focus-within:border-gray-400",children:[e.jsx(ae,{className:"text-xl text-white"}),e.jsx("input",{type:"text",placeholder:"Search by number",className:"p-0 text-white bg-transparent border-none placeholder:text-left placeholder:text-gray-300 focus:outline-none",style:{boxShadow:"0 0 transparent"},value:u,onChange:t=>{T(t.target.value),p("number","cs",t.target.value)}}),u&&e.jsx(re,{className:"text-lg text-white cursor-pointer",onClick:()=>{T(""),p("number","cs","")}})]})]}),e.jsx(he,{as:X.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(g.Panel,{children:e.jsxs("div",{className:"filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10",children:[e.jsx("span",{className:"absolute top-2 left-5 font-medium text-white",children:"Filters"}),e.jsx(g.Button,{onClick:()=>{console.log("clicked"),h([]),d([]),x({})},children:e.jsx(pe,{className:"absolute top-2 right-2 text-white cursor-pointer"})}),l==null?void 0:l.map((t,n)=>{var a;return e.jsxs("div",{className:"flex gap-3 justify-between items-center mb-2 w-full text-gray-600",children:[e.jsx("button",{type:"button",className:"block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none",title:t,style:{WebkitTouchCallout:"none"},children:((a=R.find(s=>s.accessor===t))==null?void 0:a.header)||t}),e.jsxs("select",{className:"flex h-[40px] w-1/3 appearance-none items-center justify-center rounded-md border-none bg-gray-100 !py-[8px] capitalize text-gray-600 outline-0",value:j,onChange:s=>{L(s.target.value),p(t,s.target.value,E[t])},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),t==="status"?e.jsxs("select",{value:E[t]||"",onChange:s=>{x(r=>({...r,[t]:s.target.value})),p(t,j,s.target.value)},className:"h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",children:[e.jsx("option",{value:"",children:"Select Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]}):e.jsx(ie,{type:"text",labelClassName:"!mb-0",placeholder:"Enter value",setValue:()=>{},showIcon:!1,className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",onReady:s=>p(t,j,s)}),e.jsx(ne,{className:"text-2xl text-red-600 cursor-pointer",onClick:()=>{h(s=>s.filter(r=>r!==t)),d(s=>s.filter(r=>!r.includes(t))),x(s=>{const r={...s};return delete r[t],r})}})]},n)}),e.jsxs("div",{className:"flex relative justify-between items-center font-semibold search-buttons",children:[e.jsxs("div",{className:"mr-2 flex h-[40px] w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>A(!f),children:[e.jsx(le,{}),"Add filter"]}),f&&e.jsx("div",{className:"absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:R.map(t=>e.jsx("li",{className:`${l.includes(t.accessor)?"cursor-not-allowed text-gray-100":"cursor-pointer text-gray-400 hover:text-white"}`,onClick:()=>{l.includes(t.accessor)||(h(n=>[...n,t.accessor]),x(n=>({...n,[t.accessor]:""}))),A(!1)},children:t.header},t.accessor))})}),l.length>0&&e.jsx("div",{onClick:M,className:"inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white",children:"Clear all filter"})]})]})})})]})})}),e.jsx(B,{filename:"phone_numbers",className:"relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]",data:()=>y,children:"Download CSV"})]}),e.jsx("div",{className:"mt-4 overflow-x-auto  bg-[#1d2937]",children:e.jsx("div",{className:k?"":"overflow-x-auto border-b border-gray-400 shadow",children:k&&y.length===0?e.jsx(oe,{columns:N}):e.jsxs("table",{className:"min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]",children:[e.jsx("thead",{className:"bg-[#1d2937]",children:e.jsx(ce,{actionPosition:"onTable",onSort:()=>{},columns:N,actions:{}})}),e.jsx("tbody",{className:"divide-y divide-gray-400 bg-[#1d2937]",children:y.map(t=>e.jsxs("tr",{className:"text-white",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t.id}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t.number}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:V[t.status]??"N/A"})]},t.id))})]})})}),e.jsx(de,{paginationData:O})]})]})})};export{Re as default};
