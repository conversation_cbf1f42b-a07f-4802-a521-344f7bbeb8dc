import{j as s}from"./@react-google-maps/api-c55ecefa.js";import{R as A,r as B,f as D,L as I}from"./vendor-2ae44a2e.js";import{u as M}from"./react-hook-form-47c010f8.js";import{o as O}from"./yup-5abd4662.js";import{c as T,a as i,e as U}from"./yup-5c93ed04.js";import{A as K,M as Y,s as z,t as G}from"./index-b2ff2fa1.js";import{I as H}from"./InteractiveButton-bff38983.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";const we=()=>{var m,c,p,u,x,g;const{dispatch:n}=A.useContext(K),[l,a]=B.useState(!1),E=window.location.search,R=new URLSearchParams(E).get("token"),F=T({code:i().required(),password:i().required(),confirmPassword:i().oneOf([U("password"),null],"Passwords must match")}).required(),L=D(),{register:o,handleSubmit:$,setError:d,formState:{errors:t}}=M({resolver:O(F)}),C=async h=>{var f,w,b,y,j,N,k,v;let q=new Y;try{a(!0);const e=await q.reset(R,h.code,h.password);if(!e.error)z(n,"Password Reset"),setTimeout(()=>{L("/user/login")},2e3);else if(e.validation){const P=Object.keys(e.validation);for(let r=0;r<P.length;r++){const S=P[r];d(S,{type:"manual",message:e.validation[S]})}}a(!1)}catch(e){a(!1),console.log("Error",e),d("code",{type:"manual",message:(w=(f=e==null?void 0:e.response)==null?void 0:f.data)!=null&&w.message?(y=(b=e==null?void 0:e.response)==null?void 0:b.data)==null?void 0:y.message:e==null?void 0:e.message}),G(n,(N=(j=e==null?void 0:e.response)==null?void 0:j.data)!=null&&N.message?(v=(k=e==null?void 0:e.response)==null?void 0:k.data)==null?void 0:v.message:e==null?void 0:e.message)}};return s.jsx(s.Fragment,{children:s.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[s.jsxs("form",{onSubmit:$(C),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"code",children:"Code"}),s.jsx("input",{type:"text",placeholder:"Enter code sent to your email",...o("code"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(m=t.code)!=null&&m.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(c=t.code)==null?void 0:c.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"password",children:"Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("password"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(p=t.password)!=null&&p.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(u=t.password)==null?void 0:u.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"confirmPassword",children:"Confirm Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("confirmPassword"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(x=t.confirmPassword)!=null&&x.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(g=t.confirmPassword)==null?void 0:g.message})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(H,{className:"bg-primaryBlue disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",type:"submit",loading:l,disabled:l,children:"Reset Password"}),s.jsx(I,{className:"inline-block align-baseline font-bold text-sm text-primaryBlue",to:"/admin/login",children:"Login?"})]})]}),s.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{we as default};
