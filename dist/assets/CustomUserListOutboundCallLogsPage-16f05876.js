import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as r,i as ne,r as g}from"./vendor-2ae44a2e.js";import{A as oe,G as ce,T as de,t as xe,s as he,B as pe,a as ue,b as me,l as ge,R as fe,c as je,m as be,n as ve,P as ye,X as we}from"./index-b2ff2fa1.js";import{u as Ne}from"./react-hook-form-47c010f8.js";import{C as Ce}from"./react-papaparse-b60a38ab.js";import{X as Se,e as B,f as M}from"./lucide-react-1246a7ed.js";import{C as f,q as j,_ as C}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./papaparse-2d1475f9.js";const S={0:"Inactive",1:"Active",2:"Paused"},z={inactive:0,active:1,paused:2,INACTIVE:0,ACTIVE:1,PAUSED:2,Inactive:0,Active:1,Paused:2},k=[{header:"Action",accessor:""},{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Campaign ID",accessor:"campaign_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Call ID",accessor:"call_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Recording Link",accessor:"recording_link",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Duration (sec)",accessor:"duration",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:S},{header:"Credit Used",accessor:"cost",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}}],U=k.filter(_=>!["","recording_link","duration"].includes(_.accessor)),Ge=()=>{const{state:_,dispatch:D}=r.useContext(oe),{dispatch:P}=r.useContext(ce),[n,G]=ne(),[b,d]=r.useState([]),[o,u]=r.useState(n.get("campaign_id")?["campaign_id"]:[]),[x,T]=r.useState(""),[F,A]=r.useState(!1),[E,I]=r.useState(!1),[v,W]=r.useState([]),{CSVDownloader:X}=Ce(),[H,K]=r.useState({currentPage:0,pageSize:0,totalNumber:0,totalPages:0});Ne({defaultValues:{}});const[V,h]=r.useState({}),[y,J]=r.useState("eq"),[$,R]=r.useState(null),[Q,L]=r.useState(!1),[Y,Z]=r.useState(null);r.useEffect(()=>{P({type:"SETPATH",payload:{path:"outbound_call_logs"}}),O()},[b,x]);const p=(t,i,s)=>{if(!s){d(c=>c.filter(m=>!m.startsWith(t+",")));return}let a=s,l=i||"eq";if(t==="status"){const c=s.toLowerCase();z.hasOwnProperty(c)&&(a=z[c])}const w=`${t},${l},${a}`;d(c=>[...c.filter(N=>!N.startsWith(t+",")),w])};async function O(){try{I(!0);const t=new de,i=`${t.getProjectId()}_call_logs`;let s=["type,eq,1",n.get("campaign_id")?`${i}.campaign_id,eq,${n.get("campaign_id")}`:void 0].filter(Boolean);if(x&&s.push(`${i}.call_id,cs,${x}`),b.length>0){const se=b.map(ae=>{const[ie,re,le]=ae.split(",");return`${i}.${ie},${re},${le}`});s=[...s,...se]}console.log("filterArray",s);const a=await t.getPaginate("call_logs",{size:n.get("limit")??50,page:n.get("page")??1,filter:s.filter(Boolean)}),{list:l,total:w,limit:c,num_pages:m,page:N}=a;W(l),K({currentPage:N,pageSize:c,totalNumber:w,totalPages:m})}catch(t){console.log("ERROR",t),xe(D,t.message),he(P,t.message,5e3,"error")}I(!1)}const ee=()=>{u([]),d([]),h({});for(const t of n.keys())t!=="campaign_id"&&n.delete(t);G(n),O()},q=(t,i)=>{navigator.clipboard.writeText(t),R(i),setTimeout(()=>R(null),2e3)},te=({isOpen:t,onClose:i,row:s})=>{var a;return s?e.jsx(j,{appear:!0,show:t,as:g.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[102]",onClose:i,children:[e.jsx(j.Child,{as:g.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4",children:e.jsx(j.Child,{as:g.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{className:"w-full max-w-2xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(C.Title,{className:"text-xl font-medium text-white",children:"Call Details"}),e.jsx("button",{onClick:i,children:e.jsx(we,{className:"h-6 w-6 text-white/70 hover:text-white"})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Call ID"}),e.jsx("p",{className:"text-white",children:s.call_id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Campaign ID"}),e.jsx("p",{className:"text-white",children:s.campaign_id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Duration"}),e.jsxs("p",{className:"text-white",children:[s.duration," seconds"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Status"}),e.jsx("p",{className:"text-white",children:S[s.status]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Cost"}),e.jsxs("p",{className:"text-white",children:["$",(a=s==null?void 0:s.cost)==null?void 0:a.toFixed(2)]})]})]}),s.recording_link&&e.jsxs("div",{className:"mt-6",children:[e.jsx("p",{className:"mb-2 text-sm text-white/70",children:"Recording Link"}),e.jsxs("div",{className:"flex items-center gap-2 rounded bg-[#2d3947] p-3",children:[e.jsx("a",{className:"flex-1 truncate text-white hover:underline",href:s.recording_link,target:"_blank",rel:"noopener noreferrer",children:s.recording_link}),e.jsx("button",{onClick:l=>{l.preventDefault(),q(s.recording_link,s.id)},className:"flex items-center rounded bg-[#1d2937] p-1.5 transition-colors hover:bg-[#19b2f6]/20",title:"Copy link",children:$===s.id?e.jsx(B,{className:"h-4 w-4 text-green-400"}):e.jsx(M,{className:"h-4 w-4 text-white/70 hover:text-white"})})]})]})]})]})})})})]})}):null};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5",children:[e.jsx("h3",{className:"my-4 px-8 text-2xl font-bold text-white",children:"Outbound Call Logs"}),e.jsxs("div",{className:"bg-[#1d2937] px-8 py-4",children:[e.jsxs("div",{className:"flex h-fit items-center justify-between gap-3",children:[e.jsx("div",{className:"flex w-[200px] min-w-[200px] items-center justify-between",children:e.jsx("div",{className:"relative z-10 rounded bg-[#1d2937]",children:e.jsxs(f,{children:[e.jsxs("div",{className:"flex items-center gap-4 bg-[#1d2937] text-white",children:[e.jsxs(f.Button,{className:"flex w-[130px] cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white focus-visible:outline-0 focus-visible:outline-transparent",children:[e.jsx(pe,{}),e.jsx("span",{children:"Filters"}),o.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:o.length})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white focus-within:border-gray-400",children:[e.jsx(ue,{className:`"text-white" }
                        text-xl`}),e.jsx("input",{type:"text",placeholder:"search by call id",className:"border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none",style:{boxShadow:"0 0 transparent"},value:x,onChange:t=>{T(t.target.value),p("call_id","cs",t.target.value)}}),x&&e.jsx(me,{className:"cursor-pointer text-lg text-white",onClick:()=>{T(""),p("call_id","cs","")}})]})]}),e.jsx(j,{as:g.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(f.Panel,{children:e.jsxs("div",{className:"filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10",children:[e.jsx("span",{className:"absolute left-5 top-2 font-medium text-white",children:"Filters"}),e.jsx(f.Button,{onClick:()=>{console.log("clicked"),u([]),d([]),h({})},children:e.jsx(Se,{className:"absolute right-2 top-2 cursor-pointer text-white"})}),o==null?void 0:o.map((t,i)=>{var s;return e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("button",{type:"button",className:"block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none",title:t,style:{WebkitTouchCallout:"none"},children:((s=U.find(a=>a.accessor===t))==null?void 0:s.header)||t}),e.jsxs("select",{className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none",value:y,onChange:a=>{J(a.target.value),p(t,a.target.value,V[t])},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),t==="status"?e.jsxs("select",{value:V[t]||"",onChange:a=>{h(l=>({...l,[t]:a.target.value})),p(t,y,a.target.value)},className:"h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",children:[e.jsx("option",{value:"",children:"Select Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]}):e.jsx(ge,{type:"text",labelClassName:"!mb-0",placeholder:"Enter value",setValue:()=>{},showIcon:!1,className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",onReady:a=>p(t,y,a)}),e.jsx(fe,{className:"cursor-pointer text-2xl text-red-600",onClick:()=>{u(a=>a.filter(l=>l!==t)),d(a=>a.filter(l=>!l.includes(t))),h(a=>{const l={...a};return delete l[t],l})}})]},i)}),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>A(!F),children:[e.jsx(je,{}),"Add filter"]}),F&&e.jsx("div",{className:"absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:U.map(t=>e.jsx("li",{className:`${o.includes(t.accessor)?"cursor-not-allowed text-gray-100":"cursor-pointer text-gray-400 hover:text-white"}`,onClick:()=>{o.includes(t.accessor)||(u(i=>[...i,t.accessor]),h(i=>({...i,[t.accessor]:""}))),A(!1)},children:t.header},t.accessor))})}),o.length>0&&e.jsx("div",{onClick:ee,className:"inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white",children:"Clear all filter"})]})]})})})]})})}),e.jsx(X,{filename:"outbound_call_logs",className:"relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]",data:()=>v,children:"Download CSV"})]}),e.jsx("div",{className:"mt-4 overflow-x-auto  bg-[#1d2937]",children:e.jsx("div",{className:E?"":"overflow-x-auto border-b border-gray-200 shadow",children:E&&v.length===0?e.jsx(be,{columns:k}):e.jsxs("table",{className:"min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]",children:[e.jsx("thead",{className:"bg-[#1d2937]",children:e.jsx(ve,{actionPosition:"onTable",onSort:()=>{},columns:k,actions:{view:{show:!0}}})}),e.jsx("tbody",{className:"divide-y divide-gray-400 bg-[#1d2937]",children:v.map(t=>{var i;return e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex items-center justify-between gap-3 text-sm",children:e.jsx("button",{onClick:()=>{Z(t),L(!0)},className:"rounded-[30px] bg-[#19b2f6]/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-[#19b2f6]/30",children:"View"})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.id}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.campaign_id}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.call_id}),e.jsx("td",{className:"px-6 py-4 text-white",children:t.recording_link?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:s=>{s.preventDefault(),q(t.recording_link,t.id)},className:"flex items-center rounded bg-[#2d3947] p-1.5 transition-colors hover:bg-[#19b2f6]/20",title:"Copy link",children:$===t.id?e.jsx(B,{className:"h-4 w-4 text-green-400"}):e.jsx(M,{className:"h-4 w-4 text-white/70 hover:text-white"})}),e.jsx("p",{className:"flex items-center gap-2",children:e.jsx("a",{className:"text-white hover:underline",href:t.recording_link,target:"_blank",rel:"noopener noreferrer",children:t.recording_link.length>50?`${t.recording_link.substring(0,50)}...`:t.recording_link})})]}):"-"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.duration}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:S[t.status]}),e.jsxs("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:["$",(i=t==null?void 0:t.cost)==null?void 0:i.toFixed(2)]})]},t.id)})})]})})}),e.jsx(ye,{paginationData:H})]})]}),e.jsx(te,{isOpen:Q,onClose:()=>L(!1),row:Y})]})};export{Ge as default};
