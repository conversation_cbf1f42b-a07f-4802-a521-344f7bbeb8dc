import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as r,f as h}from"./vendor-2ae44a2e.js";import{X as u}from"./@uppy/xhr-upload-502ff6d3.js";import{u as f,D as x}from"./@uppy/react-66cc10ac.js";import{M as g,G as p,s as b}from"./index-b2ff2fa1.js";import{a as j}from"./@uppy/core-a7fbc19c.js";import"./@uppy/aws-s3-a38c5234.js";import"./@craftjs/core-9da1c17f.js";import"./@uppy/aws-s3-multipart-4b7e97ea.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@uppy/dashboard-af41baed.js";import"./@fullcalendar/core-a789a586.js";import"./@uppy/compressor-d7e7d557.js";import"./@uppy/drag-drop-aa7c4730.js";import"./@uppy/progress-bar-3613651c.js";import"./@uppy/file-input-ca192629.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let d=new g;const Y=({setSidebar:i})=>{const{dispatch:m}=r.useContext(p),l=h(),{dispatch:a}=r.useContext(p),n=f(()=>{let o=new j;return o.use(u,{id:"XHRUpload",method:"post",formData:!0,limit:0,fieldName:"file",allowedMetaFields:["caption","size"],headers:d.getHeader(),endpoint:d.uploadUrl()}),o.on("file-added",e=>{o.setFileMeta(e.id,{size:e.size,caption:""})}),o.on("upload-success",async(e,s)=>{s.status,s.body,console.log("response",s),b(a,"Uploaded"),l("/admin/photos")}),o.on("upload-error",(e,s,c)=>{c.status==401&&tokenExpireError(m,"TOKEN_EXPIRED")}),o});return r.useEffect(()=>{a({type:"SETPATH",payload:{path:"photos"}})},[]),t.jsxs("div",{className:"relative p-4 flex-auto",children:[t.jsxs("div",{className:"flex items-center pb-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("svg",{onClick:()=>i(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:t.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),t.jsx("span",{className:"text-lg font-semibold",children:"Add Photo"})]}),t.jsx("div",{className:"flex items-center gap-4",children:t.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>i(!1),children:"Cancel"})})]}),t.jsx(x,{uppy:n})]})};export{Y as default};
