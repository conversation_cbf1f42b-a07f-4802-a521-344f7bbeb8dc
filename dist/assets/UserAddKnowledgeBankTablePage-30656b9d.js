import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as s,f as j}from"./vendor-2ae44a2e.js";import{u as N}from"./react-hook-form-47c010f8.js";import{o as v}from"./yup-5abd4662.js";import{c as E,a as n}from"./yup-5c93ed04.js";import{G as b,M as T,s as I,t as A}from"./index-b2ff2fa1.js";import"./react-quill-d06fcfc9.js";import{M as p}from"./MkdInput-a584fac2.js";import{I as F}from"./InteractiveButton-bff38983.js";import"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";const le=()=>{const{dispatch:u}=s.useContext(b),h=E({transcript:n(),type:n(),filename:n(),status:n()}).required(),{dispatch:y}=s.useContext(b),[f,R]=s.useState({}),[g,d]=s.useState(!1),w=j(),{register:i,handleSubmit:S,setError:x,setValue:B,formState:{errors:l}}=N({resolver:v(h)});s.useState([]);const k=async o=>{let c=new T;d(!0);try{for(let r in f){let a=new FormData;a.append("file",f[r].file);let m=await c.uploadImage(a);o[r]=m.url}c.setTable("knowledge_bank");const e=await c.callRestAPI({transcript:o.transcript,type:o.type,filename:o.filename,status:o.status},"POST");if(!e.error)I(u,"Added"),w("/user/knowledge_bank");else if(e.validation){const r=Object.keys(e.validation);for(let a=0;a<r.length;a++){const m=r[a];x(m,{type:"manual",message:e.validation[m]})}}d(!1)}catch(e){d(!1),console.log("Error",e),x("transcript",{type:"manual",message:e.message}),A(y,e.message)}};return s.useEffect(()=>{u({type:"SETPATH",payload:{path:"knowledge_bank"}})},[]),t.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Knowledge Bank"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:S(k),children:[t.jsx(p,{type:"text",page:"add",name:"transcript",errors:l,label:"Transcript",placeholder:"Transcript",register:i,className:""}),t.jsx(p,{type:"text",page:"add",name:"type",errors:l,label:"type",placeholder:"type",register:i,className:""}),t.jsx(p,{type:"text",page:"add",name:"filename",errors:l,label:"Filename",placeholder:"Filename",register:i,className:""}),t.jsx(p,{type:"text",page:"add",name:"status",errors:l,label:"Status",placeholder:"Status",register:i,className:""}),t.jsx(F,{type:"submit",loading:g,disabled:g,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{le as default};
