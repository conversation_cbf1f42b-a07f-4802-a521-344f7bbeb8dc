import{j as s}from"./@react-google-maps/api-c55ecefa.js";import{R as a,h as d}from"./vendor-2ae44a2e.js";import"./yup-5c93ed04.js";import{M as x,G as m,t as p}from"./index-b2ff2fa1.js";import{S as f}from"./index-a74110af.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let l=new x;const F=()=>{a.useContext(m);const{dispatch:c}=a.useContext(m),[e,n]=a.useState({}),[o,r]=a.useState(!0),i=d();return a.useEffect(function(){(async function(){try{r(!0),l.setTable("knowledge_bank");const t=await l.callRestAPI({id:Number(i==null?void 0:i.id),join:""},"GET");t.error||(n(t.model),r(!1))}catch(t){r(!1),console.log("error",t),p(c,t.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:o?s.jsx(f,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Knowledge Bank"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Transcript"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.transcript})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Temp974"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.temp974})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Filename"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.filename})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})})]})})};export{F as default};
