import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as i,f as L}from"./vendor-2ae44a2e.js";import{u as D}from"./react-hook-form-47c010f8.js";import{o as F}from"./yup-5abd4662.js";import{c as P,a as o}from"./yup-5c93ed04.js";import{G as A,M as B,s as J,t as W}from"./index-b2ff2fa1.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const me=({setSidebar:c,closeSidebar:d})=>{var b,y,v,w,N,j,k,S;const C=P({firstName:o().required(),lastName:o().required(),email:o().email().required(),password:o().required(),status:o().required(),telephonicService:o().required(),speechRecognition:o().required(),textToSpeech:o().required(),llm:o().required()}).required(),{dispatch:u}=i.useContext(A),{dispatch:T}=i.useContext(A),[p,h]=i.useState(!1);L();const{register:a,handleSubmit:x,setError:g,formState:{errors:n}}=D({resolver:F(C)}),q=[{name:"Active",value:"active"},{name:"Inactive",value:"inactive"}],E=[{name:"Twilio",value:"twilio"},{name:"Telynx",value:"telynx"}],I=[{name:"Deepgram",value:"deepgram"}],O=[{name:"Eleven Labs",value:"elevenlabs"},{name:"Azure Speech SDK",value:"azure"}],R=[{name:"Claude Haiku",value:"claude-3-haiku-20240307"},{name:"GPT-3.5",value:"ai_energy-3-turbo"}],f=async s=>{let m=new B;h(!0);try{const t=await m.register(s.email,s.password,"user",s.firstName,s.lastName);if(t.error){if(t.validation){const l=Object.keys(t.validation);for(let r=0;r<l.length;r++){const _=l[r];g(_,{type:"manual",message:t.validation[_]})}}}else{const l={phone_service:s.telephonicService,tts_settings:JSON.stringify({provider:s.textToSpeech,voice_id:"qzsSemAullBuJxvBTx9G",speed:"normal",output_format:"ulaw_8000"}),llm_settings:JSON.stringify({provider:s.llm.includes("claude")?"anthropic":"openai",temperature:"0.01",max_tokens:"128",top_p:"0.9",model_name:s.llm,system_prompt:"You are a customer care agent for an insurance company, making outbound calls. Follow the decision tree provided below and decide which response best suits what the lead says. Always stick to the script, picking the best response at all times to keep the conversation going. You are Alice, and the user will respond as the lead. I will respond as the lead; just provide responses and decide which is next in the tree from the responses I give. Start with the introduction. Don't say the headers like introduction or any other ones in between **. Just say what is under them for the right situation. Don't include numbers, rather spell it. If you use any acronym, also spell it out.\\n\\n---\\n\\nHi, this is Alice from ABC. Do you have a minute to discuss how you can save on your insurance?\\n\\n---\\n\\nI’d love to tell you about our latest promotion. Are you interested in saving on auto, home, or life insurance?\\n\\n---\\n\\nCould you provide some basic details like your age, and location?\\n\\n---\\n\\nBased on your information, you could save up to [specific amount] annually on [insurance type]. Would you like to hear more about these savings or perhaps get a detailed quote?\\n\\n---\\n\\nGreat! I’ll need a few more details to prepare a personalized quote for you. Could you tell me more about your current coverage?\\n\\n---\\n\\nOur plans offer great coverage options tailored to your needs, with significant savings. Would you like me to walk you through some of the benefits and coverage options?\\n\\n---\\n\\nIs there anything else I can help you with today?\\n\\n---\\n\\nThank you for your time. We appreciate your interest in our services and look forward to helping you save on your insurance.\\n\\n---\\n\\nAlright, everything is all set! We'll be in touch soon. We are looking forward to helping you save on your insurance!",first_message:"Hello, Stacy! I'm Alice from ABC Insurance company. I can help you save on the insurance, do you have a minute?"}),telephony_settings:JSON.stringify({provider:s.telephonicService}),speech_recognition_settings:JSON.stringify({provider:s.speechRecognition,model:"nova-2-phonecall",encoding:"mulaw",sample_rate:8e3}),stt_service:s.speechRecognition,llm_service:s.llm,user_id:t.user_id};m.setTable("user_settings");const r=await m.callRestAPI(l,"POST");J(u,"Added"),d&&d()}}catch(t){console.log("Error",t),g("email",{type:"manual",message:t.message}),W(u,t.message)}h(!1)};return i.useEffect(()=>{T({type:"SETPATH",payload:{path:"users"}})},[]),e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx("svg",{onClick:()=>c(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add Customer"})]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]",onClick:()=>c(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm",onClick:async()=>{await x(f)(),c(!1)},disabled:p,children:p?"Saving":"Save"})]})]}),e.jsxs("form",{className:"p-4 w-full max-w-lg text-left",style:{overflowY:"auto"},onSubmit:x(f),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"firstName",children:"First Name"}),e.jsx("input",{type:"text",placeholder:"First Name",...a("firstName"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(b=n.firstName)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=n.firstName)==null?void 0:y.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"lastName",children:"Last Name"}),e.jsx("input",{type:"text",placeholder:"Last Name",...a("lastName"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(v=n.lastName)!=null&&v.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=n.lastName)==null?void 0:w.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...a("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(N=n.email)!=null&&N.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=n.email)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"Password",...a("password"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(k=n.password)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(S=n.password)==null?void 0:S.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{name:"status",className:"px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none",...a("status"),children:q.map(s=>e.jsx("option",{name:s.name,value:s.value,children:s.name},s.value))})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",children:"Telephone Service"}),e.jsx("select",{name:"telephonicService",className:"px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none",...a("telephonicService"),children:E.map(s=>e.jsx("option",{name:s.name,value:s.value,children:s.name},s.value))})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",children:"Speech Recognition"}),e.jsx("select",{name:"speechRecognition",className:"px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none",...a("speechRecognition"),children:I.map(s=>e.jsx("option",{name:s.name,value:s.value,children:s.name},s.value))})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",children:"Text-to-Speech"}),e.jsx("select",{name:"textToSpeech",className:"px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none",...a("textToSpeech"),children:O.map(s=>e.jsx("option",{name:s.name,value:s.value,children:s.name},s.value))})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",children:"LLM"}),e.jsx("select",{name:"llm",className:"px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none",...a("llm"),children:R.map(s=>e.jsx("option",{name:s.name,value:s.value,children:s.name},s.value))})]})]})]})};export{me as default};
