import{j as H}from"./@react-google-maps/api-c55ecefa.js";import{c as yt,a as he,d as hn,b as Zt}from"./react-pdf-9659a983.js";import{c as f,r as L}from"./vendor-2ae44a2e.js";import{p as bn}from"./@react-pdf-viewer/core-ad80e4c3.js";import{t as J}from"./@craftjs/core-9da1c17f.js";import{w as oe}from"./react-calendar-366f51e4.js";var en={},T={},fe={},Pn=f&&f.__createBinding||(Object.create?function(e,n,o,t){t===void 0&&(t=o);var r=Object.getOwnPropertyDescriptor(n,o);(!r||("get"in r?!n.__esModule:r.writable||r.configurable))&&(r={enumerable:!0,get:function(){return n[o]}}),Object.defineProperty(e,t,r)}:function(e,n,o,t){t===void 0&&(t=o),e[t]=n[o]}),En=f&&f.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),jn=f&&f.__importStar||function(e){if(e&&e.__esModule)return e;var n={};if(e!=null)for(var o in e)o!=="default"&&Object.prototype.hasOwnProperty.call(e,o)&&Pn(n,e,o);return En(n,e),n};Object.defineProperty(fe,"__esModule",{value:!0});const ot=jn(bn),Rn="default"in ot?ot.default:ot;fe.default=Rn;var ht={},gt={exports:{}};function tn(e){var n,o,t="";if(typeof e=="string"||typeof e=="number")t+=e;else if(typeof e=="object")if(Array.isArray(e)){var r=e.length;for(n=0;n<r;n++)e[n]&&(o=tn(e[n]))&&(t&&(t+=" "),t+=o)}else for(o in e)e[o]&&(t&&(t+=" "),t+=o);return t}function Dt(){for(var e,n,o=0,t="",r=arguments.length;o<r;o++)(e=arguments[o])&&(n=tn(e))&&(t&&(t+=" "),t+=n);return t}gt.exports=Dt,gt.exports.clsx=Dt;var we=gt.exports,qe={};Object.defineProperty(qe,"__esModule",{value:!0});const Sn=L;qe.default=(0,Sn.createContext)(null);var ze={};Object.defineProperty(ze,"__esModule",{value:!0});const xn=H;function On({children:e,type:n}){return(0,xn.jsx)("div",{className:`react-pdf__message react-pdf__message--${n}`,children:e})}ze.default=On;var bt={},wn=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(bt,"__esModule",{value:!0});const U=wn(J),Tn="noopener noreferrer nofollow";class $n{constructor(){this.externalLinkEnabled=!0,this.externalLinkRel=void 0,this.externalLinkTarget=void 0,this.isInPresentationMode=!1,this.pdfDocument=void 0,this.pdfViewer=void 0}setDocument(n){this.pdfDocument=n}setViewer(n){this.pdfViewer=n}setExternalLinkRel(n){this.externalLinkRel=n}setExternalLinkTarget(n){this.externalLinkTarget=n}setHistory(){}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return(0,U.default)(this.pdfViewer,"PDF viewer is not initialized."),this.pdfViewer.currentPageNumber||0}set page(n){(0,U.default)(this.pdfViewer,"PDF viewer is not initialized."),this.pdfViewer.currentPageNumber=n}get rotation(){return 0}set rotation(n){}goToDestination(n){return new Promise(o=>{(0,U.default)(this.pdfDocument,"PDF document not loaded."),(0,U.default)(n,"Destination is not specified."),typeof n=="string"?this.pdfDocument.getDestination(n).then(o):Array.isArray(n)?o(n):n.then(o)}).then(o=>{(0,U.default)(Array.isArray(o),`"${o}" is not a valid destination array.`);const t=o[0];new Promise(r=>{(0,U.default)(this.pdfDocument,"PDF document not loaded."),t instanceof Object?this.pdfDocument.getPageIndex(t).then(s=>{r(s)}).catch(()=>{(0,U.default)(!1,`"${t}" is not a valid page reference.`)}):typeof t=="number"?r(t):(0,U.default)(!1,`"${t}" is not a valid destination reference.`)}).then(r=>{const s=r+1;(0,U.default)(this.pdfViewer,"PDF viewer is not initialized."),(0,U.default)(s>=1&&s<=this.pagesCount,`"${s}" is not a valid page number.`),this.pdfViewer.scrollPageIntoView({dest:o,pageIndex:r,pageNumber:s})})})}navigateTo(n){this.goToDestination(n)}goToPage(n){const o=n-1;(0,U.default)(this.pdfViewer,"PDF viewer is not initialized."),(0,U.default)(n>=1&&n<=this.pagesCount,`"${n}" is not a valid page number.`),this.pdfViewer.scrollPageIntoView({pageIndex:o,pageNumber:n})}addLinkAttributes(n,o,t){n.href=o,n.rel=this.externalLinkRel||Tn,n.target=t?"_blank":this.externalLinkTarget||""}getDestinationHash(){return"#"}getAnchorUrl(){return"#"}setHash(){}executeNamedAction(){}cachePageRef(){}isPageVisible(){return!0}isPageCached(){return!0}executeSetOCGState(){}}bt.default=$n;var Ke={};Object.defineProperty(Ke,"__esModule",{value:!0});const Cn={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};Ke.default=Cn;var q={};(function(e){var n=f&&f.__importDefault||function(c){return c&&c.__esModule?c:{default:c}};Object.defineProperty(e,"__esModule",{value:!0}),e.loadFromFile=e.isCancelException=e.makePageCallback=e.cancelRunningTask=e.displayWorkerWarning=e.displayCORSWarning=e.getDevicePixelRatio=e.dataURItoByteString=e.isDataURI=e.isBlob=e.isArrayBuffer=e.isString=e.isProvided=e.isDefined=e.isLocalFileSystem=e.isBrowser=void 0;const o=n(J),t=n(oe);e.isBrowser=typeof document<"u",e.isLocalFileSystem=e.isBrowser&&window.location.protocol==="file:";function r(c){return typeof c<"u"}e.isDefined=r;function s(c){return r(c)&&c!==null}e.isProvided=s;function i(c){return typeof c=="string"}e.isString=i;function g(c){return c instanceof ArrayBuffer}e.isArrayBuffer=g;function a(c){return(0,o.default)(e.isBrowser,"isBlob can only be used in a browser environment"),c instanceof Blob}e.isBlob=a;function l(c){return i(c)&&/^data:/.test(c)}e.isDataURI=l;function u(c){(0,o.default)(l(c),"Invalid data URI.");const[P="",p=""]=c.split(",");return P.split(";").indexOf("base64")!==-1?atob(p):unescape(p)}e.dataURItoByteString=u;function d(){return e.isBrowser&&window.devicePixelRatio||1}e.getDevicePixelRatio=d;const _="On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.";function m(){(0,t.default)(!e.isLocalFileSystem,`Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ${_}`)}e.displayCORSWarning=m;function h(){(0,t.default)(!e.isLocalFileSystem,`Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${_}`)}e.displayWorkerWarning=h;function v(c){c&&c.cancel&&c.cancel()}e.cancelRunningTask=v;function b(c,P){return Object.defineProperty(c,"width",{get(){return this.view[2]*P},configurable:!0}),Object.defineProperty(c,"height",{get(){return this.view[3]*P},configurable:!0}),Object.defineProperty(c,"originalWidth",{get(){return this.view[2]},configurable:!0}),Object.defineProperty(c,"originalHeight",{get(){return this.view[3]},configurable:!0}),c}e.makePageCallback=b;function R(c){return c.name==="RenderingCancelledException"}e.isCancelException=R;function O(c){return new Promise((P,p)=>{const E=new FileReader;E.onload=()=>{if(!E.result)return p(new Error("Error while reading a file."));P(E.result)},E.onerror=$=>{if(!$.target)return p(new Error("Error while reading a file."));const{error:D}=$.target;if(!D)return p(new Error("Error while reading a file."));switch(D.code){case D.NOT_FOUND_ERR:return p(new Error("Error while reading a file: File not found."));case D.SECURITY_ERR:return p(new Error("Error while reading a file: Security error."));case D.ABORT_ERR:return p(new Error("Error while reading a file: Aborted."));default:return p(new Error("Error while reading a file."))}},E.readAsArrayBuffer(c)})}e.loadFromFile=O})(q);var ae={};Object.defineProperty(ae,"__esModule",{value:!0});const Ln=L;function Dn(e,n){switch(n.type){case"RESOLVE":return{value:n.value,error:void 0};case"REJECT":return{value:!1,error:n.error};case"RESET":return{value:void 0,error:void 0};default:return e}}function kn(){return(0,Ln.useReducer)(Dn,{value:void 0,error:void 0})}ae.default=kn;var An=f&&f.__awaiter||function(e,n,o,t){function r(s){return s instanceof o?s:new o(function(i){i(s)})}return new(o||(o=Promise))(function(s,i){function g(u){try{l(t.next(u))}catch(d){i(d)}}function a(u){try{l(t.throw(u))}catch(d){i(d)}}function l(u){u.done?s(u.value):r(u.value).then(g,a)}l((t=t.apply(e,n||[])).next())})},kt=f&&f.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o},Y=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ht,"__esModule",{value:!0});const Me=H,C=L,Mn=Y(yt),In=Y(he),Nn=Y(we),At=Y(J),Ie=Y(oe),Mt=hn,nn=Y(fe),Gn=Y(qe),at=Y(ze),Fn=Y(bt),It=Y(Ke),K=q,Nt=Y(ae),{PDFDataRangeTransport:Vn}=nn.default,Bn=(e,n)=>{switch(n){case It.default.NEED_PASSWORD:{const o=prompt("Enter the password to open this PDF file.");e(o);break}case It.default.INCORRECT_PASSWORD:{const o=prompt("Invalid password. Please try again.");e(o);break}}};function Gt(e){return typeof e=="object"&&e!==null&&("data"in e||"range"in e||"url"in e)}const Wn=(0,C.forwardRef)(function(n,o){var{children:t,className:r,error:s="Failed to load PDF file.",externalLinkRel:i,externalLinkTarget:g,file:a,inputRef:l,imageResourcesPath:u,loading:d="Loading PDF…",noData:_="No PDF file specified.",onItemClick:m,onLoadError:h,onLoadProgress:v,onLoadSuccess:b,onPassword:R=Bn,onSourceError:O,onSourceSuccess:c,options:P,renderMode:p,rotate:E}=n,$=kt(n,["children","className","error","externalLinkRel","externalLinkTarget","file","inputRef","imageResourcesPath","loading","noData","onItemClick","onLoadError","onLoadProgress","onLoadSuccess","onPassword","onSourceError","onSourceSuccess","options","renderMode","rotate"]);const[D,M]=(0,Nt.default)(),{value:I,error:y}=D,[V,w]=(0,Nt.default)(),{value:S,error:B}=V,k=(0,C.useRef)(new Fn.default),A=(0,C.useRef)([]),Q=(0,C.useRef)(void 0),X=(0,C.useRef)(void 0);a&&a!==Q.current&&Gt(a)&&((0,Ie.default)(!(0,Mt.dequal)(a,Q.current),`File prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "file" prop.`),Q.current=a),P&&P!==X.current&&((0,Ie.default)(!(0,Mt.dequal)(P,X.current),`Options prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "options" prop.`),X.current=P);const ge=(0,C.useRef)({scrollPageIntoView:j=>{const{dest:W,pageNumber:z,pageIndex:ue=z-1}=j;if(m){m({dest:W,pageIndex:ue,pageNumber:z});return}const ce=A.current[ue];if(ce){ce.scrollIntoView();return}(0,Ie.default)(!1,`An internal link leading to page ${z} was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>.`)}});(0,C.useImperativeHandle)(o,()=>({linkService:k,pages:A,viewer:ge}),[]);function Pe(){c&&c()}function me(){y&&((0,Ie.default)(!1,y.toString()),O&&O(y))}function Le(){M({type:"RESET"})}(0,C.useEffect)(Le,[a,M]);const ve=(0,C.useCallback)(()=>An(this,void 0,void 0,function*(){if(!a)return null;if(typeof a=="string")return(0,K.isDataURI)(a)?{data:(0,K.dataURItoByteString)(a)}:((0,K.displayCORSWarning)(),{url:a});if(a instanceof Vn)return{range:a};if((0,K.isArrayBuffer)(a))return{data:a};if(K.isBrowser&&(0,K.isBlob)(a))return{data:yield(0,K.loadFromFile)(a)};if((0,At.default)(typeof a=="object","Invalid parameter in file, need either Uint8Array, string or a parameter object"),(0,At.default)(Gt(a),"Invalid parameter object: need either .data, .range or .url"),"url"in a&&typeof a.url=="string"){if((0,K.isDataURI)(a.url)){const{url:j}=a,W=kt(a,["url"]),z=(0,K.dataURItoByteString)(j);return Object.assign({data:z},W)}(0,K.displayCORSWarning)()}return a}),[a]);(0,C.useEffect)(()=>{const j=(0,In.default)(ve());return j.promise.then(W=>{M({type:"RESOLVE",value:W})}).catch(W=>{M({type:"REJECT",error:W})}),()=>{(0,K.cancelRunningTask)(j)}},[ve,M]),(0,C.useEffect)(()=>{if(!(typeof I>"u")){if(I===!1){me();return}Pe()}},[I]);function De(){S&&(b&&b(S),A.current=new Array(S.numPages),k.current.setDocument(S))}function Ee(){B&&((0,Ie.default)(!1,B.toString()),h&&h(B))}function Fe(){w({type:"RESET"})}(0,C.useEffect)(Fe,[w,I]);function nt(){if(!I)return;const j=Object.assign(Object.assign({},P),{isEvalSupported:!1}),W=Object.assign(Object.assign({},I),j),z=nn.default.getDocument(W);v&&(z.onProgress=v),R&&(z.onPassword=R);const ue=z;return ue.promise.then(ce=>{w({type:"RESOLVE",value:ce})}).catch(ce=>{ue.destroyed||w({type:"REJECT",error:ce})}),()=>{ue.destroy()}}(0,C.useEffect)(nt,[P,w,I]),(0,C.useEffect)(()=>{if(!(typeof S>"u")){if(S===!1){Ee();return}De()}},[S]);function ye(){k.current.setViewer(ge.current),k.current.setExternalLinkRel(i),k.current.setExternalLinkTarget(g)}(0,C.useEffect)(ye,[i,g]);function x(j,W){A.current[j]=W}function ke(j){delete A.current[j]}const Ae=(0,C.useMemo)(()=>({imageResourcesPath:u,linkService:k.current,onItemClick:m,pdf:S,registerPage:x,renderMode:p,rotate:E,unregisterPage:ke}),[u,m,S,p,E]),N=(0,C.useMemo)(()=>(0,Mn.default)($,()=>S),[$,S]);function Z(){return(0,Me.jsx)(Gn.default.Provider,{value:Ae,children:t})}function ne(){return a?S==null?(0,Me.jsx)(at.default,{type:"loading",children:typeof d=="function"?d():d}):S===!1?(0,Me.jsx)(at.default,{type:"error",children:typeof s=="function"?s():s}):Z():(0,Me.jsx)(at.default,{type:"no-data",children:typeof _=="function"?_():_})}return(0,Me.jsx)("div",Object.assign({className:(0,Nn.default)("react-pdf__Document",r),ref:l,style:{"--scale-factor":"1"}},N,{children:ne()}))});ht.default=Wn;var Pt={},Ye={};Object.defineProperty(Ye,"__esModule",{value:!0});const Un=L;Ye.default=(0,Un.createContext)(null);var Et={},jt={};Object.defineProperty(jt,"__esModule",{value:!0});class Hn{constructor({num:n,gen:o}){this.num=n,this.gen=o}toString(){let n=`${this.num}R`;return this.gen!==0&&(n+=this.gen),n}}jt.default=Hn;var Rt={};Object.defineProperty(Rt,"__esModule",{value:!0});const Jn=L,qn=q;function zn(e){const n=(0,Jn.useRef)(void 0),o=n.current;return(0,qn.isDefined)(o)?()=>o:()=>{const t=e();return n.current=t,t}}Rt.default=zn;var de={},Kn=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(de,"__esModule",{value:!0});const Yn=L,Qn=Kn(qe);function Xn(){return(0,Yn.useContext)(Qn.default)}de.default=Xn;var Qe={},Zn=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Qe,"__esModule",{value:!0});const er=L,tr=Zn(Ye);function nr(){return(0,er.useContext)(tr.default)}Qe.default=nr;var Ft=f&&f.__awaiter||function(e,n,o,t){function r(s){return s instanceof o?s:new o(function(i){i(s)})}return new(o||(o=Promise))(function(s,i){function g(u){try{l(t.next(u))}catch(d){i(d)}}function a(u){try{l(t.throw(u))}catch(d){i(d)}}function l(u){u.done?s(u.value):r(u.value).then(g,a)}l((t=t.apply(e,n||[])).next())})},rr=f&&f.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o},Ge=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Et,"__esModule",{value:!0});const Be=H,st=Ge(J),or=Ge(jt),it=Ge(Rt),ar=Ge(de),sr=Ge(Qe);function rn(e){const n=(0,ar.default)(),o=(0,sr.default)();(0,st.default)(o,"Unable to find Outline context.");const t=Object.assign(Object.assign(Object.assign({},n),o),e),{item:r,linkService:s,onItemClick:i,pdf:g}=t,a=rr(t,["item","linkService","onItemClick","pdf"]);(0,st.default)(g,"Attempted to load an outline, but no document was specified. Wrap <Outline /> in a <Document /> or pass explicit `pdf` prop.");const l=(0,it.default)(()=>typeof r.dest=="string"?g.getDestination(r.dest):r.dest),u=(0,it.default)(()=>Ft(this,void 0,void 0,function*(){const h=yield l();if(!h)throw new Error("Destination not found.");const[v]=h;return g.getPageIndex(new or.default(v))})),d=(0,it.default)(()=>Ft(this,void 0,void 0,function*(){return(yield u())+1}));function _(h){h.preventDefault(),(0,st.default)(i||s,"Either onItemClick callback or linkService must be defined in order to navigate to an outline item."),i?Promise.all([l(),u(),d()]).then(([v,b,R])=>{i({dest:v,pageIndex:b,pageNumber:R})}):s&&s.goToDestination(r.dest)}function m(){if(!r.items||!r.items.length)return null;const{items:h}=r;return(0,Be.jsx)("ul",{children:h.map((v,b)=>(0,Be.jsx)(rn,Object.assign({item:v,pdf:g},a),typeof v.dest=="string"?v.dest:b))})}return(0,Be.jsxs)("li",{children:[(0,Be.jsx)("a",{href:"#",onClick:_,children:r.title}),m()]})}Et.default=rn;var ir=f&&f.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o},se=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Pt,"__esModule",{value:!0});const We=H,Ne=L,ur=se(he),cr=se(yt),lr=se(we),fr=se(J),dr=se(oe),pr=se(Ye),_r=se(Et),gr=q,mr=se(de),vr=se(ae);function yr(e){const n=(0,mr.default)(),o=Object.assign(Object.assign({},n),e),{className:t,inputRef:r,onItemClick:s,onLoadError:i,onLoadSuccess:g,pdf:a}=o,l=ir(o,["className","inputRef","onItemClick","onLoadError","onLoadSuccess","pdf"]);(0,fr.default)(a,"Attempted to load an outline, but no document was specified. Wrap <Outline /> in a <Document /> or pass explicit `pdf` prop.");const[u,d]=(0,vr.default)(),{value:_,error:m}=u;function h(){typeof _>"u"||_===!1||g&&g(_)}function v(){m&&((0,dr.default)(!1,m.toString()),i&&i(m))}function b(){d({type:"RESET"})}(0,Ne.useEffect)(b,[d,a]);function R(){if(!a)return;const p=(0,ur.default)(a.getOutline()),E=p;return p.promise.then($=>{d({type:"RESOLVE",value:$})}).catch($=>{d({type:"REJECT",error:$})}),()=>(0,gr.cancelRunningTask)(E)}(0,Ne.useEffect)(R,[d,a]),(0,Ne.useEffect)(()=>{if(_!==void 0){if(_===!1){v();return}h()}},[_]);const O=(0,Ne.useMemo)(()=>({onItemClick:s}),[s]),c=(0,Ne.useMemo)(()=>(0,cr.default)(l,()=>_),[l,_]);if(!_)return null;function P(){return _?(0,We.jsx)("ul",{children:_.map((p,E)=>(0,We.jsx)(_r.default,{item:p,pdf:a},typeof p.dest=="string"?p.dest:E))}):null}return(0,We.jsx)("div",Object.assign({className:(0,lr.default)("react-pdf__Outline",t),ref:r},c,{children:(0,We.jsx)(pr.default.Provider,{value:O,children:P()})}))}Pt.default=yr;var Xe={},Ze={};Object.defineProperty(Ze,"__esModule",{value:!0});const hr=L;Ze.default=(0,hr.createContext)(null);var St={},xt={},Ot={},G={},Oe={};Object.defineProperty(Oe,"__esModule",{value:!0});Oe.HEADING_PATTERN=Oe.PDF_ROLE_TO_HTML_ROLE=void 0;Oe.PDF_ROLE_TO_HTML_ROLE={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null};Oe.HEADING_PATTERN=/^H(\d+)$/;Object.defineProperty(G,"__esModule",{value:!0});G.getAttributes=G.getBaseAttributes=G.getRoleAttributes=G.isStructTreeNodeWithOnlyContentChild=G.isStructTreeNode=G.isPdfRole=void 0;const mt=Oe;function on(e){return e in mt.PDF_ROLE_TO_HTML_ROLE}G.isPdfRole=on;function et(e){return"children"in e}G.isStructTreeNode=et;function an(e){return et(e)?e.children.length===1&&0 in e.children&&"id"in e.children[0]:!1}G.isStructTreeNodeWithOnlyContentChild=an;function sn(e){const n={};if(et(e)){const{role:o}=e,t=o.match(mt.HEADING_PATTERN);if(t)n.role="heading",n["aria-level"]=Number(t[1]);else if(on(o)){const r=mt.PDF_ROLE_TO_HTML_ROLE[o];r&&(n.role=r)}}return n}G.getRoleAttributes=sn;function wt(e){const n={};if(et(e)){if(e.alt!==void 0&&(n["aria-label"]=e.alt),e.lang!==void 0&&(n.lang=e.lang),an(e)){const[o]=e.children;if(o){const t=wt(o);return Object.assign(Object.assign({},n),t)}}}else"id"in e&&(n["aria-owns"]=e.id);return n}G.getBaseAttributes=wt;function br(e){return e?Object.assign(Object.assign({},sn(e)),wt(e)):null}G.getAttributes=br;Object.defineProperty(Ot,"__esModule",{value:!0});const Vt=H,Bt=L,ut=G;function un({className:e,node:n}){const o=(0,Bt.useMemo)(()=>(0,ut.getAttributes)(n),[n]),t=(0,Bt.useMemo)(()=>!(0,ut.isStructTreeNode)(n)||(0,ut.isStructTreeNodeWithOnlyContentChild)(n)?null:n.children.map((r,s)=>(0,Vt.jsx)(un,{node:r},s)),[n]);return(0,Vt.jsx)("span",Object.assign({className:e},o,{children:t}))}Ot.default=un;var pe={},Pr=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(pe,"__esModule",{value:!0});const Er=L,jr=Pr(Ze);function Rr(){return(0,Er.useContext)(jr.default)}pe.default=Rr;var Te=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(xt,"__esModule",{value:!0});const Sr=H,ct=L,xr=Te(he),Or=Te(J),wr=Te(oe),Tr=Te(Ot),$r=Te(pe),Cr=Te(ae),Lr=q;function Dr(){const e=(0,$r.default)();(0,Or.default)(e,"Unable to find Page context.");const{onGetStructTreeError:n,onGetStructTreeSuccess:o}=e,[t,r]=(0,Cr.default)(),{value:s,error:i}=t,{customTextRenderer:g,page:a}=e;function l(){s&&o&&o(s)}function u(){i&&((0,wr.default)(!1,i.toString()),n&&n(i))}function d(){r({type:"RESET"})}(0,ct.useEffect)(d,[r,a]);function _(){if(g||!a)return;const m=(0,xr.default)(a.getStructTree()),h=m;return m.promise.then(v=>{r({type:"RESOLVE",value:v})}).catch(v=>{r({type:"REJECT",error:v})}),()=>(0,Lr.cancelRunningTask)(h)}return(0,ct.useEffect)(_,[g,a,r]),(0,ct.useEffect)(()=>{if(s!==void 0){if(s===!1){u();return}l()}},[s]),s?(0,Sr.jsx)(Tr.default,{className:"react-pdf__Page__structTree structTree",node:s}):null}xt.default=Dr;var $e=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(St,"__esModule",{value:!0});const Wt=H,Se=L,kr=$e(Zt),Ut=$e(J),Ar=$e(oe),Mr=$e(fe),Ir=$e(xt),Nr=$e(pe),Ue=q,Ht=Mr.default.AnnotationMode;function Gr(e){const n=(0,Nr.default)();(0,Ut.default)(n,"Unable to find Page context.");const o=Object.assign(Object.assign({},n),e),{_className:t,canvasBackground:r,devicePixelRatio:s=(0,Ue.getDevicePixelRatio)(),onRenderError:i,onRenderSuccess:g,page:a,renderForms:l,renderTextLayer:u,rotate:d,scale:_}=o,{canvasRef:m}=e;(0,Ut.default)(a,"Attempted to render page canvas, but no page was specified.");const h=(0,Se.useRef)(null);function v(){a&&g&&g((0,Ue.makePageCallback)(a,_))}function b(p){(0,Ue.isCancelException)(p)||((0,Ar.default)(!1,p.toString()),i&&i(p))}const R=(0,Se.useMemo)(()=>a.getViewport({scale:_*s,rotation:d}),[s,a,d,_]),O=(0,Se.useMemo)(()=>a.getViewport({scale:_,rotation:d}),[a,d,_]);function c(){if(!a)return;a.cleanup();const{current:p}=h;if(!p)return;p.width=R.width,p.height=R.height,p.style.width=`${Math.floor(O.width)}px`,p.style.height=`${Math.floor(O.height)}px`,p.style.visibility="hidden";const E={annotationMode:l?Ht.ENABLE_FORMS:Ht.ENABLE,canvasContext:p.getContext("2d",{alpha:!1}),viewport:R};r&&(E.background=r);const $=a.render(E),D=$;return $.promise.then(()=>{p.style.visibility="",v()}).catch(b),()=>(0,Ue.cancelRunningTask)(D)}(0,Se.useEffect)(c,[r,h,s,a,l,R,O]);const P=(0,Se.useCallback)(()=>{const{current:p}=h;p&&(p.width=0,p.height=0)},[h]);return(0,Se.useEffect)(()=>P,[P]),(0,Wt.jsx)("canvas",{className:`${t}__canvas`,dir:"ltr",ref:(0,kr.default)(m,h),style:{display:"block",userSelect:"none"},children:u?(0,Wt.jsx)(Ir.default,{}):null})}St.default=Gr;var Tt={},Ce=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Tt,"__esModule",{value:!0});const Fr=H,He=L,Vr=Ce(he),Jt=Ce(J),Br=Ce(oe),Wr=Ce(fe),Ur=Ce(pe),Hr=Ce(ae),lt=q;function Jr(){const e=(0,Ur.default)();(0,Jt.default)(e,"Unable to find Page context.");const{_className:n,onRenderSuccess:o,onRenderError:t,page:r,rotate:s,scale:i}=e;(0,Jt.default)(r,"Attempted to render page SVG, but no page was specified.");const[g,a]=(0,Hr.default)(),{value:l,error:u}=g;function d(){r&&o&&o((0,lt.makePageCallback)(r,i))}function _(){u&&((0,lt.isCancelException)(u)||((0,Br.default)(!1,u.toString()),t&&t(u)))}const m=(0,He.useMemo)(()=>r.getViewport({scale:i,rotation:s}),[r,s,i]);function h(){a({type:"RESET"})}(0,He.useEffect)(h,[r,a,m]);function v(){if(!r)return;const c=(0,Vr.default)(r.getOperatorList());return c.promise.then(P=>{new Wr.default.SVGGraphics(r.commonObjs,r.objs).getSVG(P,m).then(E=>{if(!(E instanceof SVGElement))throw new Error("getSVG returned unexpected result.");a({type:"RESOLVE",value:E})}).catch(E=>{a({type:"REJECT",error:E})})}).catch(P=>{a({type:"REJECT",error:P})}),()=>(0,lt.cancelRunningTask)(c)}(0,He.useEffect)(v,[r,a,m]),(0,He.useEffect)(()=>{if(l!==void 0){if(l===!1){_();return}d()}},[l]);function b(c){if(!c||!l)return;c.firstElementChild||c.appendChild(l);const{width:P,height:p}=m;l.setAttribute("width",`${P}`),l.setAttribute("height",`${p}`)}const{width:R,height:O}=m;return(0,Fr.jsx)("div",{className:`${n}__svg`,ref:c=>b(c),style:{display:"block",backgroundColor:"white",overflow:"hidden",width:R,height:O,userSelect:"none"}})}Tt.default=Jr;var $t={},be=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty($t,"__esModule",{value:!0});const qr=H,re=L,zr=be(he),Kr=be(we),qt=be(J),ft=be(oe),Yr=be(fe),Qr=be(pe),Xr=be(ae),zt=q;function Zr(e){return"str"in e}function eo(){const e=(0,Qr.default)();(0,qt.default)(e,"Unable to find Page context.");const{customTextRenderer:n,onGetTextError:o,onGetTextSuccess:t,onRenderTextLayerError:r,onRenderTextLayerSuccess:s,page:i,pageIndex:g,pageNumber:a,rotate:l,scale:u}=e;(0,qt.default)(i,"Attempted to load page text content, but no page was specified.");const[d,_]=(0,Xr.default)(),{value:m,error:h}=d,v=(0,re.useRef)(null),b=(0,re.useRef)(void 0);(0,ft.default)(parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-text-layer"),10)===1,"TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer");function R(){m&&t&&t(m)}function O(){h&&((0,ft.default)(!1,h.toString()),o&&o(h))}function c(){_({type:"RESET"})}(0,re.useEffect)(c,[i,_]);function P(){if(!i)return;const y=(0,zr.default)(i.getTextContent()),V=y;return y.promise.then(w=>{_({type:"RESOLVE",value:w})}).catch(w=>{_({type:"REJECT",error:w})}),()=>(0,zt.cancelRunningTask)(V)}(0,re.useEffect)(P,[i,_]),(0,re.useEffect)(()=>{if(m!==void 0){if(m===!1){O();return}R()}},[m]);const p=(0,re.useCallback)(()=>{s&&s()},[s]),E=(0,re.useCallback)(y=>{(0,ft.default)(!1,y.toString()),r&&r(y)},[r]);function $(){const y=b.current;y&&y.classList.add("active")}function D(){const y=b.current;y&&y.classList.remove("active")}const M=(0,re.useMemo)(()=>i.getViewport({scale:u,rotation:l}),[i,l,u]);function I(){if(!i||!m)return;const{current:y}=v;if(!y)return;y.innerHTML="";const V=i.streamTextContent({includeMarkedContent:!0}),w={container:y,textContentSource:V,viewport:M},S=Yr.default.renderTextLayer(w),B=S;return S.promise.then(()=>{const k=document.createElement("div");k.className="endOfContent",y.append(k),b.current=k;const A=y.querySelectorAll('[role="presentation"]');if(n){let Q=0;m.items.forEach((X,ge)=>{if(!Zr(X))return;const Pe=A[Q];if(!Pe)return;const me=n(Object.assign({pageIndex:g,pageNumber:a,itemIndex:ge},X));Pe.innerHTML=me,Q+=X.str&&X.hasEOL?2:1})}p()}).catch(E),()=>(0,zt.cancelRunningTask)(B)}return(0,re.useLayoutEffect)(I,[n,E,p,i,g,a,m,M]),(0,qr.jsx)("div",{className:(0,Kr.default)("react-pdf__Page__textContent","textLayer"),onMouseUp:D,onMouseDown:$,ref:v})}$t.default=eo;var Ct={},_e=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ct,"__esModule",{value:!0});const to=H,xe=L,no=_e(he),ro=_e(we),Je=_e(J),dt=_e(oe),oo=_e(fe),ao=_e(de),so=_e(pe),io=_e(ae),uo=q;function co(){const e=(0,ao.default)(),n=(0,so.default)();(0,Je.default)(n,"Unable to find Page context.");const o=Object.assign(Object.assign({},e),n),{imageResourcesPath:t,linkService:r,onGetAnnotationsError:s,onGetAnnotationsSuccess:i,onRenderAnnotationLayerError:g,onRenderAnnotationLayerSuccess:a,page:l,pdf:u,renderForms:d,rotate:_,scale:m=1}=o;(0,Je.default)(u,"Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop."),(0,Je.default)(l,"Attempted to load page annotations, but no page was specified."),(0,Je.default)(r,"Attempted to load page annotations, but no linkService was specified.");const[h,v]=(0,io.default)(),{value:b,error:R}=h,O=(0,xe.useRef)(null);(0,dt.default)(parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-annotation-layer"),10)===1,"AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations");function c(){b&&i&&i(b)}function P(){R&&((0,dt.default)(!1,R.toString()),s&&s(R))}function p(){v({type:"RESET"})}(0,xe.useEffect)(p,[v,l]);function E(){if(!l)return;const y=(0,no.default)(l.getAnnotations()),V=y;return y.promise.then(w=>{v({type:"RESOLVE",value:w})}).catch(w=>{v({type:"REJECT",error:w})}),()=>{(0,uo.cancelRunningTask)(V)}}(0,xe.useEffect)(E,[v,l,d]),(0,xe.useEffect)(()=>{if(b!==void 0){if(b===!1){P();return}c()}},[b]);function $(){a&&a()}function D(y){(0,dt.default)(!1,`${y}`),g&&g(y)}const M=(0,xe.useMemo)(()=>l.getViewport({scale:m,rotation:_}),[l,_,m]);function I(){if(!u||!l||!r||!b)return;const{current:y}=O;if(!y)return;const V=M.clone({dontFlip:!0}),w={accessibilityManager:null,annotationCanvasMap:null,div:y,l10n:null,page:l,viewport:V},S={annotations:b,annotationStorage:u.annotationStorage,div:y,downloadManager:null,imageResourcesPath:t,linkService:r,page:l,renderForms:d,viewport:V};y.innerHTML="";try{new oo.default.AnnotationLayer(w).render(S),$()}catch(B){D(B)}return()=>{}}return(0,xe.useEffect)(I,[b,t,r,l,d,M]),(0,to.jsx)("div",{className:(0,ro.default)("react-pdf__Page__annotations","annotationLayer"),ref:O})}Ct.default=co;var lo=f&&f.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o},F=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Xe,"__esModule",{value:!0});const ee=H,le=L,fo=F(he),po=F(yt),_o=F(we),go=F(Zt),Kt=F(J),mo=F(oe),vo=F(Ze),pt=F(ze),yo=F(St),ho=F(Tt),bo=F($t),Po=F(Ct),te=q,Eo=F(de),jo=F(ae),Yt=1;function Ro(e){const n=(0,Eo.default)(),o=Object.assign(Object.assign({},n),e),{_className:t="react-pdf__Page",_enableRegisterUnregisterPage:r=!0,canvasBackground:s,canvasRef:i,children:g,className:a,customRenderer:l,customTextRenderer:u,devicePixelRatio:d,error:_="Failed to load the page.",height:m,inputRef:h,loading:v="Loading page…",noData:b="No page specified.",onGetAnnotationsError:R,onGetAnnotationsSuccess:O,onGetStructTreeError:c,onGetStructTreeSuccess:P,onGetTextError:p,onGetTextSuccess:E,onLoadError:$,onLoadSuccess:D,onRenderAnnotationLayerError:M,onRenderAnnotationLayerSuccess:I,onRenderError:y,onRenderSuccess:V,onRenderTextLayerError:w,onRenderTextLayerSuccess:S,pageIndex:B,pageNumber:k,pdf:A,registerPage:Q,renderAnnotationLayer:X=!0,renderForms:ge=!1,renderMode:Pe="canvas",renderTextLayer:me=!0,rotate:Le,scale:ve=Yt,unregisterPage:De,width:Ee}=o,Fe=lo(o,["_className","_enableRegisterUnregisterPage","canvasBackground","canvasRef","children","className","customRenderer","customTextRenderer","devicePixelRatio","error","height","inputRef","loading","noData","onGetAnnotationsError","onGetAnnotationsSuccess","onGetStructTreeError","onGetStructTreeSuccess","onGetTextError","onGetTextSuccess","onLoadError","onLoadSuccess","onRenderAnnotationLayerError","onRenderAnnotationLayerSuccess","onRenderError","onRenderSuccess","onRenderTextLayerError","onRenderTextLayerSuccess","pageIndex","pageNumber","pdf","registerPage","renderAnnotationLayer","renderForms","renderMode","renderTextLayer","rotate","scale","unregisterPage","width"]),[nt,ye]=(0,jo.default)(),{value:x,error:ke}=nt,Ae=(0,le.useRef)(null);(0,Kt.default)(A,"Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.");const N=(0,te.isProvided)(k)?k-1:B??null,Z=k??((0,te.isProvided)(B)?B+1:null),ne=Le??(x?x.rotate:null),j=(0,le.useMemo)(()=>{if(!x)return null;let je=1;const rt=ve??Yt;if(Ee||m){const Re=x.getViewport({scale:1,rotation:ne});Ee?je=Ee/Re.width:m&&(je=m/Re.height)}return rt*je},[m,x,ne,ve,Ee]);function W(){return()=>{(0,te.isProvided)(N)&&r&&De&&De(N)}}(0,le.useEffect)(W,[r,A,N,De]);function z(){if(D){if(!x||!j)return;D((0,te.makePageCallback)(x,j))}if(r&&Q){if(!(0,te.isProvided)(N)||!Ae.current)return;Q(N,Ae.current)}}function ue(){ke&&((0,mo.default)(!1,ke.toString()),$&&$(ke))}function ce(){ye({type:"RESET"})}(0,le.useEffect)(ce,[ye,A,N]);function ln(){if(!A||!Z)return;const je=(0,fo.default)(A.getPage(Z)),rt=je;return je.promise.then(Re=>{ye({type:"RESOLVE",value:Re})}).catch(Re=>{ye({type:"REJECT",error:Re})}),()=>(0,te.cancelRunningTask)(rt)}(0,le.useEffect)(ln,[ye,A,N,Z,Q]),(0,le.useEffect)(()=>{if(x!==void 0){if(x===!1){ue();return}z()}},[x,j]);const fn=(0,le.useMemo)(()=>x&&(0,te.isProvided)(N)&&Z&&(0,te.isProvided)(ne)&&(0,te.isProvided)(j)?{_className:t,canvasBackground:s,customTextRenderer:u,devicePixelRatio:d,onGetAnnotationsError:R,onGetAnnotationsSuccess:O,onGetStructTreeError:c,onGetStructTreeSuccess:P,onGetTextError:p,onGetTextSuccess:E,onRenderAnnotationLayerError:M,onRenderAnnotationLayerSuccess:I,onRenderError:y,onRenderSuccess:V,onRenderTextLayerError:w,onRenderTextLayerSuccess:S,page:x,pageIndex:N,pageNumber:Z,renderForms:ge,renderTextLayer:me,rotate:ne,scale:j}:null,[t,s,u,d,R,O,c,P,p,E,M,I,y,V,w,S,x,N,Z,ge,me,ne,j]),dn=(0,le.useMemo)(()=>(0,po.default)(Fe,()=>x&&(j?(0,te.makePageCallback)(x,j):void 0)),[Fe,x,j]),Ve=`${N}@${j}/${ne}`,pn=`${N}/${ne}`;function _n(){switch(Pe){case"custom":return(0,Kt.default)(l,'renderMode was set to "custom", but no customRenderer was passed.'),(0,ee.jsx)(l,{},`${Ve}_custom`);case"none":return null;case"svg":return(0,ee.jsx)(ho.default,{},`${pn}_svg`);case"canvas":default:return(0,ee.jsx)(yo.default,{canvasRef:i},`${Ve}_canvas`)}}function gn(){return me?(0,ee.jsx)(bo.default,{},`${Ve}_text`):null}function mn(){return X?(0,ee.jsx)(Po.default,{},`${Ve}_annotations`):null}function vn(){return(0,ee.jsxs)(vo.default.Provider,{value:fn,children:[_n(),gn(),mn(),g]})}function yn(){return Z?A===null||x===void 0||x===null?(0,ee.jsx)(pt.default,{type:"loading",children:typeof v=="function"?v():v}):A===!1||x===!1?(0,ee.jsx)(pt.default,{type:"error",children:typeof _=="function"?_():_}):vn():(0,ee.jsx)(pt.default,{type:"no-data",children:typeof b=="function"?b():b})}return(0,ee.jsx)("div",Object.assign({className:(0,_o.default)(t,a),"data-page-number":Z,ref:(0,go.default)(h,Ae),style:{"--scale-factor":`${j}`,backgroundColor:s||"white",position:"relative",minWidth:"min-content",minHeight:"min-content"}},dn,{children:yn()}))}Xe.default=Ro;var Lt={},So=f&&f.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o},tt=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Lt,"__esModule",{value:!0});const Qt=H,xo=tt(we),Xt=tt(J),Oo=tt(Xe),_t=q,wo=tt(de);function To(e){const n=(0,wo.default)(),o=Object.assign(Object.assign({},n),e),{className:t,linkService:r,onItemClick:s,pageIndex:i,pageNumber:g,pdf:a}=o;(0,Xt.default)(a,"Attempted to load a thumbnail, but no document was specified. Wrap <Thumbnail /> in a <Document /> or pass explicit `pdf` prop.");const l=(0,_t.isProvided)(g)?g-1:i??null,u=g??((0,_t.isProvided)(i)?i+1:null);function d(m){m.preventDefault(),!(!(0,_t.isProvided)(l)||!u)&&((0,Xt.default)(s||r,"Either onItemClick callback or linkService must be defined in order to navigate to an outline item."),s?s({pageIndex:l,pageNumber:u}):r&&r.goToPage(u))}const _=So(e,["className","onItemClick"]);return(0,Qt.jsx)("a",{className:(0,xo.default)("react-pdf__Thumbnail",t),href:u?"#":void 0,onClick:d,children:(0,Qt.jsx)(Oo.default,Object.assign({},_,{_className:"react-pdf__Thumbnail__page",_enableRegisterUnregisterPage:!1,renderAnnotationLayer:!1,renderTextLayer:!1}))})}Lt.default=To;var ie=f&&f.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(T,"__esModule",{value:!0});T.PasswordResponses=T.usePageContext=T.useOutlineContext=T.useDocumentContext=T.Thumbnail=T.Page=T.Outline=T.Document=T.pdfjs=void 0;const cn=ie(fe);T.pdfjs=cn.default;const $o=ie(ht);T.Document=$o.default;const Co=ie(Pt);T.Outline=Co.default;const Lo=ie(Xe);T.Page=Lo.default;const Do=ie(Lt);T.Thumbnail=Do.default;const ko=ie(de);T.useDocumentContext=ko.default;const Ao=ie(Qe);T.useOutlineContext=Ao.default;const Mo=ie(pe);T.usePageContext=Mo.default;const Io=ie(Ke);T.PasswordResponses=Io.default;const No=q;(0,No.displayWorkerWarning)();cn.default.GlobalWorkerOptions.workerSrc="pdf.worker.js";var Go=f&&f.__awaiter||function(e,n,o,t){function r(s){return s instanceof o?s:new o(function(i){i(s)})}return new(o||(o=Promise))(function(s,i){function g(u){try{l(t.next(u))}catch(d){i(d)}}function a(u){try{l(t.throw(u))}catch(d){i(d)}}function l(u){u.done?s(u.value):r(u.value).then(g,a)}l((t=t.apply(e,n||[])).next())})};Object.defineProperty(en,"__esModule",{value:!0});const vt=T;vt.pdfjs.GlobalWorkerOptions.workerSrc=`//cdnjs.cloudflare.com/ajax/libs/pdf.js/${vt.pdfjs.version}/pdf.worker.js`;const Fo=e=>Go(void 0,void 0,void 0,function*(){const n=URL.createObjectURL(e),o=vt.pdfjs.getDocument(n);let t="",r=!1;try{const s=yield o.promise,i=s.numPages;for(let g=1;g<=i;g++){const u=(yield(yield s.getPage(g)).getTextContent()).items.map(d=>"str"in d?d.str:"").join(" ");t+=u}}catch(s){r=!0,console.error("Error extracting text from PDF:",s)}if(URL.revokeObjectURL(n),o.destroy(),!r)return t});var qo=en.default=Fo;export{qo as _};
