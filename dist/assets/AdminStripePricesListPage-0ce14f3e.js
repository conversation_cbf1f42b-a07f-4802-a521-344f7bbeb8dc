import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as s,f as ae}from"./vendor-2ae44a2e.js";import{M as re,G as ie,A as ne,B as oe,a as le,b as ce,R as de,c as pe,p as xe,E as me,t as ue}from"./index-b2ff2fa1.js";import{o as he}from"./yup-5abd4662.js";import{u as ge}from"./react-hook-form-47c010f8.js";import{c as ye,a as c}from"./yup-5c93ed04.js";import{P as fe}from"./index-132fbad2.js";import{A as je}from"./index-e429b426.js";import{S as be}from"./index-a74110af.js";import{M as T}from"./index-9aa09a5c.js";import{X as ve}from"./lucide-react-1246a7ed.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";let we=new re;const j=[{header:"Stripe Id",accessor:"stripe_id"},{header:"Product",accessor:"product_name"},{header:"Nickname",accessor:"name"},{header:"Type",accessor:"type",mapping:{one_time:"One Time",recurring:"Recurring",lifetime:"Lifetime"}},{header:"Price",accessor:"amount"},{header:"Trial",accessor:"trial_days"},{header:"Status",accessor:"status",mapping:{0:"Archived",1:"Active"}},{header:"Action",accessor:""}],Ye=()=>{const{dispatch:_}=s.useContext(ie),{dispatch:R}=s.useContext(ne),[b,D]=s.useState([]),[o,v]=s.useState(10),[w,M]=s.useState(0),[Ne,O]=s.useState(0),[d,$]=s.useState(0),[q,B]=s.useState(!1),[I,z]=s.useState(!1),[N,L]=s.useState(!1),[S,C]=s.useState(!1),[n,p]=s.useState([]),[G,x]=s.useState([]),[H,K]=s.useState(""),[P,V]=s.useState("eq"),[A,F]=s.useState(!0),[X,m]=s.useState(!1),[J,u]=s.useState(!1),[Q,U]=s.useState();ae();const W=ye({stripe_id:c(),name:c(),status:c(),product_name:c(),amount:c(),type:c()}),{register:Se,handleSubmit:Y,formState:{errors:Ce}}=ge({resolver:he(W)});function Z(t){(async function(){v(t),await l(1,t)})()}function ee(){(async function(){await l(d-1>0?d-1:0,o)})()}function te(){(async function(){await l(d+1<=w?d+1:0,o)})()}const E=(t,a,r)=>{const i=a==="eq"&&isNaN(r)?`"${r}"`:r,g=`${t},${a},${i}`;x(y=>[...y.filter(h=>!h.includes(t)),g]),K(r)};async function l(t,a,r){F(!0);try{const i=await we.getStripePrices({page:t,limit:a},r),{list:g,total:y,limit:k,num_pages:h,page:f}=i;D(g),v(+k),M(+h),$(+f),O(+y),B(+f>1),z(+f+1<=+h)}catch(i){console.log("ERROR",i),ue(R,i.message)}F(!1)}const se=t=>{l(1,o,{})};return s.useEffect(()=>{_({type:"SETPATH",payload:{path:"prices"}});const a=setTimeout(async()=>{await l(1,o)},700);return()=>{clearTimeout(a)}},[H,G,P]),e.jsxs("div",{className:"min-h-screen bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between px-8 py-6",children:[e.jsxs("form",{className:"relative",onSubmit:Y(se),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 hover:border-gray-300",onClick:()=>L(!N),children:[e.jsx(oe,{}),e.jsx("span",{children:"Filters"}),n.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-white",children:n.length})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 focus-within:border-gray-300",children:[e.jsx(le,{className:"text-xl text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search",className:"border-none p-0 placeholder:text-gray-400 focus:outline-none",style:{boxShadow:"none"},onInput:t=>{var a;return E("name","cs",(a=t.target)==null?void 0:a.value)}}),e.jsx(ce,{className:"text-lg text-gray-400 hover:text-gray-600"})]})]}),N&&e.jsxs("div",{className:"absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-5 shadow-lg",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-700",children:"Filters"}),e.jsx(ve,{onClick:()=>{p([]),x([]),setFilterValues({})},className:"cursor-pointer text-lg text-gray-400 hover:text-gray-600"})]}),n==null?void 0:n.map((t,a)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:"w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0",onChange:r=>{V(r.target.value)},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",onChange:r=>E(t,P,r.target.value)}),e.jsx(de,{className:"cursor-pointer text-2xl text-red-500 hover:text-red-600",onClick:()=>{p(r=>r.filter(i=>i!==t)),x(r=>r.filter(i=>!i.includes(t)))}})]},a)),e.jsxs("div",{className:"relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"flex cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 text-gray-600 hover:bg-gray-200",onClick:()=>{C(!S)},children:[e.jsx(pe,{}),"Add filter"]}),S&&e.jsx("div",{className:"absolute top-11 z-10 w-48 rounded-md border border-gray-200 bg-white py-2 shadow-lg",children:e.jsx("ul",{className:"flex flex-col",children:j.map(t=>e.jsx("li",{className:`px-4 py-2 ${n.includes(t.header)?"cursor-not-allowed text-gray-700":"cursor-pointer text-gray-400 hover:bg-gray-50"}`,onClick:()=>{n.includes(t.header)||p(a=>[...a,t.header]),C(!1)},children:t.header},t.header))})}),n.length>0&&e.jsx("button",{type:"button",onClick:()=>{p([]),x([])},className:"py-2 pl-4 text-gray-600 hover:text-gray-800",children:"Clear all filter"})]})]})]}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsx(je,{onClick:()=>{u(!0)},showChildren:!0,children:"Add New"})})]}),A?e.jsx(be,{}):e.jsx("div",{className:"px-8",children:e.jsxs("div",{className:"overflow-x-auto rounded-lg border border-gray-200 bg-white shadow",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:j.map((t,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:b.map((t,a)=>{var r;return e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:t.stripe_id}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:t.product_name}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:t.name}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:((r=j.find(i=>i.accessor==="type"))==null?void 0:r.mapping[t.type])||t.type}),e.jsxs("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:["$",t.amount]}),e.jsxs("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:[t.trial_days," days"]}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm",children:t.status===1?e.jsx("span",{className:"rounded-full bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:"Active"}):e.jsx("span",{className:"rounded-full bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:"Archived"})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{U(t.id),m(!0)},children:"Edit"})})]},a)})})]}),!A&&b.length===0&&e.jsx("div",{className:"px-6 py-4 text-center text-sm text-gray-500",children:"No prices found"})]})}),e.jsx("div",{className:"px-8 py-4",children:e.jsx(fe,{currentPage:d,pageCount:w,pageSize:o,canPreviousPage:q,canNextPage:I,updatePageSize:Z,previousPage:ee,nextPage:te})}),e.jsx(T,{isModalActive:J,closeModalFn:()=>u(!1),children:e.jsx(xe,{setSidebar:u,closeSidebar:()=>{u(!1),l(1,o)}})}),e.jsx(T,{isModalActive:X,closeModalFn:()=>m(!1),children:e.jsx(me,{activeId:Q,setSidebar:m,closeSidebar:()=>{m(!1),l(1,o)}})})]})};export{Ye as default};
