import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as y,r as N,u as F,f as G,L}from"./vendor-2ae44a2e.js";import{u as q}from"./react-hook-form-47c010f8.js";import{o as $}from"./yup-5abd4662.js";import{c as D,a as A}from"./yup-5c93ed04.js";import{M as O,A as _,G as K,s as S}from"./index-b2ff2fa1.js";import{I as T}from"./InteractiveButton-bff38983.js";import{L as U}from"./login-new-bg-eb709951.js";import{a as V}from"./automate-icon-06435bcb.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";let W=new O;const Ce=()=>{var c,d;const k=D({email:A().email().required(),password:A().required()}).required(),{dispatch:R}=y.useContext(_),{dispatch:l}=y.useContext(K),[n,a]=N.useState(!1),[o,I]=N.useState(!1),M=F(),P=new URLSearchParams(M.search).get("redirect_uri"),B=G(),{register:r,handleSubmit:E,setError:m,formState:{errors:t}}=q({resolver:$(k)}),Z=async x=>{var p,u,h,f,g,w,b,j;try{a(!0);const s=await W.login(x.email,x.password,"admin");if(!s.error)R({type:"LOGIN",payload:s}),S(l,"Succesfully Logged In",4e3,"success"),B(P??"/admin/customers");else if(a(!1),s.validation){const C=Object.keys(s.validation);for(let i=0;i<C.length;i++){const v=C[i];m(v,{type:"manual",message:s.validation[v]})}}}catch(s){a(!1),S(l,(u=(p=s==null?void 0:s.response)==null?void 0:p.data)!=null&&u.message?(f=(h=s==null?void 0:s.response)==null?void 0:h.data)==null?void 0:f.message:s==null?void 0:s.message,4e3,"error"),console.log("Error",s),m("email",{type:"manual",message:(w=(g=s==null?void 0:s.response)==null?void 0:g.data)!=null&&w.message?(j=(b=s==null?void 0:s.response)==null?void 0:b.data)==null?void 0:j.message:s==null?void 0:s.message})}};return e.jsxs("main",{className:"min-h-screen bg-[cover] bg-no-repeat",style:{backgroundImage:`url(${U})`},children:[e.jsx("nav",{className:"flex min-h-[50px] items-center justify-between border-b border-b-[#C6C6C6] bg-white px-6 py-2",children:e.jsx(L,{to:"/",className:"text-xl font-semibold",children:"AutomateIntel - Voice"})}),e.jsx("div",{className:"flex flex-col justify-center items-center min-h-full",children:e.jsxs("div",{className:"my-12 flex w-[50%] flex-col items-center rounded-lg border border-[#a8a8a8] p-4  shadow-md",children:[e.jsx("img",{style:{width:"30px",height:"30px"},src:V}),e.jsx("div",{className:"my-2 text-xl font-semibold text-[#262626]",children:"Welcome Back"}),e.jsx("div",{className:"oauth flex w-full max-w-md grow flex-col gap-4 px-6 text-[#344054]"}),e.jsxs("form",{className:"admin-input min-w-[70%]",onSubmit:E(Z),children:[e.jsxs("div",{className:"flex flex-col mb-6 text-sm",children:[e.jsx("label",{htmlFor:"",children:"Email"}),e.jsx("input",{name:"admin",className:"rounded-md border border-[#c6c6c6] bg-transparent px-3 py-2 text-[#525252]",type:"text",placeholder:"<EMAIL>",...r("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(c=t==null?void 0:t.email)==null?void 0:c.message})]}),e.jsxs("div",{className:"flex flex-col text-sm",children:[e.jsx("label",{htmlFor:"",children:"Password"}),e.jsxs("div",{className:"flex items-center rounded-md border border-[#c6c6c6] bg-transparent px-2 py-1 text-[#525252]",children:[e.jsx("input",{name:"admin",className:"focus-visible::outline-none w-[95%] border-none bg-transparent p-1 shadow-[0] outline-none focus:border-none focus:shadow-none focus:outline-none",type:o?"text":"password",placeholder:"********",...r("password"),style:{boxShadow:"0 0 transparent"}}),e.jsx("span",{className:"w-[5%] cursor-pointer",onClick:()=>I(!o),children:o?e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.99998 3.33337C13.5326 3.33335 16.9489 5.50937 19.0735 9.61715L19.2715 10L19.0735 10.3828C16.9489 14.4906 13.5326 16.6667 10 16.6667C6.46737 16.6667 3.05113 14.4907 0.926472 10.3829L0.728455 10.0001L0.926472 9.61724C3.05113 5.50946 6.46736 3.3334 9.99998 3.33337ZM7.08333 10C7.08333 8.38921 8.38917 7.08337 10 7.08337C11.6108 7.08337 12.9167 8.38921 12.9167 10C12.9167 11.6109 11.6108 12.9167 10 12.9167C8.38917 12.9167 7.08333 11.6109 7.08333 10Z",fill:"#A8A8A8"})}):e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.28033 2.21967C2.98744 1.92678 2.51256 1.92678 2.21967 2.21967C1.92678 2.51256 1.92678 2.98744 2.21967 3.28033L5.38733 6.44799C4.04329 7.533 2.8302 8.97021 1.81768 10.7471C1.37472 11.5245 1.37667 12.4782 1.81881 13.2539C3.74678 16.6364 6.40456 18.789 9.29444 19.6169C12.0009 20.3923 14.8469 19.9857 17.3701 18.4308L20.7197 21.7803C21.0126 22.0732 21.4874 22.0732 21.7803 21.7803C22.0732 21.4874 22.0732 21.0126 21.7803 20.7197L3.28033 2.21967ZM14.2475 15.3082L13.1559 14.2166C12.81 14.3975 12.4167 14.4995 11.9991 14.4995C10.6184 14.4995 9.49911 13.3802 9.49911 11.9995C9.49911 11.5819 9.60116 11.1886 9.78207 10.8427L8.69048 9.75114C8.25449 10.3917 7.99911 11.1662 7.99911 11.9995C7.99911 14.2087 9.78998 15.9995 11.9991 15.9995C12.8324 15.9995 13.6069 15.7441 14.2475 15.3082Z",fill:"#A8A8A8"}),e.jsx("path",{d:"M19.7234 16.5416C20.5189 15.7335 21.2556 14.7869 21.9145 13.7052C22.5512 12.66 22.5512 11.34 21.9145 10.2948C19.3961 6.16075 15.7432 4.00003 11.9999 4C10.6454 3.99999 9.30281 4.28286 8.02148 4.83974L19.7234 16.5416Z",fill:"#A8A8A8"})]})})]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(d=t==null?void 0:t.password)==null?void 0:d.message})]}),e.jsxs("div",{className:"flex justify-between my-2 text-sm",children:[e.jsxs("div",{className:"flex items-center text-[#525252]",children:[e.jsx("input",{className:"mr-2",type:"checkbox"}),"Remember me"]}),e.jsx(L,{to:"/admin/forgot",className:"text-[black]",children:"Forgot password"})]}),e.jsx(T,{type:"submit",className:"flex justify-center items-center py-2 my-12 w-full tracking-wide text-white rounded-md outline-none bg-[black] focus:outline-none",loading:n,disabled:n,children:e.jsx("span",{children:"Sign in"})})]})]})})]})};export{Ce as default};
