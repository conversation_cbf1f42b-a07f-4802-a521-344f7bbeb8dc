import{r as i,R as P,a as Te,b as nn}from"../vendor-2ae44a2e.js";var rn=Object.defineProperty,on=(e,t,n)=>t in e?rn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Ge=(e,t,n)=>(on(e,typeof t!="symbol"?t+"":t,n),n);let ln=class{constructor(){Ge(this,"current",this.detect()),Ge(this,"handoffState","pending"),Ge(this,"currentId",0)}set(t){this.current!==t&&(this.handoffState="pending",this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},ue=new ln,H=(e,t)=>{ue.isServer?i.useEffect(e,t):i.useLayoutEffect(e,t)};function Y(e){let t=i.useRef(e);return H(()=>{t.current=e},[e]),t}let y=function(e){let t=Y(e);return P.useCallback((...n)=>t.current(...n),[t])};function xe(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(t=>setTimeout(()=>{throw t}))}function te(){let e=[],t={addEventListener(n,r,l,o){return n.addEventListener(r,l,o),t.add(()=>n.removeEventListener(r,l,o))},requestAnimationFrame(...n){let r=requestAnimationFrame(...n);return t.add(()=>cancelAnimationFrame(r))},nextFrame(...n){return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(...n){let r=setTimeout(...n);return t.add(()=>clearTimeout(r))},microTask(...n){let r={current:!0};return xe(()=>{r.current&&n[0]()}),t.add(()=>{r.current=!1})},style(n,r,l){let o=n.style.getPropertyValue(r);return Object.assign(n.style,{[r]:l}),this.add(()=>{Object.assign(n.style,{[r]:o})})},group(n){let r=te();return n(r),this.add(()=>r.dispose())},add(n){return e.push(n),()=>{let r=e.indexOf(n);if(r>=0)for(let l of e.splice(r,1))l()}},dispose(){for(let n of e.splice(0))n()}};return t}function Ie(){let[e]=i.useState(te);return i.useEffect(()=>()=>e.dispose(),[e]),e}function an(){let e=typeof document>"u";return"useSyncExternalStore"in Te?(t=>t.useSyncExternalStore)(Te)(()=>()=>{},()=>!1,()=>!e):!1}function Ee(){let e=an(),[t,n]=i.useState(ue.isHandoffComplete);return t&&ue.isHandoffComplete===!1&&n(!1),i.useEffect(()=>{t!==!0&&n(!0)},[t]),i.useEffect(()=>ue.handoff(),[]),e?!1:t}var wt;let K=(wt=P.useId)!=null?wt:function(){let e=Ee(),[t,n]=P.useState(e?()=>ue.nextId():null);return H(()=>{t===null&&n(ue.nextId())},[t]),t!=null?""+t:void 0};function L(e,t,...n){if(e in t){let l=t[e];return typeof l=="function"?l(...n):l}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(l=>`"${l}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,L),r}function ye(e){return ue.isServer?null:e instanceof Node?e.ownerDocument:e!=null&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let Je=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var O=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(O||{}),ae=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(ae||{}),un=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(un||{});function Re(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Je)).sort((t,n)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(n.tabIndex||Number.MAX_SAFE_INTEGER)))}var ke=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(ke||{});function Ne(e,t=0){var n;return e===((n=ye(e))==null?void 0:n.body)?!1:L(t,{0(){return e.matches(Je)},1(){let r=e;for(;r!==null;){if(r.matches(Je))return!0;r=r.parentElement}return!1}})}function Ft(e){let t=ye(e);te().nextFrame(()=>{t&&!Ne(t.activeElement,0)&&me(e)})}var sn=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(sn||{});typeof window<"u"&&typeof document<"u"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function me(e){e==null||e.focus({preventScroll:!0})}let cn=["textarea","input"].join(",");function dn(e){var t,n;return(n=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,cn))!=null?n:!1}function ge(e,t=n=>n){return e.slice().sort((n,r)=>{let l=t(n),o=t(r);if(l===null||o===null)return 0;let a=l.compareDocumentPosition(o);return a&Node.DOCUMENT_POSITION_FOLLOWING?-1:a&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function fn(e,t){return G(Re(),t,{relativeTo:e})}function G(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:l=[]}={}){let o=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,a=Array.isArray(e)?n?ge(e):e:Re(e);l.length>0&&a.length>1&&(a=a.filter(h=>!l.includes(h))),r=r??o.activeElement;let u=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),s=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,a.indexOf(r))-1;if(t&4)return Math.max(0,a.indexOf(r))+1;if(t&8)return a.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=t&32?{preventScroll:!0}:{},c=0,f=a.length,p;do{if(c>=f||c+f<=0)return 0;let h=s+c;if(t&16)h=(h+f)%f;else{if(h<0)return 3;if(h>=f)return 1}p=a[h],p==null||p.focus(d),c+=u}while(p!==o.activeElement);return t&6&&dn(p)&&p.select(),2}function Ct(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function pn(){return/Android/gi.test(window.navigator.userAgent)}function mn(){return Ct()||pn()}function De(e,t,n){let r=Y(t);i.useEffect(()=>{function l(o){r.current(o)}return document.addEventListener(e,l,n),()=>document.removeEventListener(e,l,n)},[e,n])}function Dt(e,t,n){let r=Y(t);i.useEffect(()=>{function l(o){r.current(o)}return window.addEventListener(e,l,n),()=>window.removeEventListener(e,l,n)},[e,n])}function ot(e,t,n=!0){let r=i.useRef(!1);i.useEffect(()=>{requestAnimationFrame(()=>{r.current=n})},[n]);function l(a,u){if(!r.current||a.defaultPrevented)return;let s=u(a);if(s===null||!s.getRootNode().contains(s)||!s.isConnected)return;let d=function c(f){return typeof f=="function"?c(f()):Array.isArray(f)||f instanceof Set?f:[f]}(e);for(let c of d){if(c===null)continue;let f=c instanceof HTMLElement?c:c.current;if(f!=null&&f.contains(s)||a.composed&&a.composedPath().includes(f))return}return!Ne(s,ke.Loose)&&s.tabIndex!==-1&&a.preventDefault(),t(a,s)}let o=i.useRef(null);De("pointerdown",a=>{var u,s;r.current&&(o.current=((s=(u=a.composedPath)==null?void 0:u.call(a))==null?void 0:s[0])||a.target)},!0),De("mousedown",a=>{var u,s;r.current&&(o.current=((s=(u=a.composedPath)==null?void 0:u.call(a))==null?void 0:s[0])||a.target)},!0),De("click",a=>{mn()||o.current&&(l(a,()=>o.current),o.current=null)},!0),De("touchend",a=>l(a,()=>a.target instanceof HTMLElement?a.target:null),!0),Dt("blur",a=>l(a,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function ce(...e){return i.useMemo(()=>ye(...e),[...e])}function Pt(e){var t;if(e.type)return e.type;let n=(t=e.as)!=null?t:"button";if(typeof n=="string"&&n.toLowerCase()==="button")return"button"}function lt(e,t){let[n,r]=i.useState(()=>Pt(e));return H(()=>{r(Pt(e))},[e.type,e.as]),H(()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&r("button")},[n,t]),n}let Mt=Symbol();function Lt(e,t=!0){return Object.assign(e,{[Mt]:t})}function N(...e){let t=i.useRef(e);i.useEffect(()=>{t.current=e},[e]);let n=y(r=>{for(let l of t.current)l!=null&&(typeof l=="function"?l(r):l.current=r)});return e.every(r=>r==null||(r==null?void 0:r[Mt]))?void 0:n}function St(e){return[e.screenX,e.screenY]}function vn(){let e=i.useRef([-1,-1]);return{wasMoved(t){let n=St(t);return e.current[0]===n[0]&&e.current[1]===n[1]?!1:(e.current=n,!0)},update(t){e.current=St(t)}}}function gn({container:e,accept:t,walk:n,enabled:r=!0}){let l=i.useRef(t),o=i.useRef(n);i.useEffect(()=>{l.current=t,o.current=n},[t,n]),H(()=>{if(!e||!r)return;let a=ye(e);if(!a)return;let u=l.current,s=o.current,d=Object.assign(f=>u(f),{acceptNode:u}),c=a.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,d,!1);for(;c.nextNode();)s(c.currentNode)},[e,r,l,o])}function at(e,t){let n=i.useRef([]),r=y(e);i.useEffect(()=>{let l=[...n.current];for(let[o,a]of t.entries())if(n.current[o]!==a){let u=r(t,l);return n.current=t,u}},[r,...t])}function Le(...e){return Array.from(new Set(e.flatMap(t=>typeof t=="string"?t.split(" "):[]))).filter(Boolean).join(" ")}var J=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(J||{}),pe=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(pe||{});function A({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:l,visible:o=!0,name:a,mergeRefs:u}){u=u??bn;let s=Ot(t,e);if(o)return Me(s,n,r,a,u);let d=l??0;if(d&2){let{static:c=!1,...f}=s;if(c)return Me(f,n,r,a,u)}if(d&1){let{unmount:c=!0,...f}=s;return L(c?0:1,{0(){return null},1(){return Me({...f,hidden:!0,style:{display:"none"}},n,r,a,u)}})}return Me(s,n,r,a,u)}function Me(e,t={},n,r,l){let{as:o=n,children:a,refName:u="ref",...s}=Ke(e,["unmount","static"]),d=e.ref!==void 0?{[u]:e.ref}:{},c=typeof a=="function"?a(t):a;"className"in s&&s.className&&typeof s.className=="function"&&(s.className=s.className(t));let f={};if(t){let p=!1,h=[];for(let[g,v]of Object.entries(t))typeof v=="boolean"&&(p=!0),v===!0&&h.push(g);p&&(f["data-headlessui-state"]=h.join(" "))}if(o===i.Fragment&&Object.keys($t(s)).length>0){if(!i.isValidElement(c)||Array.isArray(c)&&c.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(s).map(v=>`  - ${v}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(v=>`  - ${v}`).join(`
`)].join(`
`));let p=c.props,h=typeof(p==null?void 0:p.className)=="function"?(...v)=>Le(p==null?void 0:p.className(...v),s.className):Le(p==null?void 0:p.className,s.className),g=h?{className:h}:{};return i.cloneElement(c,Object.assign({},Ot(c.props,$t(Ke(s,["ref"]))),f,d,{ref:l(c.ref,d.ref)},g))}return i.createElement(o,Object.assign({},Ke(s,["ref"]),o!==i.Fragment&&d,o!==i.Fragment&&f),c)}function hn(){let e=i.useRef([]),t=i.useCallback(n=>{for(let r of e.current)r!=null&&(typeof r=="function"?r(n):r.current=n)},[]);return(...n)=>{if(!n.every(r=>r==null))return e.current=n,t}}function bn(...e){return e.every(t=>t==null)?void 0:t=>{for(let n of e)n!=null&&(typeof n=="function"?n(t):n.current=t)}}function Ot(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let r of e)for(let l in r)l.startsWith("on")&&typeof r[l]=="function"?(n[l]!=null||(n[l]=[]),n[l].push(r[l])):t[l]=r[l];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(r=>[r,void 0])));for(let r in n)Object.assign(t,{[r](l,...o){let a=n[r];for(let u of a){if((l instanceof Event||(l==null?void 0:l.nativeEvent)instanceof Event)&&l.defaultPrevented)return;u(l,...o)}}});return t}function k(e){var t;return Object.assign(i.forwardRef(e),{displayName:(t=e.displayName)!=null?t:e.name})}function $t(e){let t=Object.assign({},e);for(let n in t)t[n]===void 0&&delete t[n];return t}function Ke(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}let En="div";var ie=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(ie||{});function yn(e,t){var n;let{features:r=1,...l}=e,o={ref:t,"aria-hidden":(r&2)===2?!0:(n=l["aria-hidden"])!=null?n:void 0,hidden:(r&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(r&4)===4&&(r&2)!==2&&{display:"none"}}};return A({ourProps:o,theirProps:l,slot:{},defaultTag:En,name:"Hidden"})}let se=k(yn),ut=i.createContext(null);ut.displayName="OpenClosedContext";var B=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(B||{});function we(){return i.useContext(ut)}function it({value:e,children:t}){return P.createElement(ut.Provider,{value:e},t)}function wn(e){function t(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",t))}typeof window<"u"&&typeof document<"u"&&(document.addEventListener("DOMContentLoaded",t),t())}let fe=[];wn(()=>{function e(t){t.target instanceof HTMLElement&&t.target!==document.body&&fe[0]!==t.target&&(fe.unshift(t.target),fe=fe.filter(n=>n!=null&&n.isConnected),fe.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function Ae(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(t==null?void 0:t.getAttribute("disabled"))==="";return r&&Pn(n)?!1:r}function Pn(e){if(!e)return!1;let t=e.previousElementSibling;for(;t!==null;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}function Sn(e){throw new Error("Unexpected object: "+e)}var ee=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(ee||{});function $n(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),l=r??-1;switch(e.focus){case 0:{for(let o=0;o<n.length;++o)if(!t.resolveDisabled(n[o],o,n))return o;return r}case 1:{for(let o=l-1;o>=0;--o)if(!t.resolveDisabled(n[o],o,n))return o;return r}case 2:{for(let o=l+1;o<n.length;++o)if(!t.resolveDisabled(n[o],o,n))return o;return r}case 3:{for(let o=n.length-1;o>=0;--o)if(!t.resolveDisabled(n[o],o,n))return o;return r}case 4:{for(let o=0;o<n.length;++o)if(t.resolveId(n[o],o,n)===e.id)return o;return r}case 5:return null;default:Sn(e)}}var R=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(R||{});function st(e,t,n,r){let l=Y(n);i.useEffect(()=>{e=e??window;function o(a){l.current(a)}return e.addEventListener(t,o,r),()=>e.removeEventListener(t,o,r)},[e,t,r])}function Pe(){let e=i.useRef(!1);return H(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function kt(e){let t=y(e),n=i.useRef(!1);i.useEffect(()=>(n.current=!1,()=>{n.current=!0,xe(()=>{n.current&&t()})}),[t])}var z=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(z||{});function ct(){let e=i.useRef(0);return Dt("keydown",t=>{t.key==="Tab"&&(e.current=t.shiftKey?1:0)},!0),e}function Nt(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}let Tn="div";var At=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(At||{});function xn(e,t){let n=i.useRef(null),r=N(n,t),{initialFocus:l,containers:o,features:a=30,...u}=e;Ee()||(a=1);let s=ce(n);Fn({ownerDocument:s},!!(a&16));let d=Cn({ownerDocument:s,container:n,initialFocus:l},!!(a&2));Dn({ownerDocument:s,container:n,containers:o,previousActiveElement:d},!!(a&8));let c=ct(),f=y(v=>{let m=n.current;m&&(S=>S())(()=>{L(c.current,{[z.Forwards]:()=>{G(m,O.First,{skipElements:[v.relatedTarget]})},[z.Backwards]:()=>{G(m,O.Last,{skipElements:[v.relatedTarget]})}})})}),p=Ie(),h=i.useRef(!1),g={ref:r,onKeyDown(v){v.key=="Tab"&&(h.current=!0,p.requestAnimationFrame(()=>{h.current=!1}))},onBlur(v){let m=Nt(o);n.current instanceof HTMLElement&&m.add(n.current);let S=v.relatedTarget;S instanceof HTMLElement&&S.dataset.headlessuiFocusGuard!=="true"&&(_t(m,S)||(h.current?G(n.current,L(c.current,{[z.Forwards]:()=>O.Next,[z.Backwards]:()=>O.Previous})|O.WrapAround,{relativeTo:v.target}):v.target instanceof HTMLElement&&me(v.target)))}};return P.createElement(P.Fragment,null,!!(a&4)&&P.createElement(se,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:ie.Focusable}),A({ourProps:g,theirProps:u,defaultTag:Tn,name:"FocusTrap"}),!!(a&4)&&P.createElement(se,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:ie.Focusable}))}let In=k(xn),Se=Object.assign(In,{features:At});function Rn(e=!0){let t=i.useRef(fe.slice());return at(([n],[r])=>{r===!0&&n===!1&&xe(()=>{t.current.splice(0)}),r===!1&&n===!0&&(t.current=fe.slice())},[e,fe,t]),y(()=>{var n;return(n=t.current.find(r=>r!=null&&r.isConnected))!=null?n:null})}function Fn({ownerDocument:e},t){let n=Rn(t);at(()=>{t||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&me(n())},[t]),kt(()=>{t&&me(n())})}function Cn({ownerDocument:e,container:t,initialFocus:n},r){let l=i.useRef(null),o=Pe();return at(()=>{if(!r)return;let a=t.current;a&&xe(()=>{if(!o.current)return;let u=e==null?void 0:e.activeElement;if(n!=null&&n.current){if((n==null?void 0:n.current)===u){l.current=u;return}}else if(a.contains(u)){l.current=u;return}n!=null&&n.current?me(n.current):G(a,O.First)===ae.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),l.current=e==null?void 0:e.activeElement})},[r]),l}function Dn({ownerDocument:e,container:t,containers:n,previousActiveElement:r},l){let o=Pe();st(e==null?void 0:e.defaultView,"focus",a=>{if(!l||!o.current)return;let u=Nt(n);t.current instanceof HTMLElement&&u.add(t.current);let s=r.current;if(!s)return;let d=a.target;d&&d instanceof HTMLElement?_t(u,d)?(r.current=d,me(d)):(a.preventDefault(),a.stopPropagation(),me(s)):me(r.current)},!0)}function _t(e,t){for(let n of e)if(n.contains(t))return!0;return!1}let Bt=i.createContext(!1);function Mn(){return i.useContext(Bt)}function Ze(e){return P.createElement(Bt.Provider,{value:e.force},e.children)}function Ln(e){let t=Mn(),n=i.useContext(Ht),r=ce(e),[l,o]=i.useState(()=>{if(!t&&n!==null||ue.isServer)return null;let a=r==null?void 0:r.getElementById("headlessui-portal-root");if(a)return a;if(r===null)return null;let u=r.createElement("div");return u.setAttribute("id","headlessui-portal-root"),r.body.appendChild(u)});return i.useEffect(()=>{l!==null&&(r!=null&&r.body.contains(l)||r==null||r.body.appendChild(l))},[l,r]),i.useEffect(()=>{t||n!==null&&o(n.current)},[n,o,t]),l}let On=i.Fragment;function kn(e,t){let n=e,r=i.useRef(null),l=N(Lt(c=>{r.current=c}),t),o=ce(r),a=Ln(r),[u]=i.useState(()=>{var c;return ue.isServer?null:(c=o==null?void 0:o.createElement("div"))!=null?c:null}),s=i.useContext(et),d=Ee();return H(()=>{!a||!u||a.contains(u)||(u.setAttribute("data-headlessui-portal",""),a.appendChild(u))},[a,u]),H(()=>{if(u&&s)return s.register(u)},[s,u]),kt(()=>{var c;!a||!u||(u instanceof Node&&a.contains(u)&&a.removeChild(u),a.childNodes.length<=0&&((c=a.parentElement)==null||c.removeChild(a)))}),d?!a||!u?null:nn.createPortal(A({ourProps:{ref:l},theirProps:n,defaultTag:On,name:"Portal"}),u):null}let Nn=i.Fragment,Ht=i.createContext(null);function An(e,t){let{target:n,...r}=e,l={ref:N(t)};return P.createElement(Ht.Provider,{value:n},A({ourProps:l,theirProps:r,defaultTag:Nn,name:"Popover.Group"}))}let et=i.createContext(null);function jt(){let e=i.useContext(et),t=i.useRef([]),n=y(o=>(t.current.push(o),e&&e.register(o),()=>r(o))),r=y(o=>{let a=t.current.indexOf(o);a!==-1&&t.current.splice(a,1),e&&e.unregister(o)}),l=i.useMemo(()=>({register:n,unregister:r,portals:t}),[n,r,t]);return[t,i.useMemo(()=>function({children:o}){return P.createElement(et.Provider,{value:l},o)},[l])]}let _n=k(kn),Bn=k(An),tt=Object.assign(_n,{Group:Bn});function Hn(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const jn=typeof Object.is=="function"?Object.is:Hn,{useState:Un,useEffect:Wn,useLayoutEffect:qn,useDebugValue:Gn}=Te;function Kn(e,t,n){const r=t(),[{inst:l},o]=Un({inst:{value:r,getSnapshot:t}});return qn(()=>{l.value=r,l.getSnapshot=t,Ve(l)&&o({inst:l})},[e,r,t]),Wn(()=>(Ve(l)&&o({inst:l}),e(()=>{Ve(l)&&o({inst:l})})),[e]),Gn(r),r}function Ve(e){const t=e.getSnapshot,n=e.value;try{const r=t();return!jn(n,r)}catch{return!0}}function Vn(e,t,n){return t()}const Yn=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Qn=!Yn,zn=Qn?Vn:Kn,Xn="useSyncExternalStore"in Te?(e=>e.useSyncExternalStore)(Te):zn;function Jn(e){return Xn(e.subscribe,e.getSnapshot,e.getSnapshot)}function Zn(e,t){let n=e(),r=new Set;return{getSnapshot(){return n},subscribe(l){return r.add(l),()=>r.delete(l)},dispatch(l,...o){let a=t[l].call(n,...o);a&&(n=a,r.forEach(u=>u()))}}}function er(){let e;return{before({doc:t}){var n;let r=t.documentElement;e=((n=t.defaultView)!=null?n:window).innerWidth-r.clientWidth},after({doc:t,d:n}){let r=t.documentElement,l=r.clientWidth-r.offsetWidth,o=e-l;n.style(r,"paddingRight",`${o}px`)}}}function tr(){return Ct()?{before({doc:e,d:t,meta:n}){function r(l){return n.containers.flatMap(o=>o()).some(o=>o.contains(l))}t.microTask(()=>{var l;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let u=te();u.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>u.dispose()))}let o=(l=window.scrollY)!=null?l:window.pageYOffset,a=null;t.addEventListener(e,"click",u=>{if(u.target instanceof HTMLElement)try{let s=u.target.closest("a");if(!s)return;let{hash:d}=new URL(s.href),c=e.querySelector(d);c&&!r(c)&&(a=c)}catch{}},!0),t.addEventListener(e,"touchstart",u=>{if(u.target instanceof HTMLElement)if(r(u.target)){let s=u.target;for(;s.parentElement&&r(s.parentElement);)s=s.parentElement;t.style(s,"overscrollBehavior","contain")}else t.style(u.target,"touchAction","none")}),t.addEventListener(e,"touchmove",u=>{if(u.target instanceof HTMLElement)if(r(u.target)){let s=u.target;for(;s.parentElement&&s.dataset.headlessuiPortal!==""&&!(s.scrollHeight>s.clientHeight||s.scrollWidth>s.clientWidth);)s=s.parentElement;s.dataset.headlessuiPortal===""&&u.preventDefault()}else u.preventDefault()},{passive:!1}),t.add(()=>{var u;let s=(u=window.scrollY)!=null?u:window.pageYOffset;o!==s&&window.scrollTo(0,o),a&&a.isConnected&&(a.scrollIntoView({block:"nearest"}),a=null)})})}}:{}}function nr(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function rr(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let he=Zn(()=>new Map,{PUSH(e,t){var n;let r=(n=this.get(e))!=null?n:{doc:e,count:0,d:te(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:rr(n)},l=[tr(),er(),nr()];l.forEach(({before:o})=>o==null?void 0:o(r)),l.forEach(({after:o})=>o==null?void 0:o(r))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});he.subscribe(()=>{let e=he.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let r=t.get(n.doc)==="hidden",l=n.count!==0;(l&&!r||!l&&r)&&he.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),n.count===0&&he.dispatch("TEARDOWN",n)}});function or(e,t,n){let r=Jn(he),l=e?r.get(e):void 0,o=l?l.count>0:!1;return H(()=>{if(!(!e||!t))return he.dispatch("PUSH",e,n),()=>he.dispatch("POP",e,n)},[t,e]),o}let Ye=new Map,$e=new Map;function Tt(e,t=!0){H(()=>{var n;if(!t)return;let r=typeof e=="function"?e():e.current;if(!r)return;function l(){var a;if(!r)return;let u=(a=$e.get(r))!=null?a:1;if(u===1?$e.delete(r):$e.set(r,u-1),u!==1)return;let s=Ye.get(r);s&&(s["aria-hidden"]===null?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",s["aria-hidden"]),r.inert=s.inert,Ye.delete(r))}let o=(n=$e.get(r))!=null?n:0;return $e.set(r,o+1),o!==0||(Ye.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),l},[e,t])}function Ut({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let l=i.useRef((r=n==null?void 0:n.current)!=null?r:null),o=ce(l),a=y(()=>{var u,s,d;let c=[];for(let f of e)f!==null&&(f instanceof HTMLElement?c.push(f):"current"in f&&f.current instanceof HTMLElement&&c.push(f.current));if(t!=null&&t.current)for(let f of t.current)c.push(f);for(let f of(u=o==null?void 0:o.querySelectorAll("html > *, body > *"))!=null?u:[])f!==document.body&&f!==document.head&&f instanceof HTMLElement&&f.id!=="headlessui-portal-root"&&(f.contains(l.current)||f.contains((d=(s=l.current)==null?void 0:s.getRootNode())==null?void 0:d.host)||c.some(p=>f.contains(p))||c.push(f));return c});return{resolveContainers:a,contains:y(u=>a().some(s=>s.contains(u))),mainTreeNodeRef:l,MainTreeNode:i.useMemo(()=>function(){return n!=null?null:P.createElement(se,{features:ie.Hidden,ref:l})},[l,n])}}function lr(){let e=i.useRef(null);return{mainTreeNodeRef:e,MainTreeNode:i.useMemo(()=>function(){return P.createElement(se,{features:ie.Hidden,ref:e})},[e])}}let dt=i.createContext(()=>{});dt.displayName="StackContext";var nt=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(nt||{});function ar(){return i.useContext(dt)}function ur({children:e,onUpdate:t,type:n,element:r,enabled:l}){let o=ar(),a=y((...u)=>{t==null||t(...u),o(...u)});return H(()=>{let u=l===void 0||l===!0;return u&&a(0,n,r),()=>{u&&a(1,n,r)}},[a,n,r,l]),P.createElement(dt.Provider,{value:a},e)}let Wt=i.createContext(null);function qt(){let e=i.useContext(Wt);if(e===null){let t=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,qt),t}return e}function ir(){let[e,t]=i.useState([]);return[e.length>0?e.join(" "):void 0,i.useMemo(()=>function(n){let r=y(o=>(t(a=>[...a,o]),()=>t(a=>{let u=a.slice(),s=u.indexOf(o);return s!==-1&&u.splice(s,1),u}))),l=i.useMemo(()=>({register:r,slot:n.slot,name:n.name,props:n.props}),[r,n.slot,n.name,n.props]);return P.createElement(Wt.Provider,{value:l},n.children)},[t])]}let sr="p";function cr(e,t){let n=K(),{id:r=`headlessui-description-${n}`,...l}=e,o=qt(),a=N(t);H(()=>o.register(r),[r,o.register]);let u={ref:a,...o.props,id:r};return A({ourProps:u,theirProps:l,slot:o.slot||{},defaultTag:sr,name:o.name||"Description"})}let dr=k(cr),fr=Object.assign(dr,{});var pr=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(pr||{}),mr=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(mr||{});let vr={0(e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},Oe=i.createContext(null);Oe.displayName="DialogContext";function Fe(e){let t=i.useContext(Oe);if(t===null){let n=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Fe),n}return t}function gr(e,t,n=()=>[document.body]){or(e,t,r=>{var l;return{containers:[...(l=r.containers)!=null?l:[],n]}})}function hr(e,t){return L(t.type,vr,e,t)}let br="div",Er=J.RenderStrategy|J.Static;function yr(e,t){let n=K(),{id:r=`headlessui-dialog-${n}`,open:l,onClose:o,initialFocus:a,role:u="dialog",__demoMode:s=!1,...d}=e,[c,f]=i.useState(0),p=i.useRef(!1);u=function(){return u==="dialog"||u==="alertdialog"?u:(p.current||(p.current=!0,console.warn(`Invalid role [${u}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let h=we();l===void 0&&h!==null&&(l=(h&B.Open)===B.Open);let g=i.useRef(null),v=N(g,t),m=ce(g),S=e.hasOwnProperty("open")||h!==null,b=e.hasOwnProperty("onClose");if(!S&&!b)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!S)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!b)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(typeof l!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${l}`);if(typeof o!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${o}`);let $=l?0:1,[F,x]=i.useReducer(hr,{titleId:null,descriptionId:null,panelRef:i.createRef()}),I=y(()=>o(!1)),j=y(W=>x({type:0,id:W})),C=Ee()?s?!1:$===0:!1,T=c>1,E=i.useContext(Oe)!==null,[w,D]=jt(),U={get current(){var W;return(W=F.panelRef.current)!=null?W:g.current}},{resolveContainers:_,mainTreeNodeRef:V,MainTreeNode:M}=Ut({portals:w,defaultContainers:[U]}),q=T?"parent":"leaf",re=h!==null?(h&B.Closing)===B.Closing:!1,oe=(()=>E||re?!1:C)(),Z=i.useCallback(()=>{var W,le;return(le=Array.from((W=m==null?void 0:m.querySelectorAll("body > *"))!=null?W:[]).find(X=>X.id==="headlessui-portal-root"?!1:X.contains(V.current)&&X instanceof HTMLElement))!=null?le:null},[V]);Tt(Z,oe);let ve=(()=>T?!0:C)(),Q=i.useCallback(()=>{var W,le;return(le=Array.from((W=m==null?void 0:m.querySelectorAll("[data-headlessui-portal]"))!=null?W:[]).find(X=>X.contains(V.current)&&X instanceof HTMLElement))!=null?le:null},[V]);Tt(Q,ve);let qe=(()=>!(!C||T))();ot(_,W=>{W.preventDefault(),I()},qe);let ne=(()=>!(T||$!==0))();st(m==null?void 0:m.defaultView,"keydown",W=>{ne&&(W.defaultPrevented||W.key===R.Escape&&(W.preventDefault(),W.stopPropagation(),I()))});let Xt=(()=>!(re||$!==0||E))();gr(m,Xt,_),i.useEffect(()=>{if($!==0||!g.current)return;let W=new ResizeObserver(le=>{for(let X of le){let Ce=X.target.getBoundingClientRect();Ce.x===0&&Ce.y===0&&Ce.width===0&&Ce.height===0&&I()}});return W.observe(g.current),()=>W.disconnect()},[$,g,I]);let[Jt,Zt]=ir(),en=i.useMemo(()=>[{dialogState:$,close:I,setTitleId:j},F],[$,F,I,j]),yt=i.useMemo(()=>({open:$===0}),[$]),tn={ref:v,id:r,role:u,"aria-modal":$===0?!0:void 0,"aria-labelledby":F.titleId,"aria-describedby":Jt};return P.createElement(ur,{type:"Dialog",enabled:$===0,element:g,onUpdate:y((W,le)=>{le==="Dialog"&&L(W,{[nt.Add]:()=>f(X=>X+1),[nt.Remove]:()=>f(X=>X-1)})})},P.createElement(Ze,{force:!0},P.createElement(tt,null,P.createElement(Oe.Provider,{value:en},P.createElement(tt.Group,{target:g},P.createElement(Ze,{force:!1},P.createElement(Zt,{slot:yt,name:"Dialog.Description"},P.createElement(Se,{initialFocus:a,containers:_,features:C?L(q,{parent:Se.features.RestoreFocus,leaf:Se.features.All&~Se.features.FocusLock}):Se.features.None},P.createElement(D,null,A({ourProps:tn,theirProps:d,slot:yt,defaultTag:br,features:Er,visible:$===0,name:"Dialog"}))))))))),P.createElement(M,null))}let wr="div";function Pr(e,t){let n=K(),{id:r=`headlessui-dialog-overlay-${n}`,...l}=e,[{dialogState:o,close:a}]=Fe("Dialog.Overlay"),u=N(t),s=y(c=>{if(c.target===c.currentTarget){if(Ae(c.currentTarget))return c.preventDefault();c.preventDefault(),c.stopPropagation(),a()}}),d=i.useMemo(()=>({open:o===0}),[o]);return A({ourProps:{ref:u,id:r,"aria-hidden":!0,onClick:s},theirProps:l,slot:d,defaultTag:wr,name:"Dialog.Overlay"})}let Sr="div";function $r(e,t){let n=K(),{id:r=`headlessui-dialog-backdrop-${n}`,...l}=e,[{dialogState:o},a]=Fe("Dialog.Backdrop"),u=N(t);i.useEffect(()=>{if(a.panelRef.current===null)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[a.panelRef]);let s=i.useMemo(()=>({open:o===0}),[o]);return P.createElement(Ze,{force:!0},P.createElement(tt,null,A({ourProps:{ref:u,id:r,"aria-hidden":!0},theirProps:l,slot:s,defaultTag:Sr,name:"Dialog.Backdrop"})))}let Tr="div";function xr(e,t){let n=K(),{id:r=`headlessui-dialog-panel-${n}`,...l}=e,[{dialogState:o},a]=Fe("Dialog.Panel"),u=N(t,a.panelRef),s=i.useMemo(()=>({open:o===0}),[o]),d=y(c=>{c.stopPropagation()});return A({ourProps:{ref:u,id:r,onClick:d},theirProps:l,slot:s,defaultTag:Tr,name:"Dialog.Panel"})}let Ir="h2";function Rr(e,t){let n=K(),{id:r=`headlessui-dialog-title-${n}`,...l}=e,[{dialogState:o,setTitleId:a}]=Fe("Dialog.Title"),u=N(t);i.useEffect(()=>(a(r),()=>a(null)),[r,a]);let s=i.useMemo(()=>({open:o===0}),[o]);return A({ourProps:{ref:u,id:r},theirProps:l,slot:s,defaultTag:Ir,name:"Dialog.Title"})}let Fr=k(yr),Cr=k($r),Dr=k(xr),Mr=k(Pr),Lr=k(Rr),fl=Object.assign(Fr,{Backdrop:Cr,Panel:Dr,Overlay:Mr,Title:Lr,Description:fr}),xt=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function It(e){var t,n;let r=(t=e.innerText)!=null?t:"",l=e.cloneNode(!0);if(!(l instanceof HTMLElement))return r;let o=!1;for(let u of l.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))u.remove(),o=!0;let a=o?(n=l.innerText)!=null?n:"":r;return xt.test(a)&&(a=a.replace(xt,"")),a}function Or(e){let t=e.getAttribute("aria-label");if(typeof t=="string")return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let r=n.split(" ").map(l=>{let o=document.getElementById(l);if(o){let a=o.getAttribute("aria-label");return typeof a=="string"?a.trim():It(o).trim()}return null}).filter(Boolean);if(r.length>0)return r.join(", ")}return It(e).trim()}function kr(e){let t=i.useRef(""),n=i.useRef("");return y(()=>{let r=e.current;if(!r)return"";let l=r.innerText;if(t.current===l)return n.current;let o=Or(r).trim().toLowerCase();return t.current=l,n.current=o,o})}var Nr=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Nr||{}),Ar=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(Ar||{}),_r=(e=>(e[e.OpenMenu=0]="OpenMenu",e[e.CloseMenu=1]="CloseMenu",e[e.GoToItem=2]="GoToItem",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterItem=5]="RegisterItem",e[e.UnregisterItem=6]="UnregisterItem",e))(_r||{});function Qe(e,t=n=>n){let n=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,r=ge(t(e.items.slice()),o=>o.dataRef.current.domRef.current),l=n?r.indexOf(n):null;return l===-1&&(l=null),{items:r,activeItemIndex:l}}let Br={1(e){return e.menuState===1?e:{...e,activeItemIndex:null,menuState:1}},0(e){return e.menuState===0?e:{...e,__demoMode:!1,menuState:0}},2:(e,t)=>{var n;let r=Qe(e),l=$n(t,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:o=>o.id,resolveDisabled:o=>o.dataRef.current.disabled});return{...e,...r,searchQuery:"",activeItemIndex:l,activationTrigger:(n=t.trigger)!=null?n:1}},3:(e,t)=>{let n=e.searchQuery!==""?0:1,r=e.searchQuery+t.value.toLowerCase(),l=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find(a=>{var u;return((u=a.dataRef.current.textValue)==null?void 0:u.startsWith(r))&&!a.dataRef.current.disabled}),o=l?e.items.indexOf(l):-1;return o===-1||o===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:o,activationTrigger:1}},4(e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},5:(e,t)=>{let n=Qe(e,r=>[...r,{id:t.id,dataRef:t.dataRef}]);return{...e,...n}},6:(e,t)=>{let n=Qe(e,r=>{let l=r.findIndex(o=>o.id===t.id);return l!==-1&&r.splice(l,1),r});return{...e,...n,activationTrigger:1}}},ft=i.createContext(null);ft.displayName="MenuContext";function _e(e){let t=i.useContext(ft);if(t===null){let n=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,_e),n}return t}function Hr(e,t){return L(t.type,Br,e,t)}let jr=i.Fragment;function Ur(e,t){let{__demoMode:n=!1,...r}=e,l=i.useReducer(Hr,{__demoMode:n,menuState:n?0:1,buttonRef:i.createRef(),itemsRef:i.createRef(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:o,itemsRef:a,buttonRef:u},s]=l,d=N(t);ot([u,a],(h,g)=>{var v;s({type:1}),Ne(g,ke.Loose)||(h.preventDefault(),(v=u.current)==null||v.focus())},o===0);let c=y(()=>{s({type:1})}),f=i.useMemo(()=>({open:o===0,close:c}),[o,c]),p={ref:d};return P.createElement(ft.Provider,{value:l},P.createElement(it,{value:L(o,{0:B.Open,1:B.Closed})},A({ourProps:p,theirProps:r,slot:f,defaultTag:jr,name:"Menu"})))}let Wr="button";function qr(e,t){var n;let r=K(),{id:l=`headlessui-menu-button-${r}`,...o}=e,[a,u]=_e("Menu.Button"),s=N(a.buttonRef,t),d=Ie(),c=y(v=>{switch(v.key){case R.Space:case R.Enter:case R.ArrowDown:v.preventDefault(),v.stopPropagation(),u({type:0}),d.nextFrame(()=>u({type:2,focus:ee.First}));break;case R.ArrowUp:v.preventDefault(),v.stopPropagation(),u({type:0}),d.nextFrame(()=>u({type:2,focus:ee.Last}));break}}),f=y(v=>{switch(v.key){case R.Space:v.preventDefault();break}}),p=y(v=>{if(Ae(v.currentTarget))return v.preventDefault();e.disabled||(a.menuState===0?(u({type:1}),d.nextFrame(()=>{var m;return(m=a.buttonRef.current)==null?void 0:m.focus({preventScroll:!0})})):(v.preventDefault(),u({type:0})))}),h=i.useMemo(()=>({open:a.menuState===0}),[a]),g={ref:s,id:l,type:lt(e,a.buttonRef),"aria-haspopup":"menu","aria-controls":(n=a.itemsRef.current)==null?void 0:n.id,"aria-expanded":a.menuState===0,onKeyDown:c,onKeyUp:f,onClick:p};return A({ourProps:g,theirProps:o,slot:h,defaultTag:Wr,name:"Menu.Button"})}let Gr="div",Kr=J.RenderStrategy|J.Static;function Vr(e,t){var n,r;let l=K(),{id:o=`headlessui-menu-items-${l}`,...a}=e,[u,s]=_e("Menu.Items"),d=N(u.itemsRef,t),c=ce(u.itemsRef),f=Ie(),p=we(),h=(()=>p!==null?(p&B.Open)===B.Open:u.menuState===0)();i.useEffect(()=>{let b=u.itemsRef.current;b&&u.menuState===0&&b!==(c==null?void 0:c.activeElement)&&b.focus({preventScroll:!0})},[u.menuState,u.itemsRef,c]),gn({container:u.itemsRef.current,enabled:u.menuState===0,accept(b){return b.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:b.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(b){b.setAttribute("role","none")}});let g=y(b=>{var $,F;switch(f.dispose(),b.key){case R.Space:if(u.searchQuery!=="")return b.preventDefault(),b.stopPropagation(),s({type:3,value:b.key});case R.Enter:if(b.preventDefault(),b.stopPropagation(),s({type:1}),u.activeItemIndex!==null){let{dataRef:x}=u.items[u.activeItemIndex];(F=($=x.current)==null?void 0:$.domRef.current)==null||F.click()}Ft(u.buttonRef.current);break;case R.ArrowDown:return b.preventDefault(),b.stopPropagation(),s({type:2,focus:ee.Next});case R.ArrowUp:return b.preventDefault(),b.stopPropagation(),s({type:2,focus:ee.Previous});case R.Home:case R.PageUp:return b.preventDefault(),b.stopPropagation(),s({type:2,focus:ee.First});case R.End:case R.PageDown:return b.preventDefault(),b.stopPropagation(),s({type:2,focus:ee.Last});case R.Escape:b.preventDefault(),b.stopPropagation(),s({type:1}),te().nextFrame(()=>{var x;return(x=u.buttonRef.current)==null?void 0:x.focus({preventScroll:!0})});break;case R.Tab:b.preventDefault(),b.stopPropagation(),s({type:1}),te().nextFrame(()=>{fn(u.buttonRef.current,b.shiftKey?O.Previous:O.Next)});break;default:b.key.length===1&&(s({type:3,value:b.key}),f.setTimeout(()=>s({type:4}),350));break}}),v=y(b=>{switch(b.key){case R.Space:b.preventDefault();break}}),m=i.useMemo(()=>({open:u.menuState===0}),[u]),S={"aria-activedescendant":u.activeItemIndex===null||(n=u.items[u.activeItemIndex])==null?void 0:n.id,"aria-labelledby":(r=u.buttonRef.current)==null?void 0:r.id,id:o,onKeyDown:g,onKeyUp:v,role:"menu",tabIndex:0,ref:d};return A({ourProps:S,theirProps:a,slot:m,defaultTag:Gr,features:Kr,visible:h,name:"Menu.Items"})}let Yr=i.Fragment;function Qr(e,t){let n=K(),{id:r=`headlessui-menu-item-${n}`,disabled:l=!1,...o}=e,[a,u]=_e("Menu.Item"),s=a.activeItemIndex!==null?a.items[a.activeItemIndex].id===r:!1,d=i.useRef(null),c=N(t,d);H(()=>{if(a.__demoMode||a.menuState!==0||!s||a.activationTrigger===0)return;let x=te();return x.requestAnimationFrame(()=>{var I,j;(j=(I=d.current)==null?void 0:I.scrollIntoView)==null||j.call(I,{block:"nearest"})}),x.dispose},[a.__demoMode,d,s,a.menuState,a.activationTrigger,a.activeItemIndex]);let f=kr(d),p=i.useRef({disabled:l,domRef:d,get textValue(){return f()}});H(()=>{p.current.disabled=l},[p,l]),H(()=>(u({type:5,id:r,dataRef:p}),()=>u({type:6,id:r})),[p,r]);let h=y(()=>{u({type:1})}),g=y(x=>{if(l)return x.preventDefault();u({type:1}),Ft(a.buttonRef.current)}),v=y(()=>{if(l)return u({type:2,focus:ee.Nothing});u({type:2,focus:ee.Specific,id:r})}),m=vn(),S=y(x=>m.update(x)),b=y(x=>{m.wasMoved(x)&&(l||s||u({type:2,focus:ee.Specific,id:r,trigger:0}))}),$=y(x=>{m.wasMoved(x)&&(l||s&&u({type:2,focus:ee.Nothing}))}),F=i.useMemo(()=>({active:s,disabled:l,close:h}),[s,l,h]);return A({ourProps:{id:r,ref:c,role:"menuitem",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,disabled:void 0,onClick:g,onFocus:v,onPointerEnter:S,onMouseEnter:S,onPointerMove:b,onMouseMove:b,onPointerLeave:$,onMouseLeave:$},theirProps:o,slot:F,defaultTag:Yr,name:"Menu.Item"})}let zr=k(Ur),Xr=k(qr),Jr=k(Vr),Zr=k(Qr),pl=Object.assign(zr,{Button:Xr,Items:Jr,Item:Zr});var eo=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(eo||{}),to=(e=>(e[e.TogglePopover=0]="TogglePopover",e[e.ClosePopover=1]="ClosePopover",e[e.SetButton=2]="SetButton",e[e.SetButtonId=3]="SetButtonId",e[e.SetPanel=4]="SetPanel",e[e.SetPanelId=5]="SetPanelId",e))(to||{});let no={0:e=>{let t={...e,popoverState:L(e.popoverState,{0:1,1:0})};return t.popoverState===0&&(t.__demoMode=!1),t},1(e){return e.popoverState===1?e:{...e,popoverState:1}},2(e,t){return e.button===t.button?e:{...e,button:t.button}},3(e,t){return e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId}},4(e,t){return e.panel===t.panel?e:{...e,panel:t.panel}},5(e,t){return e.panelId===t.panelId?e:{...e,panelId:t.panelId}}},pt=i.createContext(null);pt.displayName="PopoverContext";function Be(e){let t=i.useContext(pt);if(t===null){let n=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Be),n}return t}let mt=i.createContext(null);mt.displayName="PopoverAPIContext";function vt(e){let t=i.useContext(mt);if(t===null){let n=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,vt),n}return t}let gt=i.createContext(null);gt.displayName="PopoverGroupContext";function Gt(){return i.useContext(gt)}let He=i.createContext(null);He.displayName="PopoverPanelContext";function ro(){return i.useContext(He)}function oo(e,t){return L(t.type,no,e,t)}let lo="div";function ao(e,t){var n;let{__demoMode:r=!1,...l}=e,o=i.useRef(null),a=N(t,Lt(M=>{o.current=M})),u=i.useRef([]),s=i.useReducer(oo,{__demoMode:r,popoverState:r?0:1,buttons:u,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:i.createRef(),afterPanelSentinel:i.createRef()}),[{popoverState:d,button:c,buttonId:f,panel:p,panelId:h,beforePanelSentinel:g,afterPanelSentinel:v},m]=s,S=ce((n=o.current)!=null?n:c),b=i.useMemo(()=>{if(!c||!p)return!1;for(let Q of document.querySelectorAll("body > *"))if(Number(Q==null?void 0:Q.contains(c))^Number(Q==null?void 0:Q.contains(p)))return!0;let M=Re(),q=M.indexOf(c),re=(q+M.length-1)%M.length,oe=(q+1)%M.length,Z=M[re],ve=M[oe];return!p.contains(Z)&&!p.contains(ve)},[c,p]),$=Y(f),F=Y(h),x=i.useMemo(()=>({buttonId:$,panelId:F,close:()=>m({type:1})}),[$,F,m]),I=Gt(),j=I==null?void 0:I.registerPopover,C=y(()=>{var M;return(M=I==null?void 0:I.isFocusWithinPopoverGroup())!=null?M:(S==null?void 0:S.activeElement)&&((c==null?void 0:c.contains(S.activeElement))||(p==null?void 0:p.contains(S.activeElement)))});i.useEffect(()=>j==null?void 0:j(x),[j,x]);let[T,E]=jt(),w=Ut({mainTreeNodeRef:I==null?void 0:I.mainTreeNodeRef,portals:T,defaultContainers:[c,p]});st(S==null?void 0:S.defaultView,"focus",M=>{var q,re,oe,Z;M.target!==window&&M.target instanceof HTMLElement&&d===0&&(C()||c&&p&&(w.contains(M.target)||(re=(q=g.current)==null?void 0:q.contains)!=null&&re.call(q,M.target)||(Z=(oe=v.current)==null?void 0:oe.contains)!=null&&Z.call(oe,M.target)||m({type:1})))},!0),ot(w.resolveContainers,(M,q)=>{m({type:1}),Ne(q,ke.Loose)||(M.preventDefault(),c==null||c.focus())},d===0);let D=y(M=>{m({type:1});let q=(()=>M?M instanceof HTMLElement?M:"current"in M&&M.current instanceof HTMLElement?M.current:c:c)();q==null||q.focus()}),U=i.useMemo(()=>({close:D,isPortalled:b}),[D,b]),_=i.useMemo(()=>({open:d===0,close:D}),[d,D]),V={ref:a};return P.createElement(He.Provider,{value:null},P.createElement(pt.Provider,{value:s},P.createElement(mt.Provider,{value:U},P.createElement(it,{value:L(d,{0:B.Open,1:B.Closed})},P.createElement(E,null,A({ourProps:V,theirProps:l,slot:_,defaultTag:lo,name:"Popover"}),P.createElement(w.MainTreeNode,null))))))}let uo="button";function io(e,t){let n=K(),{id:r=`headlessui-popover-button-${n}`,...l}=e,[o,a]=Be("Popover.Button"),{isPortalled:u}=vt("Popover.Button"),s=i.useRef(null),d=`headlessui-focus-sentinel-${K()}`,c=Gt(),f=c==null?void 0:c.closeOthers,p=ro()!==null;i.useEffect(()=>{if(!p)return a({type:3,buttonId:r}),()=>{a({type:3,buttonId:null})}},[p,r,a]);let[h]=i.useState(()=>Symbol()),g=N(s,t,p?null:w=>{if(w)o.buttons.current.push(h);else{let D=o.buttons.current.indexOf(h);D!==-1&&o.buttons.current.splice(D,1)}o.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),w&&a({type:2,button:w})}),v=N(s,t),m=ce(s),S=y(w=>{var D,U,_;if(p){if(o.popoverState===1)return;switch(w.key){case R.Space:case R.Enter:w.preventDefault(),(U=(D=w.target).click)==null||U.call(D),a({type:1}),(_=o.button)==null||_.focus();break}}else switch(w.key){case R.Space:case R.Enter:w.preventDefault(),w.stopPropagation(),o.popoverState===1&&(f==null||f(o.buttonId)),a({type:0});break;case R.Escape:if(o.popoverState!==0)return f==null?void 0:f(o.buttonId);if(!s.current||m!=null&&m.activeElement&&!s.current.contains(m.activeElement))return;w.preventDefault(),w.stopPropagation(),a({type:1});break}}),b=y(w=>{p||w.key===R.Space&&w.preventDefault()}),$=y(w=>{var D,U;Ae(w.currentTarget)||e.disabled||(p?(a({type:1}),(D=o.button)==null||D.focus()):(w.preventDefault(),w.stopPropagation(),o.popoverState===1&&(f==null||f(o.buttonId)),a({type:0}),(U=o.button)==null||U.focus()))}),F=y(w=>{w.preventDefault(),w.stopPropagation()}),x=o.popoverState===0,I=i.useMemo(()=>({open:x}),[x]),j=lt(e,s),C=p?{ref:v,type:j,onKeyDown:S,onClick:$}:{ref:g,id:o.buttonId,type:j,"aria-expanded":o.popoverState===0,"aria-controls":o.panel?o.panelId:void 0,onKeyDown:S,onKeyUp:b,onClick:$,onMouseDown:F},T=ct(),E=y(()=>{let w=o.panel;if(!w)return;function D(){L(T.current,{[z.Forwards]:()=>G(w,O.First),[z.Backwards]:()=>G(w,O.Last)})===ae.Error&&G(Re().filter(U=>U.dataset.headlessuiFocusGuard!=="true"),L(T.current,{[z.Forwards]:O.Next,[z.Backwards]:O.Previous}),{relativeTo:o.button})}D()});return P.createElement(P.Fragment,null,A({ourProps:C,theirProps:l,slot:I,defaultTag:uo,name:"Popover.Button"}),x&&!p&&u&&P.createElement(se,{id:d,features:ie.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:E}))}let so="div",co=J.RenderStrategy|J.Static;function fo(e,t){let n=K(),{id:r=`headlessui-popover-overlay-${n}`,...l}=e,[{popoverState:o},a]=Be("Popover.Overlay"),u=N(t),s=we(),d=(()=>s!==null?(s&B.Open)===B.Open:o===0)(),c=y(p=>{if(Ae(p.currentTarget))return p.preventDefault();a({type:1})}),f=i.useMemo(()=>({open:o===0}),[o]);return A({ourProps:{ref:u,id:r,"aria-hidden":!0,onClick:c},theirProps:l,slot:f,defaultTag:so,features:co,visible:d,name:"Popover.Overlay"})}let po="div",mo=J.RenderStrategy|J.Static;function vo(e,t){let n=K(),{id:r=`headlessui-popover-panel-${n}`,focus:l=!1,...o}=e,[a,u]=Be("Popover.Panel"),{close:s,isPortalled:d}=vt("Popover.Panel"),c=`headlessui-focus-sentinel-before-${K()}`,f=`headlessui-focus-sentinel-after-${K()}`,p=i.useRef(null),h=N(p,t,C=>{u({type:4,panel:C})}),g=ce(p),v=hn();H(()=>(u({type:5,panelId:r}),()=>{u({type:5,panelId:null})}),[r,u]);let m=we(),S=(()=>m!==null?(m&B.Open)===B.Open:a.popoverState===0)(),b=y(C=>{var T;switch(C.key){case R.Escape:if(a.popoverState!==0||!p.current||g!=null&&g.activeElement&&!p.current.contains(g.activeElement))return;C.preventDefault(),C.stopPropagation(),u({type:1}),(T=a.button)==null||T.focus();break}});i.useEffect(()=>{var C;e.static||a.popoverState===1&&((C=e.unmount)==null||C)&&u({type:4,panel:null})},[a.popoverState,e.unmount,e.static,u]),i.useEffect(()=>{if(a.__demoMode||!l||a.popoverState!==0||!p.current)return;let C=g==null?void 0:g.activeElement;p.current.contains(C)||G(p.current,O.First)},[a.__demoMode,l,p,a.popoverState]);let $=i.useMemo(()=>({open:a.popoverState===0,close:s}),[a,s]),F={ref:h,id:r,onKeyDown:b,onBlur:l&&a.popoverState===0?C=>{var T,E,w,D,U;let _=C.relatedTarget;_&&p.current&&((T=p.current)!=null&&T.contains(_)||(u({type:1}),((w=(E=a.beforePanelSentinel.current)==null?void 0:E.contains)!=null&&w.call(E,_)||(U=(D=a.afterPanelSentinel.current)==null?void 0:D.contains)!=null&&U.call(D,_))&&_.focus({preventScroll:!0})))}:void 0,tabIndex:-1},x=ct(),I=y(()=>{let C=p.current;if(!C)return;function T(){L(x.current,{[z.Forwards]:()=>{var E;G(C,O.First)===ae.Error&&((E=a.afterPanelSentinel.current)==null||E.focus())},[z.Backwards]:()=>{var E;(E=a.button)==null||E.focus({preventScroll:!0})}})}T()}),j=y(()=>{let C=p.current;if(!C)return;function T(){L(x.current,{[z.Forwards]:()=>{var E;if(!a.button)return;let w=Re(),D=w.indexOf(a.button),U=w.slice(0,D+1),_=[...w.slice(D+1),...U];for(let V of _.slice())if(V.dataset.headlessuiFocusGuard==="true"||(E=a.panel)!=null&&E.contains(V)){let M=_.indexOf(V);M!==-1&&_.splice(M,1)}G(_,O.First,{sorted:!1})},[z.Backwards]:()=>{var E;G(C,O.Previous)===ae.Error&&((E=a.button)==null||E.focus())}})}T()});return P.createElement(He.Provider,{value:r},S&&d&&P.createElement(se,{id:c,ref:a.beforePanelSentinel,features:ie.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:I}),A({mergeRefs:v,ourProps:F,theirProps:o,slot:$,defaultTag:po,features:mo,visible:S,name:"Popover.Panel"}),S&&d&&P.createElement(se,{id:f,ref:a.afterPanelSentinel,features:ie.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:j}))}let go="div";function ho(e,t){let n=i.useRef(null),r=N(n,t),[l,o]=i.useState([]),a=lr(),u=y(v=>{o(m=>{let S=m.indexOf(v);if(S!==-1){let b=m.slice();return b.splice(S,1),b}return m})}),s=y(v=>(o(m=>[...m,v]),()=>u(v))),d=y(()=>{var v;let m=ye(n);if(!m)return!1;let S=m.activeElement;return(v=n.current)!=null&&v.contains(S)?!0:l.some(b=>{var $,F;return(($=m.getElementById(b.buttonId.current))==null?void 0:$.contains(S))||((F=m.getElementById(b.panelId.current))==null?void 0:F.contains(S))})}),c=y(v=>{for(let m of l)m.buttonId.current!==v&&m.close()}),f=i.useMemo(()=>({registerPopover:s,unregisterPopover:u,isFocusWithinPopoverGroup:d,closeOthers:c,mainTreeNodeRef:a.mainTreeNodeRef}),[s,u,d,c,a.mainTreeNodeRef]),p=i.useMemo(()=>({}),[]),h=e,g={ref:r};return P.createElement(gt.Provider,{value:f},A({ourProps:g,theirProps:h,slot:p,defaultTag:go,name:"Popover.Group"}),P.createElement(a.MainTreeNode,null))}let bo=k(ao),Eo=k(io),yo=k(fo),wo=k(vo),Po=k(ho),ml=Object.assign(bo,{Button:Eo,Overlay:yo,Panel:wo,Group:Po});function So(e=0){let[t,n]=i.useState(e),r=Pe(),l=i.useCallback(s=>{r.current&&n(d=>d|s)},[t,r]),o=i.useCallback(s=>!!(t&s),[t]),a=i.useCallback(s=>{r.current&&n(d=>d&~s)},[n,r]),u=i.useCallback(s=>{r.current&&n(d=>d^s)},[n]);return{flags:t,addFlag:l,hasFlag:o,removeFlag:a,toggleFlag:u}}function $o({onFocus:e}){let[t,n]=i.useState(!0),r=Pe();return t?P.createElement(se,{as:"button",type:"button",features:ie.Focusable,onFocus:l=>{l.preventDefault();let o,a=50;function u(){if(a--<=0){o&&cancelAnimationFrame(o);return}if(e()){if(cancelAnimationFrame(o),!r.current)return;n(!1);return}o=requestAnimationFrame(u)}o=requestAnimationFrame(u)}}):null}const Kt=i.createContext(null);function To(){return{groups:new Map,get(e,t){var n;let r=this.groups.get(e);r||(r=new Map,this.groups.set(e,r));let l=(n=r.get(t))!=null?n:0;r.set(t,l+1);let o=Array.from(r.keys()).indexOf(t);function a(){let u=r.get(t);u>1?r.set(t,u-1):r.delete(t)}return[o,a]}}}function xo({children:e}){let t=i.useRef(To());return i.createElement(Kt.Provider,{value:t},e)}function Vt(e){let t=i.useContext(Kt);if(!t)throw new Error("You must wrap your component in a <StableCollection>");let n=Io(),[r,l]=t.current.get(e,n);return i.useEffect(()=>l,[]),r}function Io(){var e,t,n;let r=(n=(t=(e=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)==null?void 0:e.ReactCurrentOwner)==null?void 0:t.current)!=null?n:null;if(!r)return Symbol();let l=[],o=r;for(;o;)l.push(o.index),o=o.return;return"$."+l.join(".")}var Ro=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(Ro||{}),Fo=(e=>(e[e.Less=-1]="Less",e[e.Equal=0]="Equal",e[e.Greater=1]="Greater",e))(Fo||{}),Co=(e=>(e[e.SetSelectedIndex=0]="SetSelectedIndex",e[e.RegisterTab=1]="RegisterTab",e[e.UnregisterTab=2]="UnregisterTab",e[e.RegisterPanel=3]="RegisterPanel",e[e.UnregisterPanel=4]="UnregisterPanel",e))(Co||{});let Do={0(e,t){var n;let r=ge(e.tabs,c=>c.current),l=ge(e.panels,c=>c.current),o=r.filter(c=>{var f;return!((f=c.current)!=null&&f.hasAttribute("disabled"))}),a={...e,tabs:r,panels:l};if(t.index<0||t.index>r.length-1){let c=L(Math.sign(t.index-e.selectedIndex),{[-1]:()=>1,0:()=>L(Math.sign(t.index),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0});if(o.length===0)return a;let f=L(c,{0:()=>r.indexOf(o[0]),1:()=>r.indexOf(o[o.length-1])});return{...a,selectedIndex:f===-1?e.selectedIndex:f}}let u=r.slice(0,t.index),s=[...r.slice(t.index),...u].find(c=>o.includes(c));if(!s)return a;let d=(n=r.indexOf(s))!=null?n:e.selectedIndex;return d===-1&&(d=e.selectedIndex),{...a,selectedIndex:d}},1(e,t){if(e.tabs.includes(t.tab))return e;let n=e.tabs[e.selectedIndex],r=ge([...e.tabs,t.tab],o=>o.current),l=e.selectedIndex;return e.info.current.isControlled||(l=r.indexOf(n),l===-1&&(l=e.selectedIndex)),{...e,tabs:r,selectedIndex:l}},2(e,t){return{...e,tabs:e.tabs.filter(n=>n!==t.tab)}},3(e,t){return e.panels.includes(t.panel)?e:{...e,panels:ge([...e.panels,t.panel],n=>n.current)}},4(e,t){return{...e,panels:e.panels.filter(n=>n!==t.panel)}}},ht=i.createContext(null);ht.displayName="TabsDataContext";function be(e){let t=i.useContext(ht);if(t===null){let n=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,be),n}return t}let bt=i.createContext(null);bt.displayName="TabsActionsContext";function Et(e){let t=i.useContext(bt);if(t===null){let n=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Et),n}return t}function Mo(e,t){return L(t.type,Do,e,t)}let Lo=i.Fragment;function Oo(e,t){let{defaultIndex:n=0,vertical:r=!1,manual:l=!1,onChange:o,selectedIndex:a=null,...u}=e;const s=r?"vertical":"horizontal",d=l?"manual":"auto";let c=a!==null,f=Y({isControlled:c}),p=N(t),[h,g]=i.useReducer(Mo,{info:f,selectedIndex:a??n,tabs:[],panels:[]}),v=i.useMemo(()=>({selectedIndex:h.selectedIndex}),[h.selectedIndex]),m=Y(o||(()=>{})),S=Y(h.tabs),b=i.useMemo(()=>({orientation:s,activation:d,...h}),[s,d,h]),$=y(T=>(g({type:1,tab:T}),()=>g({type:2,tab:T}))),F=y(T=>(g({type:3,panel:T}),()=>g({type:4,panel:T}))),x=y(T=>{I.current!==T&&m.current(T),c||g({type:0,index:T})}),I=Y(c?e.selectedIndex:h.selectedIndex),j=i.useMemo(()=>({registerTab:$,registerPanel:F,change:x}),[]);H(()=>{g({type:0,index:a??n})},[a]),H(()=>{if(I.current===void 0||h.tabs.length<=0)return;let T=ge(h.tabs,E=>E.current);T.some((E,w)=>h.tabs[w]!==E)&&x(T.indexOf(h.tabs[I.current]))});let C={ref:p};return P.createElement(xo,null,P.createElement(bt.Provider,{value:j},P.createElement(ht.Provider,{value:b},b.tabs.length<=0&&P.createElement($o,{onFocus:()=>{var T,E;for(let w of S.current)if(((T=w.current)==null?void 0:T.tabIndex)===0)return(E=w.current)==null||E.focus(),!0;return!1}}),A({ourProps:C,theirProps:u,slot:v,defaultTag:Lo,name:"Tabs"}))))}let ko="div";function No(e,t){let{orientation:n,selectedIndex:r}=be("Tab.List"),l=N(t);return A({ourProps:{ref:l,role:"tablist","aria-orientation":n},theirProps:e,slot:{selectedIndex:r},defaultTag:ko,name:"Tabs.List"})}let Ao="button";function _o(e,t){var n,r;let l=K(),{id:o=`headlessui-tabs-tab-${l}`,...a}=e,{orientation:u,activation:s,selectedIndex:d,tabs:c,panels:f}=be("Tab"),p=Et("Tab"),h=be("Tab"),g=i.useRef(null),v=N(g,t);H(()=>p.registerTab(g),[p,g]);let m=Vt("tabs"),S=c.indexOf(g);S===-1&&(S=m);let b=S===d,$=y(E=>{var w;let D=E();if(D===ae.Success&&s==="auto"){let U=(w=ye(g))==null?void 0:w.activeElement,_=h.tabs.findIndex(V=>V.current===U);_!==-1&&p.change(_)}return D}),F=y(E=>{let w=c.map(D=>D.current).filter(Boolean);if(E.key===R.Space||E.key===R.Enter){E.preventDefault(),E.stopPropagation(),p.change(S);return}switch(E.key){case R.Home:case R.PageUp:return E.preventDefault(),E.stopPropagation(),$(()=>G(w,O.First));case R.End:case R.PageDown:return E.preventDefault(),E.stopPropagation(),$(()=>G(w,O.Last))}if($(()=>L(u,{vertical(){return E.key===R.ArrowUp?G(w,O.Previous|O.WrapAround):E.key===R.ArrowDown?G(w,O.Next|O.WrapAround):ae.Error},horizontal(){return E.key===R.ArrowLeft?G(w,O.Previous|O.WrapAround):E.key===R.ArrowRight?G(w,O.Next|O.WrapAround):ae.Error}}))===ae.Success)return E.preventDefault()}),x=i.useRef(!1),I=y(()=>{var E;x.current||(x.current=!0,(E=g.current)==null||E.focus({preventScroll:!0}),p.change(S),xe(()=>{x.current=!1}))}),j=y(E=>{E.preventDefault()}),C=i.useMemo(()=>{var E;return{selected:b,disabled:(E=e.disabled)!=null?E:!1}},[b,e.disabled]),T={ref:v,onKeyDown:F,onMouseDown:j,onClick:I,id:o,role:"tab",type:lt(e,g),"aria-controls":(r=(n=f[S])==null?void 0:n.current)==null?void 0:r.id,"aria-selected":b,tabIndex:b?0:-1};return A({ourProps:T,theirProps:a,slot:C,defaultTag:Ao,name:"Tabs.Tab"})}let Bo="div";function Ho(e,t){let{selectedIndex:n}=be("Tab.Panels"),r=N(t),l=i.useMemo(()=>({selectedIndex:n}),[n]);return A({ourProps:{ref:r},theirProps:e,slot:l,defaultTag:Bo,name:"Tabs.Panels"})}let jo="div",Uo=J.RenderStrategy|J.Static;function Wo(e,t){var n,r,l,o;let a=K(),{id:u=`headlessui-tabs-panel-${a}`,tabIndex:s=0,...d}=e,{selectedIndex:c,tabs:f,panels:p}=be("Tab.Panel"),h=Et("Tab.Panel"),g=i.useRef(null),v=N(g,t);H(()=>h.registerPanel(g),[h,g,u]);let m=Vt("panels"),S=p.indexOf(g);S===-1&&(S=m);let b=S===c,$=i.useMemo(()=>({selected:b}),[b]),F={ref:v,id:u,role:"tabpanel","aria-labelledby":(r=(n=f[S])==null?void 0:n.current)==null?void 0:r.id,tabIndex:b?s:-1};return!b&&((l=d.unmount)==null||l)&&!((o=d.static)!=null&&o)?P.createElement(se,{as:"span","aria-hidden":"true",...F}):A({ourProps:F,theirProps:d,slot:$,defaultTag:jo,features:Uo,visible:b,name:"Tabs.Panel"})}let qo=k(_o),Go=k(Oo),Ko=k(No),Vo=k(Ho),Yo=k(Wo),vl=Object.assign(qo,{Group:Go,List:Ko,Panels:Vo,Panel:Yo});function Qo(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}function ze(e,...t){e&&t.length>0&&e.classList.add(...t)}function Xe(e,...t){e&&t.length>0&&e.classList.remove(...t)}function zo(e,t){let n=te();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:l}=getComputedStyle(e),[o,a]=[r,l].map(s=>{let[d=0]=s.split(",").filter(Boolean).map(c=>c.includes("ms")?parseFloat(c):parseFloat(c)*1e3).sort((c,f)=>f-c);return d}),u=o+a;if(u!==0){n.group(d=>{d.setTimeout(()=>{t(),d.dispose()},u),d.addEventListener(e,"transitionrun",c=>{c.target===c.currentTarget&&d.dispose()})});let s=n.addEventListener(e,"transitionend",d=>{d.target===d.currentTarget&&(t(),s())})}else t();return n.add(()=>t()),n.dispose}function Xo(e,t,n,r){let l=n?"enter":"leave",o=te(),a=r!==void 0?Qo(r):()=>{};l==="enter"&&(e.removeAttribute("hidden"),e.style.display="");let u=L(l,{enter:()=>t.enter,leave:()=>t.leave}),s=L(l,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),d=L(l,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return Xe(e,...t.base,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),ze(e,...t.base,...u,...d),o.nextFrame(()=>{Xe(e,...t.base,...u,...d),ze(e,...t.base,...u,...s),zo(e,()=>(Xe(e,...t.base,...u),ze(e,...t.base,...t.entered),a()))}),o.dispose}function Jo({immediate:e,container:t,direction:n,classes:r,onStart:l,onStop:o}){let a=Pe(),u=Ie(),s=Y(n);H(()=>{e&&(s.current="enter")},[e]),H(()=>{let d=te();u.add(d.dispose);let c=t.current;if(c&&s.current!=="idle"&&a.current)return d.dispose(),l.current(s.current),d.add(Xo(c,r.current,s.current==="enter",()=>{d.dispose(),o.current(s.current)})),d.dispose},[n])}function de(e=""){return e.split(/\s+/).filter(t=>t.length>1)}let je=i.createContext(null);je.displayName="TransitionContext";var Zo=(e=>(e.Visible="visible",e.Hidden="hidden",e))(Zo||{});function el(){let e=i.useContext(je);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function tl(){let e=i.useContext(Ue);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}let Ue=i.createContext(null);Ue.displayName="NestingContext";function We(e){return"children"in e?We(e.children):e.current.filter(({el:t})=>t.current!==null).filter(({state:t})=>t==="visible").length>0}function Yt(e,t){let n=Y(e),r=i.useRef([]),l=Pe(),o=Ie(),a=y((h,g=pe.Hidden)=>{let v=r.current.findIndex(({el:m})=>m===h);v!==-1&&(L(g,{[pe.Unmount](){r.current.splice(v,1)},[pe.Hidden](){r.current[v].state="hidden"}}),o.microTask(()=>{var m;!We(r)&&l.current&&((m=n.current)==null||m.call(n))}))}),u=y(h=>{let g=r.current.find(({el:v})=>v===h);return g?g.state!=="visible"&&(g.state="visible"):r.current.push({el:h,state:"visible"}),()=>a(h,pe.Unmount)}),s=i.useRef([]),d=i.useRef(Promise.resolve()),c=i.useRef({enter:[],leave:[],idle:[]}),f=y((h,g,v)=>{s.current.splice(0),t&&(t.chains.current[g]=t.chains.current[g].filter(([m])=>m!==h)),t==null||t.chains.current[g].push([h,new Promise(m=>{s.current.push(m)})]),t==null||t.chains.current[g].push([h,new Promise(m=>{Promise.all(c.current[g].map(([S,b])=>b)).then(()=>m())})]),g==="enter"?d.current=d.current.then(()=>t==null?void 0:t.wait.current).then(()=>v(g)):v(g)}),p=y((h,g,v)=>{Promise.all(c.current[g].splice(0).map(([m,S])=>S)).then(()=>{var m;(m=s.current.shift())==null||m()}).then(()=>v(g))});return i.useMemo(()=>({children:r,register:u,unregister:a,onStart:f,onStop:p,wait:d,chains:c}),[u,a,r,f,p,c,d])}function nl(){}let rl=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function Rt(e){var t;let n={};for(let r of rl)n[r]=(t=e[r])!=null?t:nl;return n}function ol(e){let t=i.useRef(Rt(e));return i.useEffect(()=>{t.current=Rt(e)},[e]),t}let ll="div",Qt=J.RenderStrategy;function al(e,t){var n,r;let{beforeEnter:l,afterEnter:o,beforeLeave:a,afterLeave:u,enter:s,enterFrom:d,enterTo:c,entered:f,leave:p,leaveFrom:h,leaveTo:g,...v}=e,m=i.useRef(null),S=N(m,t),b=(n=v.unmount)==null||n?pe.Unmount:pe.Hidden,{show:$,appear:F,initial:x}=el(),[I,j]=i.useState($?"visible":"hidden"),C=tl(),{register:T,unregister:E}=C;i.useEffect(()=>T(m),[T,m]),i.useEffect(()=>{if(b===pe.Hidden&&m.current){if($&&I!=="visible"){j("visible");return}return L(I,{hidden:()=>E(m),visible:()=>T(m)})}},[I,m,T,E,$,b]);let w=Y({base:de(v.className),enter:de(s),enterFrom:de(d),enterTo:de(c),entered:de(f),leave:de(p),leaveFrom:de(h),leaveTo:de(g)}),D=ol({beforeEnter:l,afterEnter:o,beforeLeave:a,afterLeave:u}),U=Ee();i.useEffect(()=>{if(U&&I==="visible"&&m.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[m,I,U]);let _=x&&!F,V=F&&$&&x,M=(()=>!U||_?"idle":$?"enter":"leave")(),q=So(0),re=y(ne=>L(ne,{enter:()=>{q.addFlag(B.Opening),D.current.beforeEnter()},leave:()=>{q.addFlag(B.Closing),D.current.beforeLeave()},idle:()=>{}})),oe=y(ne=>L(ne,{enter:()=>{q.removeFlag(B.Opening),D.current.afterEnter()},leave:()=>{q.removeFlag(B.Closing),D.current.afterLeave()},idle:()=>{}})),Z=Yt(()=>{j("hidden"),E(m)},C),ve=i.useRef(!1);Jo({immediate:V,container:m,classes:w,direction:M,onStart:Y(ne=>{ve.current=!0,Z.onStart(m,ne,re)}),onStop:Y(ne=>{ve.current=!1,Z.onStop(m,ne,oe),ne==="leave"&&!We(Z)&&(j("hidden"),E(m))})});let Q=v,qe={ref:S};return V?Q={...Q,className:Le(v.className,...w.current.enter,...w.current.enterFrom)}:ve.current&&(Q.className=Le(v.className,(r=m.current)==null?void 0:r.className),Q.className===""&&delete Q.className),P.createElement(Ue.Provider,{value:Z},P.createElement(it,{value:L(I,{visible:B.Open,hidden:B.Closed})|q.flags},A({ourProps:qe,theirProps:Q,defaultTag:ll,features:Qt,visible:I==="visible",name:"Transition.Child"})))}function ul(e,t){let{show:n,appear:r=!1,unmount:l=!0,...o}=e,a=i.useRef(null),u=N(a,t);Ee();let s=we();if(n===void 0&&s!==null&&(n=(s&B.Open)===B.Open),![!0,!1].includes(n))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[d,c]=i.useState(n?"visible":"hidden"),f=Yt(()=>{c("hidden")}),[p,h]=i.useState(!0),g=i.useRef([n]);H(()=>{p!==!1&&g.current[g.current.length-1]!==n&&(g.current.push(n),h(!1))},[g,n]);let v=i.useMemo(()=>({show:n,appear:r,initial:p}),[n,r,p]);i.useEffect(()=>{if(n)c("visible");else if(!We(f))c("hidden");else{let $=a.current;if(!$)return;let F=$.getBoundingClientRect();F.x===0&&F.y===0&&F.width===0&&F.height===0&&c("hidden")}},[n,f]);let m={unmount:l},S=y(()=>{var $;p&&h(!1),($=e.beforeEnter)==null||$.call(e)}),b=y(()=>{var $;p&&h(!1),($=e.beforeLeave)==null||$.call(e)});return P.createElement(Ue.Provider,{value:f},P.createElement(je.Provider,{value:v},A({ourProps:{...m,as:i.Fragment,children:P.createElement(zt,{ref:u,...m,...o,beforeEnter:S,beforeLeave:b})},theirProps:{},defaultTag:i.Fragment,features:Qt,visible:d==="visible",name:"Transition"})))}function il(e,t){let n=i.useContext(je)!==null,r=we()!==null;return P.createElement(P.Fragment,null,!n&&r?P.createElement(rt,{ref:t,...e}):P.createElement(zt,{ref:t,...e}))}let rt=k(ul),zt=k(al),sl=k(il),gl=Object.assign(rt,{Child:sl,Root:rt});export{vl as $,ml as C,fl as _,pl as a,gl as q};
