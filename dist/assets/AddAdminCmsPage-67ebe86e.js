import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as L,R as a,f as _}from"./vendor-2ae44a2e.js";import{u as I}from"./react-hook-form-47c010f8.js";import{o as P}from"./yup-5abd4662.js";import{c as M,a as o}from"./yup-5c93ed04.js";import{G as j,M as q,s as D,t as R}from"./index-b2ff2fa1.js";import{_ as V}from"./qr-scanner-cf010ec4.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const F=L.lazy(()=>V(()=>import("./DynamicContentType-8f50551c.js"),["assets/DynamicContentType-8f50551c.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/index-b2ff2fa1.js","assets/react-confirm-alert-783bc3ae.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-7bce1936.js","assets/react-icons-f29df01f.js","assets/lucide-react-1246a7ed.js","assets/react-loading-skeleton-f53ed7d1.js","assets/react-papaparse-b60a38ab.js","assets/papaparse-2d1475f9.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-9cc92aaf.js","assets/@fortawesome/react-fontawesome-27c5bed3.js","assets/@fortawesome/fontawesome-svg-core-294d29ff.js","assets/@fortawesome/free-solid-svg-icons-11dbc67c.js","assets/@fortawesome/free-regular-svg-icons-3e88f209.js","assets/@fortawesome/free-brands-svg-icons-2414b431.js","assets/index-3134cf1f.css"])),ie=({setSidebar:r})=>{var y,h,f;const w=M({page:o().required(),key:o().required(),type:o().required(),value:o()}).required(),i=[{key:"text",value:"Text"},{key:"image",value:"Image"},{key:"number",value:"Number"},{key:"kvp",value:"Key-Value Pair"},{key:"image-list",value:"Image List"},{key:"captioned-image-list",value:"Captioned Image List"},{key:"team-list",value:"Team List"}],{dispatch:C}=a.useContext(j),{dispatch:m}=a.useContext(j),[N,E]=a.useState((y=i[0])==null?void 0:y.key),[T,A]=a.useState(""),[c,d]=a.useState(!1),S=_(),{register:l,handleSubmit:p,setError:u,formState:{errors:x}}=I({resolver:P(w)}),g=async t=>{let b=new q;d(!0),console.log(t);try{b.setTable("cms");const s=await b.cmsAdd(t.page,t.key,t.type,T);if(!s.error)S("/admin/cms"),D(m,"Added");else if(s.validation){const k=Object.keys(s.validation);for(let n=0;n<k.length;n++){const v=k[n];u(v,{type:"manual",message:s.validation[v]})}}}catch(s){console.log("Error",s),u("page",{type:"manual",message:s.message}),R(C,s.message)}d(!1)};return a.useEffect(()=>{m({type:"SETPATH",payload:{path:"cms"}})},[]),e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>r(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add CMS Content"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]",onClick:()=>r(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(g)(),r(!1)},disabled:c,children:c?"Saving":"Save"})]})]}),e.jsxs("form",{className:" w-full max-w-lg p-4 text-left",onSubmit:p(g),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"page",children:"Page"}),e.jsx("input",{type:"text",placeholder:"Page",...l("page"),className:`focus:shadow-outline } mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow
focus:outline-none`})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"key",children:"Content Identifier"}),e.jsx("input",{type:"text",placeholder:"Content Identifier",...l("key"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(h=x.key)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=x.key)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Content Type"}),e.jsx("select",{name:"type",id:"type",className:"focus:shadow-outline mb-3  w-full rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("type",{onChange:t=>E(t.target.value)}),children:i.map(t=>e.jsx("option",{name:t.name,value:t.key,children:t.value},t.key))})]}),e.jsx(F,{contentType:N,setContentValue:A})]})]})};export{ie as default};
