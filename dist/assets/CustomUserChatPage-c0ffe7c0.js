import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as s,R as u}from"./vendor-2ae44a2e.js";import{e as B,G as E,A as z,M as I,t as G,f as V}from"./index-b2ff2fa1.js";import"./index-33a9b201.js";import{G as f}from"./react-icons-f29df01f.js";import{B as O,a as W}from"./index.esm-de9a80b6.js";import{e as F}from"./index.esm-ae78e666.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";function _(t){return f({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"22",y1:"2",x2:"11",y2:"13"}},{tag:"polygon",attr:{points:"22 2 15 22 11 13 2 9 22 2"}}]})(t)}function D(t){return f({tag:"svg",attr:{viewBox:"0 0 15 15",fill:"none"},child:[{tag:"path",attr:{fillRule:"evenodd",clipRule:"evenodd",d:"M1.5 3C1.22386 3 1 3.22386 1 3.5C1 3.77614 1.22386 4 1.5 4H13.5C13.7761 4 14 3.77614 14 3.5C14 3.22386 13.7761 3 13.5 3H1.5ZM1 7.5C1 7.22386 1.22386 7 1.5 7H13.5C13.7761 7 14 7.22386 14 7.5C14 7.77614 13.7761 8 13.5 8H1.5C1.22386 8 1 7.77614 1 7.5ZM1 11.5C1 11.2239 1.22386 11 1.5 11H13.5C13.7761 11 14 11.2239 14 11.5C14 11.7761 13.7761 12 13.5 12H1.5C1.22386 12 1 11.7761 1 11.5Z",fill:"currentColor"}}]})(t)}function K(t){return f({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"title",attr:{},child:[]},{tag:"path",attr:{d:"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z"}}]})(t)}function Z(t){return f({tag:"svg",attr:{viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}},{tag:"path",attr:{d:"M10 12h4"}},{tag:"path",attr:{d:"M9 4a3 3 0 0 1 3 3v10a3 3 0 0 1 -3 3"}},{tag:"path",attr:{d:"M15 4a3 3 0 0 0 -3 3v10a3 3 0 0 0 3 3"}}]})(t)}const $=({phrase:t})=>{const a=t!==null&&t.split(" "),[d,r]=s.useState(0);return s.useEffect(()=>{if(t!==null){const n=setInterval(()=>{r(m=>m+1)},100);return()=>clearInterval(n)}},[]),e.jsx(e.Fragment,{children:t!==null?e.jsxs("span",{children:[a.map((n,m)=>e.jsx("span",{children:m<d?`${n} `:""},m)),d<a.length&&""]}):e.jsx("span",{className:"text-sm font-bold",children:"No Response, Try Again."})})},q=t=>{const{conversation:a,generating:d}=t,[r,n]=s.useState(0);return s.useEffect(()=>{n(a.length-1)},[a]),e.jsx(e.Fragment,{children:e.jsxs("div",{className:"mb- group w-full border-b border-black/10 text-gray-800 dark:text-gray-100 ",children:[e.jsxs("div",{className:"m-auto flex w-full items-center justify-center gap-4 py-2 text-base dark:border-gray-900/50 dark:bg-gray-500 sm:py-0 md:gap-6 lg:px-0",children:[e.jsxs("div",{className:"ml-4 flex gap-1 text-start text-xs",children:[e.jsx("button",{className:"text-gray-300 dark:text-gray-400",disabled:r+1===1,onClick:()=>n(r-1),children:"<"}),e.jsxs("span",{className:"flex-shrink-0 flex-grow",children:[r+1," / ",a==null?void 0:a.length]}),e.jsx("button",{disabled:r+1===a.length,className:"text-gray-300 dark:text-gray-400",onClick:()=>n(r+1),children:">"})]}),e.jsxs("div",{className:"my-auto flex w-full flex-row gap-4 pl-4 md:max-w-2xl md:gap-6 md:py-6 lg:max-w-xl lg:px-0 xl:max-w-3xl",children:[e.jsx("div",{className:"relative flex w-8 flex-col items-end",children:e.jsx("div",{className:"text-opacity-100r relative flex h-7 w-7 items-center justify-center rounded-sm bg-green-600 p-1 text-white",children:e.jsx(F,{className:"h-4 w-4 text-white"})})}),e.jsx("div",{className:"relative flex w-[calc(100%-50px)] flex-col gap-1 text-start md:gap-3 lg:w-[calc(100%-115px)]",children:e.jsx("div",{className:"flex flex-grow flex-col gap-3",children:e.jsx("div",{className:"min-h-20 flex flex-col items-start gap-4 whitespace-pre-wrap break-words",children:e.jsx("div",{className:"markdown prose dark:prose-invert dark w-full break-words",children:e.jsx("p",{children:a[r].content})})})})})]})]}),e.jsx("div",{className:"mr m-auto mt-2 flex w-full gap-4 text-base md:max-w-2xl md:gap-6 lg:max-w-xl lg:px-0 xl:max-w-3xl",children:e.jsxs("div",{className:"m-auto flex w-full flex-row gap-4 p-4 md:max-w-2xl md:gap-6 md:py-6 lg:max-w-xl lg:px-0 xl:max-w-3xl",children:[e.jsx("div",{className:"relative flex w-8 flex-col items-end",children:e.jsx("div",{className:"text-opacity-100r relative flex h-7 w-7 items-center justify-center rounded-sm bg-green-600 p-1 text-white",children:e.jsx(K,{className:"h-4 w-4 text-white"})})}),e.jsx("div",{className:"relative flex w-[calc(100%-50px)] flex-col gap-1 text-start md:gap-3 lg:w-[calc(100%-115px)]",children:e.jsx("div",{className:"flex flex-grow flex-col gap-3",children:e.jsx("div",{className:"min-h-20 flex flex-col items-start gap-4 whitespace-pre-wrap break-words",children:e.jsx("div",{className:"markdown prose dark:prose-invert dark w-full break-words",children:d?e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Z,{className:"h-6 w-6 animate-pulse"}),e.jsx("span",{className:"text-lg font-bold",children:"Generating Response..."})]}):e.jsx("p",{children:e.jsx($,{phrase:a[r].content2})})})})})})]})})]})})};function J(t){return f({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M85.57 446.25h340.86a32 32 0 0028.17-47.17L284.18 82.58c-12.09-22.44-44.27-22.44-56.36 0L57.4 399.08a32 32 0 0028.17 47.17z"}},{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M250.26 195.39l5.74 122 5.73-121.95a5.74 5.74 0 00-5.79-6h0a5.74 5.74 0 00-5.68 5.95z"}},{tag:"path",attr:{d:"M256 397.25a20 20 0 1120-20 20 20 0 01-20 20z"}}]})(t)}const Q=[{title:"Examples",icon:e.jsx(O,{}),details:["Explain Quantum Computing in simple terms"]},{title:"Capabilities",icon:e.jsx(B,{}),details:["Got any creative ideas for a ten year old birthday"]},{title:"Limitations",icon:e.jsx(J,{}),details:["How do I make an HTTP request using Javascript"]}],U=t=>{const{toggleComponentVisibility:a,index:d,currentRoom:r}=t,{state:n,dispatch:m}=s.useContext(E),{dispatch:N}=s.useContext(z),[k,g]=s.useState(!1),[w,x]=s.useState(""),[i,h]=s.useState(!0),[p,S]=s.useState([]),[o,M]=s.useState(""),[P,b]=s.useState(!1),C=s.useRef(null);let T=new I;s.useEffect(()=>{C.current&&C.current.scrollIntoView({behavior:"smooth"})},[p]);const R=async c=>{var v,j,y,L;if(o.length<1){x("Please enter a message.");return}else x(""),m({type:"SETROOM",payload:{position:t.index,value:o}});g(!0),S([...p,{content:o,role:"user",content2:null,role2:"system"}]),M(""),h(!1);try{b(!0);const l=await T.chatGPT(o);if(l.ok){const A=await l.json();b(!1),S([...p,{content:o,role:"user",content2:A.Answer,role2:"system"}])}else console.error(l),b(!1),x(l.statusText);g(!1)}catch(l){console.error(l),b(!1),x(l.message),G(N,(j=(v=l==null?void 0:l.response)==null?void 0:v.data)!=null&&j.messsage?(L=(y=l==null?void 0:l.response)==null?void 0:y.data)==null?void 0:L.messsage:l==null?void 0:l.message),g(!1)}},H=c=>{c.keyCode==13&&!c.shiftKey&&(R(),c.preventDefault())};return e.jsxs("div",{className:`${d!==r&&"hidden"} flex max-w-full flex-1 flex-col dark:bg-gray-800`,children:[e.jsxs("div",{className:"sticky top-0 z-10 flex items-center border-b border-white/20 bg-gray-800 pl-1 pt-1 text-gray-200 sm:pl-3 md:hidden",children:[e.jsxs("button",{type:"button",className:"-ml-0.5 -mt-0.5 inline-flex h-10 w-10 items-center justify-center rounded-md hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white dark:hover:text-white",onClick:a,children:[e.jsx("span",{className:"sr-only",children:"Open sidebar"}),e.jsx(D,{className:"h-6 w-6 text-white"})]}),e.jsx("h1",{className:"flex-1 text-center text-base font-normal",children:"New chat"}),e.jsx("button",{type:"button",className:"px-3",children:e.jsx(W,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"transition-width  relative mx-auto flex h-full w-full flex-1 flex-col items-stretch overflow-hidden",children:[e.jsx("div",{className:"overflow-scroll sm:h-[550px]",children:e.jsx("div",{className:"react-scroll-to-bottom--css-ikyem-79elbk dark:bg-gray-800",children:e.jsxs("div",{className:`react-scroll-to-bottom--css-ikyem-1n7m0yu ${!i&&p.length?" sm:mt-2":"sm:mt-20"}`,children:[!i&&p.length>0?e.jsxs("div",{className:"flex flex-col items-center bg-gray-800 text-sm",children:[e.jsx(q,{generating:P,conversation:p,message:o,genert:!0}),e.jsx("div",{className:"h-32 w-full flex-shrink-0 md:h-48"}),e.jsx("div",{ref:C})]}):null,i?e.jsx("div",{className:"relative mx-auto mt-[11rem]  h-full max-w-[90%] py-10 sm:mb-0 sm:max-w-[80%]",children:e.jsx("div",{className:"mt-5 items-center gap-4 sm:mt-10 sm:flex sm:flex-wrap sm:justify-center",children:Q.map((c,v)=>e.jsxs("div",{children:[e.jsxs("div",{className:"mt-4 flex items-center justify-center gap-2 sm:mt-0 sm:grid",children:[e.jsx("span",{className:"flex justify-center text-center text-2xl text-[white] sm:mt-4",children:c.icon}),e.jsx("h3",{className:"text-lg text-gray-500 sm:my-3",children:c.title})]}),e.jsx("div",{className:"mt-4 sm:mt-10",children:c.details.map((j,y)=>e.jsx("div",{className:"mb-4 w-full rounded-md bg-gray-500 px-2 py-4 text-sm text-gray-100 sm:my-4 sm:w-[250px]",children:j},y))})]},v))})}):null,e.jsx("div",{className:"flex flex-col items-center text-sm dark:bg-gray-800"})]})})}),e.jsxs("div",{className:"md:bg-vert-light-gradient dark:md:bg-vert-dark-gradient absolute bottom-0 left-0 w-full border-t bg-white pt-2 md:border-t-0 md:border-transparent md:!bg-transparent dark:border-white/20 dark:bg-gray-800 md:dark:border-transparent",children:[e.jsx("form",{className:"stretch mx-2 flex flex-row gap-3 last:mb-2 md:mx-4 md:last:mb-6 lg:mx-auto lg:max-w-2xl xl:max-w-3xl",children:e.jsx("div",{className:"relative flex h-full flex-1 flex-col items-stretch md:flex-col",children:e.jsxs("div",{className:"relative flex h-[55px] w-full flex-grow items-center rounded-md border border-black/10 bg-white px-4 shadow-[0_0_10px_rgba(0,0,0,0.10)] dark:border-gray-900/50 dark:bg-gray-700 dark:text-white dark:shadow-[0_0_15px_rgba(0,0,0,0.10)]",children:[e.jsx("textarea",{value:o,tabIndex:0,"data-id":"root",style:{height:"24px",maxHeight:"200px",overflowY:"hidden"},placeholder:"Send a message",className:"m-0 flex h-6 w-full resize-none items-center border-0 bg-transparent focus:outline-none focus:ring-0 focus-visible:ring-0 dark:bg-transparent",onChange:c=>M(c.target.value),onKeyDown:H}),e.jsx("button",{disabled:k||(o==null?void 0:o.length)===0,onClick:()=>R(),className:"absolute right-1 rounded-md bg-green-500 bg-transparent p-1 disabled:bg-gray-500 disabled:opacity-40 md:right-2",children:e.jsx(_,{className:"mr-1 h-4 w-4 text-white "})})]})})}),w?e.jsx("div",{className:"mb-2 md:mb-0",children:e.jsx("div",{className:"ml-1 flex h-full justify-center gap-0 md:m-auto md:mb-2 md:w-full md:gap-2",children:e.jsx("span",{className:"text-sm text-red-500",children:w})})}):null,e.jsx("div",{className:"px-3 pb-3 pt-2 text-center text-xs text-black/50 md:px-4 md:pb-6 md:pt-3 dark:text-white/50",children:e.jsx("span",{children:"ChatGPT may produce inaccurate information about people, places, or facts."})})]})]})]})};function Y(){const[t,a]=u.useState(["old"]),[d,r]=u.useState(""),[n,m]=u.useState(0),[N,k]=s.useState(!1);t.filter((i,h)=>h===n);let g=new I;const w=()=>{k(!N)},x=async()=>{try{const i=await g.runPodStatus();i.error||r(i.status)}catch(i){console.log(i)}};return u.useEffect(()=>{x()},[]),e.jsxs("div",{className:"relative text-center",children:[e.jsx("main",{className:"relative flex h-full w-full overflow-hidden",children:t.map((i,h)=>e.jsx(U,{index:h,currentRoom:n,toggleComponentVisibility:w},h))}),e.jsxs("div",{className:"absolute right-2 top-12 flex items-center gap-2 sm:right-4 sm:top-7",children:[e.jsxs("span",{className:"rounded-md bg-white p-2 text-xs font-bold",children:["RunPod: ",d]}),e.jsx(V,{onClick:()=>x(),className:"text-xl text-green-600 hover:cursor-pointer"})]})]})}const ve=()=>{s.useContext(z);const{state:t,dispatch:a}=s.useContext(E);return u.useEffect(()=>{a({type:"SETPATH",payload:{path:"chat"}})},[]),e.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:e.jsx("div",{className:" ",children:e.jsx(Y,{})})})};export{ve as default};
