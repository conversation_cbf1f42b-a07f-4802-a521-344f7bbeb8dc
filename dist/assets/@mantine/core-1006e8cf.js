import{r as S}from"../vendor-2ae44a2e.js";import{_ as $,a as _t,b as fe}from"../@fullcalendar/core-a789a586.js";var ut="right-scroll-bar-position",dt="width-before-scroll-bar",ue="with-scroll-bars-hidden",de="--removed-body-scroll-bar-size";function wt(t,e){return typeof t=="function"?t(e):t&&(t.current=e),t}function he(t,e){var n=S.useState(function(){return{value:t,callback:e,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=e,n.facade}var me=typeof window<"u"?S.useLayoutEffect:S.useEffect,Dt=new WeakMap;function ve(t,e){var n=he(e||null,function(r){return t.forEach(function(o){return wt(o,r)})});return me(function(){var r=Dt.get(n);if(r){var o=new Set(r),i=new Set(t),c=n.current;o.forEach(function(a){i.has(a)||wt(a,null)}),i.forEach(function(a){o.has(a)||wt(a,c)})}Dt.set(n,t)},[t]),n}function ge(t){return t}function pe(t,e){e===void 0&&(e=ge);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:t},useMedium:function(i){var c=e(i,r);return n.push(c),function(){n=n.filter(function(a){return a!==c})}},assignSyncMedium:function(i){for(r=!0;n.length;){var c=n;n=[],c.forEach(i)}n={push:function(a){return i(a)},filter:function(){return n}}},assignMedium:function(i){r=!0;var c=[];if(n.length){var a=n;n=[],a.forEach(i),c=n}var s=function(){var m=c;c=[],m.forEach(i)},u=function(){return Promise.resolve().then(s)};u(),n={push:function(m){c.push(m),u()},filter:function(m){return c=c.filter(m),n}}}};return o}function we(t){t===void 0&&(t={});var e=pe(null);return e.options=$({async:!0,ssr:!1},t),e}var jt=function(t){var e=t.sideCar,n=_t(t,["sideCar"]);if(!e)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=e.read();if(!r)throw new Error("Sidecar medium not found");return S.createElement(r,$({},n))};jt.isSideCarExport=!0;function ye(t,e){return t.useMedium(e),jt}var Zt=we(),yt=function(){},gt=S.forwardRef(function(t,e){var n=S.useRef(null),r=S.useState({onScrollCapture:yt,onWheelCapture:yt,onTouchMoveCapture:yt}),o=r[0],i=r[1],c=t.forwardProps,a=t.children,s=t.className,u=t.removeScrollBar,m=t.enabled,l=t.shards,h=t.sideCar,d=t.noIsolation,g=t.inert,f=t.allowPinchZoom,v=t.as,p=v===void 0?"div":v,b=t.gapMode,w=_t(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=h,y=ve([n,e]),C=$($({},w),o);return S.createElement(S.Fragment,null,m&&S.createElement(x,{sideCar:Zt,removeScrollBar:u,shards:l,noIsolation:d,inert:g,setCallbacks:i,allowPinchZoom:!!f,lockRef:n,gapMode:b}),c?S.cloneElement(S.Children.only(a),$($({},C),{ref:y})):S.createElement(p,$({},C,{className:s,ref:y}),a))});gt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};gt.classNames={fullWidth:dt,zeroRight:ut};var Wt,be=function(){if(Wt)return Wt;if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function xe(){if(!document)return null;var t=document.createElement("style");t.type="text/css";var e=be();return e&&t.setAttribute("nonce",e),t}function Se(t,e){t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e))}function Ce(t){var e=document.head||document.getElementsByTagName("head")[0];e.appendChild(t)}var Ae=function(){var t=0,e=null;return{add:function(n){t==0&&(e=xe())&&(Se(e,n),Ce(e)),t++},remove:function(){t--,!t&&e&&(e.parentNode&&e.parentNode.removeChild(e),e=null)}}},Ee=function(){var t=Ae();return function(e,n){S.useEffect(function(){return t.add(e),function(){t.remove()}},[e&&n])}},qt=function(){var t=Ee(),e=function(n){var r=n.styles,o=n.dynamic;return t(r,o),null};return e},Re={left:0,top:0,right:0,gap:0},bt=function(t){return parseInt(t||"",10)||0},ke=function(t){var e=window.getComputedStyle(document.body),n=e[t==="padding"?"paddingLeft":"marginLeft"],r=e[t==="padding"?"paddingTop":"marginTop"],o=e[t==="padding"?"paddingRight":"marginRight"];return[bt(n),bt(r),bt(o)]},Oe=function(t){if(t===void 0&&(t="margin"),typeof window>"u")return Re;var e=ke(t),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:e[0],top:e[1],right:e[2],gap:Math.max(0,r-n+e[2]-e[0])}},Le=qt(),Q="data-scroll-locked",Te=function(t,e,n,r){var o=t.left,i=t.top,c=t.right,a=t.gap;return n===void 0&&(n="margin"),`
  .`.concat(ue,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(Q,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([e&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(ut,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(dt,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(ut," .").concat(ut,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(dt," .").concat(dt,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Q,`] {
    `).concat(de,": ").concat(a,`px;
  }
`)},Bt=function(){var t=parseInt(document.body.getAttribute(Q)||"0",10);return isFinite(t)?t:0},Pe=function(){S.useEffect(function(){return document.body.setAttribute(Q,(Bt()+1).toString()),function(){var t=Bt()-1;t<=0?document.body.removeAttribute(Q):document.body.setAttribute(Q,t.toString())}},[])},Me=function(t){var e=t.noRelative,n=t.noImportant,r=t.gapMode,o=r===void 0?"margin":r;Pe();var i=S.useMemo(function(){return Oe(o)},[o]);return S.createElement(Le,{styles:Te(i,!e,o,n?"":"!important")})},Ct=!1;if(typeof window<"u")try{var ct=Object.defineProperty({},"passive",{get:function(){return Ct=!0,!0}});window.addEventListener("test",ct,ct),window.removeEventListener("test",ct,ct)}catch{Ct=!1}var Z=Ct?{passive:!1}:!1,De=function(t){return t.tagName==="TEXTAREA"},Kt=function(t,e){if(!(t instanceof Element))return!1;var n=window.getComputedStyle(t);return n[e]!=="hidden"&&!(n.overflowY===n.overflowX&&!De(t)&&n[e]==="visible")},We=function(t){return Kt(t,"overflowY")},Be=function(t){return Kt(t,"overflowX")},Nt=function(t,e){var n=e.ownerDocument,r=e;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Qt(t,r);if(o){var i=Ut(t,r),c=i[1],a=i[2];if(c>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Ne=function(t){var e=t.scrollTop,n=t.scrollHeight,r=t.clientHeight;return[e,n,r]},Fe=function(t){var e=t.scrollLeft,n=t.scrollWidth,r=t.clientWidth;return[e,n,r]},Qt=function(t,e){return t==="v"?We(e):Be(e)},Ut=function(t,e){return t==="v"?Ne(e):Fe(e)},He=function(t,e){return t==="h"&&e==="rtl"?-1:1},Ve=function(t,e,n,r,o){var i=He(t,window.getComputedStyle(e).direction),c=i*r,a=n.target,s=e.contains(a),u=!1,m=c>0,l=0,h=0;do{var d=Ut(t,a),g=d[0],f=d[1],v=d[2],p=f-v-i*g;(g||p)&&Qt(t,a)&&(l+=p,h+=g),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!s&&a!==document.body||s&&(e.contains(a)||e===a));return(m&&(o&&Math.abs(l)<1||!o&&c>l)||!m&&(o&&Math.abs(h)<1||!o&&-c>h))&&(u=!0),u},at=function(t){return"changedTouches"in t?[t.changedTouches[0].clientX,t.changedTouches[0].clientY]:[0,0]},Ft=function(t){return[t.deltaX,t.deltaY]},Ht=function(t){return t&&"current"in t?t.current:t},Ie=function(t,e){return t[0]===e[0]&&t[1]===e[1]},ze=function(t){return`
  .block-interactivity-`.concat(t,` {pointer-events: none;}
  .allow-interactivity-`).concat(t,` {pointer-events: all;}
`)},Ye=0,q=[];function $e(t){var e=S.useRef([]),n=S.useRef([0,0]),r=S.useRef(),o=S.useState(Ye++)[0],i=S.useState(qt)[0],c=S.useRef(t);S.useEffect(function(){c.current=t},[t]),S.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(o));var f=fe([t.lockRef.current],(t.shards||[]).map(Ht),!0).filter(Boolean);return f.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),f.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[t.inert,t.lockRef.current,t.shards]);var a=S.useCallback(function(f,v){if("touches"in f&&f.touches.length===2||f.type==="wheel"&&f.ctrlKey)return!c.current.allowPinchZoom;var p=at(f),b=n.current,w="deltaX"in f?f.deltaX:b[0]-p[0],x="deltaY"in f?f.deltaY:b[1]-p[1],y,C=f.target,A=Math.abs(w)>Math.abs(x)?"h":"v";if("touches"in f&&A==="h"&&C.type==="range")return!1;var E=Nt(A,C);if(!E)return!0;if(E?y=A:(y=A==="v"?"h":"v",E=Nt(A,C)),!E)return!1;if(!r.current&&"changedTouches"in f&&(w||x)&&(r.current=y),!y)return!0;var T=r.current||y;return Ve(T,v,f,T==="h"?w:x,!0)},[]),s=S.useCallback(function(f){var v=f;if(!(!q.length||q[q.length-1]!==i)){var p="deltaY"in v?Ft(v):at(v),b=e.current.filter(function(y){return y.name===v.type&&(y.target===v.target||v.target===y.shadowParent)&&Ie(y.delta,p)})[0];if(b&&b.should){v.cancelable&&v.preventDefault();return}if(!b){var w=(c.current.shards||[]).map(Ht).filter(Boolean).filter(function(y){return y.contains(v.target)}),x=w.length>0?a(v,w[0]):!c.current.noIsolation;x&&v.cancelable&&v.preventDefault()}}},[]),u=S.useCallback(function(f,v,p,b){var w={name:f,delta:v,target:p,should:b,shadowParent:Xe(p)};e.current.push(w),setTimeout(function(){e.current=e.current.filter(function(x){return x!==w})},1)},[]),m=S.useCallback(function(f){n.current=at(f),r.current=void 0},[]),l=S.useCallback(function(f){u(f.type,Ft(f),f.target,a(f,t.lockRef.current))},[]),h=S.useCallback(function(f){u(f.type,at(f),f.target,a(f,t.lockRef.current))},[]);S.useEffect(function(){return q.push(i),t.setCallbacks({onScrollCapture:l,onWheelCapture:l,onTouchMoveCapture:h}),document.addEventListener("wheel",s,Z),document.addEventListener("touchmove",s,Z),document.addEventListener("touchstart",m,Z),function(){q=q.filter(function(f){return f!==i}),document.removeEventListener("wheel",s,Z),document.removeEventListener("touchmove",s,Z),document.removeEventListener("touchstart",m,Z)}},[]);var d=t.removeScrollBar,g=t.inert;return S.createElement(S.Fragment,null,g?S.createElement(i,{styles:ze(o)}):null,d?S.createElement(Me,{gapMode:t.gapMode}):null)}function Xe(t){for(var e=null;t!==null;)t instanceof ShadowRoot&&(e=t.host,t=t.host),t=t.parentNode;return e}const _e=ye(Zt,$e);var Gt=S.forwardRef(function(t,e){return S.createElement(gt,$({},t,{ref:e,sideCar:_e}))});Gt.classNames=gt.classNames;const Wn=Gt;var je=function(t){if(typeof document>"u")return null;var e=Array.isArray(t)?t[0]:t;return e.ownerDocument.body},K=new WeakMap,st=new WeakMap,lt={},xt=0,Jt=function(t){return t&&(t.host||Jt(t.parentNode))},Ze=function(t,e){return e.map(function(n){if(t.contains(n))return n;var r=Jt(n);return r&&t.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",t,". Doing nothing"),null)}).filter(function(n){return!!n})},qe=function(t,e,n,r){var o=Ze(e,Array.isArray(t)?t:[t]);lt[n]||(lt[n]=new WeakMap);var i=lt[n],c=[],a=new Set,s=new Set(o),u=function(l){!l||a.has(l)||(a.add(l),u(l.parentNode))};o.forEach(u);var m=function(l){!l||s.has(l)||Array.prototype.forEach.call(l.children,function(h){if(a.has(h))m(h);else try{var d=h.getAttribute(r),g=d!==null&&d!=="false",f=(K.get(h)||0)+1,v=(i.get(h)||0)+1;K.set(h,f),i.set(h,v),c.push(h),f===1&&g&&st.set(h,!0),v===1&&h.setAttribute(n,"true"),g||h.setAttribute(r,"true")}catch(p){console.error("aria-hidden: cannot operate on ",h,p)}})};return m(e),a.clear(),xt++,function(){c.forEach(function(l){var h=K.get(l)-1,d=i.get(l)-1;K.set(l,h),i.set(l,d),h||(st.has(l)||l.removeAttribute(r),st.delete(l)),d||l.removeAttribute(n)}),xt--,xt||(K=new WeakMap,K=new WeakMap,st=new WeakMap,lt={})}},Bn=function(t,e,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(t)?t:[t]),o=e||je(t);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),qe(r,o,n,"aria-hidden")):function(){return null}};function te(t){var e,n,r="";if(typeof t=="string"||typeof t=="number")r+=t;else if(typeof t=="object")if(Array.isArray(t))for(e=0;e<t.length;e++)t[e]&&(n=te(t[e]))&&(r&&(r+=" "),r+=n);else for(e in t)t[e]&&(r&&(r+=" "),r+=e);return r}function Nn(){for(var t=0,e,n,r="";t<arguments.length;)(e=arguments[t++])&&(n=te(e))&&(r&&(r+=" "),r+=n);return r}const Ke=["top","right","bottom","left"],W=Math.min,k=Math.max,ht=Math.round,ft=Math.floor,X=t=>({x:t,y:t}),Qe={left:"right",right:"left",bottom:"top",top:"bottom"},Ue={start:"end",end:"start"};function At(t,e,n){return k(t,W(e,n))}function H(t,e){return typeof t=="function"?t(e):t}function V(t){return t.split("-")[0]}function J(t){return t.split("-")[1]}function Rt(t){return t==="x"?"y":"x"}function kt(t){return t==="y"?"height":"width"}function tt(t){return["top","bottom"].includes(V(t))?"y":"x"}function Ot(t){return Rt(tt(t))}function Ge(t,e,n){n===void 0&&(n=!1);const r=J(t),o=Ot(t),i=kt(o);let c=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return e.reference[i]>e.floating[i]&&(c=mt(c)),[c,mt(c)]}function Je(t){const e=mt(t);return[Et(t),e,Et(e)]}function Et(t){return t.replace(/start|end/g,e=>Ue[e])}function tn(t,e,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],c=["bottom","top"];switch(t){case"top":case"bottom":return n?e?o:r:e?r:o;case"left":case"right":return e?i:c;default:return[]}}function en(t,e,n,r){const o=J(t);let i=tn(V(t),n==="start",r);return o&&(i=i.map(c=>c+"-"+o),e&&(i=i.concat(i.map(Et)))),i}function mt(t){return t.replace(/left|right|bottom|top/g,e=>Qe[e])}function nn(t){return{top:0,right:0,bottom:0,left:0,...t}}function ee(t){return typeof t!="number"?nn(t):{top:t,right:t,bottom:t,left:t}}function vt(t){const{x:e,y:n,width:r,height:o}=t;return{width:r,height:o,top:n,left:e,right:e+r,bottom:n+o,x:e,y:n}}function Vt(t,e,n){let{reference:r,floating:o}=t;const i=tt(e),c=Ot(e),a=kt(c),s=V(e),u=i==="y",m=r.x+r.width/2-o.width/2,l=r.y+r.height/2-o.height/2,h=r[a]/2-o[a]/2;let d;switch(s){case"top":d={x:m,y:r.y-o.height};break;case"bottom":d={x:m,y:r.y+r.height};break;case"right":d={x:r.x+r.width,y:l};break;case"left":d={x:r.x-o.width,y:l};break;default:d={x:r.x,y:r.y}}switch(J(e)){case"start":d[c]-=h*(n&&u?-1:1);break;case"end":d[c]+=h*(n&&u?-1:1);break}return d}const rn=async(t,e,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:c}=n,a=i.filter(Boolean),s=await(c.isRTL==null?void 0:c.isRTL(e));let u=await c.getElementRects({reference:t,floating:e,strategy:o}),{x:m,y:l}=Vt(u,r,s),h=r,d={},g=0;for(let f=0;f<a.length;f++){const{name:v,fn:p}=a[f],{x:b,y:w,data:x,reset:y}=await p({x:m,y:l,initialPlacement:r,placement:h,strategy:o,middlewareData:d,rects:u,platform:c,elements:{reference:t,floating:e}});m=b??m,l=w??l,d={...d,[v]:{...d[v],...x}},y&&g<=50&&(g++,typeof y=="object"&&(y.placement&&(h=y.placement),y.rects&&(u=y.rects===!0?await c.getElementRects({reference:t,floating:e,strategy:o}):y.rects),{x:m,y:l}=Vt(u,h,s)),f=-1)}return{x:m,y:l,placement:h,strategy:o,middlewareData:d}};async function rt(t,e){var n;e===void 0&&(e={});const{x:r,y:o,platform:i,rects:c,elements:a,strategy:s}=t,{boundary:u="clippingAncestors",rootBoundary:m="viewport",elementContext:l="floating",altBoundary:h=!1,padding:d=0}=H(e,t),g=ee(d),v=a[h?l==="floating"?"reference":"floating":l],p=vt(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:m,strategy:s})),b=l==="floating"?{x:r,y:o,width:c.floating.width,height:c.floating.height}:c.reference,w=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),x=await(i.isElement==null?void 0:i.isElement(w))?await(i.getScale==null?void 0:i.getScale(w))||{x:1,y:1}:{x:1,y:1},y=vt(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:w,strategy:s}):b);return{top:(p.top-y.top+g.top)/x.y,bottom:(y.bottom-p.bottom+g.bottom)/x.y,left:(p.left-y.left+g.left)/x.x,right:(y.right-p.right+g.right)/x.x}}const on=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:r,placement:o,rects:i,platform:c,elements:a,middlewareData:s}=e,{element:u,padding:m=0}=H(t,e)||{};if(u==null)return{};const l=ee(m),h={x:n,y:r},d=Ot(o),g=kt(d),f=await c.getDimensions(u),v=d==="y",p=v?"top":"left",b=v?"bottom":"right",w=v?"clientHeight":"clientWidth",x=i.reference[g]+i.reference[d]-h[d]-i.floating[g],y=h[d]-i.reference[d],C=await(c.getOffsetParent==null?void 0:c.getOffsetParent(u));let A=C?C[w]:0;(!A||!await(c.isElement==null?void 0:c.isElement(C)))&&(A=a.floating[w]||i.floating[g]);const E=x/2-y/2,T=A/2-f[g]/2-1,z=W(l[p],T),Y=W(l[b],T),L=z,nt=A-f[g]-Y,R=A/2-f[g]/2+E,M=At(L,R,nt),D=!s.arrow&&J(o)!=null&&R!==M&&i.reference[g]/2-(R<L?z:Y)-f[g]/2<0,F=D?R<L?R-L:R-nt:0;return{[d]:h[d]+F,data:{[d]:M,centerOffset:R-M-F,...D&&{alignmentOffset:F}},reset:D}}}),cn=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,r;const{placement:o,middlewareData:i,rects:c,initialPlacement:a,platform:s,elements:u}=e,{mainAxis:m=!0,crossAxis:l=!0,fallbackPlacements:h,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:f=!0,...v}=H(t,e);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const p=V(o),b=V(a)===a,w=await(s.isRTL==null?void 0:s.isRTL(u.floating)),x=h||(b||!f?[mt(a)]:Je(a));!h&&g!=="none"&&x.push(...en(a,f,g,w));const y=[a,...x],C=await rt(e,v),A=[];let E=((r=i.flip)==null?void 0:r.overflows)||[];if(m&&A.push(C[p]),l){const L=Ge(o,c,w);A.push(C[L[0]],C[L[1]])}if(E=[...E,{placement:o,overflows:A}],!A.every(L=>L<=0)){var T,z;const L=(((T=i.flip)==null?void 0:T.index)||0)+1,nt=y[L];if(nt)return{data:{index:L,overflows:E},reset:{placement:nt}};let R=(z=E.filter(M=>M.overflows[0]<=0).sort((M,D)=>M.overflows[1]-D.overflows[1])[0])==null?void 0:z.placement;if(!R)switch(d){case"bestFit":{var Y;const M=(Y=E.map(D=>[D.placement,D.overflows.filter(F=>F>0).reduce((F,le)=>F+le,0)]).sort((D,F)=>D[1]-F[1])[0])==null?void 0:Y[0];M&&(R=M);break}case"initialPlacement":R=a;break}if(o!==R)return{reset:{placement:R}}}return{}}}};function It(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function zt(t){return Ke.some(e=>t[e]>=0)}const an=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:r="referenceHidden",...o}=H(t,e);switch(r){case"referenceHidden":{const i=await rt(e,{...o,elementContext:"reference"}),c=It(i,n.reference);return{data:{referenceHiddenOffsets:c,referenceHidden:zt(c)}}}case"escaped":{const i=await rt(e,{...o,altBoundary:!0}),c=It(i,n.floating);return{data:{escapedOffsets:c,escaped:zt(c)}}}default:return{}}}}};async function sn(t,e){const{placement:n,platform:r,elements:o}=t,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),c=V(n),a=J(n),s=tt(n)==="y",u=["left","top"].includes(c)?-1:1,m=i&&s?-1:1,l=H(e,t);let{mainAxis:h,crossAxis:d,alignmentAxis:g}=typeof l=="number"?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...l};return a&&typeof g=="number"&&(d=a==="end"?g*-1:g),s?{x:d*m,y:h*u}:{x:h*u,y:d*m}}const ln=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,r;const{x:o,y:i,placement:c,middlewareData:a}=e,s=await sn(e,t);return c===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:c}}}}},fn=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:c=!1,limiter:a={fn:v=>{let{x:p,y:b}=v;return{x:p,y:b}}},...s}=H(t,e),u={x:n,y:r},m=await rt(e,s),l=tt(V(o)),h=Rt(l);let d=u[h],g=u[l];if(i){const v=h==="y"?"top":"left",p=h==="y"?"bottom":"right",b=d+m[v],w=d-m[p];d=At(b,d,w)}if(c){const v=l==="y"?"top":"left",p=l==="y"?"bottom":"right",b=g+m[v],w=g-m[p];g=At(b,g,w)}const f=a.fn({...e,[h]:d,[l]:g});return{...f,data:{x:f.x-n,y:f.y-r}}}}},un=function(t){return t===void 0&&(t={}),{options:t,fn(e){const{x:n,y:r,placement:o,rects:i,middlewareData:c}=e,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=H(t,e),m={x:n,y:r},l=tt(o),h=Rt(l);let d=m[h],g=m[l];const f=H(a,e),v=typeof f=="number"?{mainAxis:f,crossAxis:0}:{mainAxis:0,crossAxis:0,...f};if(s){const w=h==="y"?"height":"width",x=i.reference[h]-i.floating[w]+v.mainAxis,y=i.reference[h]+i.reference[w]-v.mainAxis;d<x?d=x:d>y&&(d=y)}if(u){var p,b;const w=h==="y"?"width":"height",x=["top","left"].includes(V(o)),y=i.reference[l]-i.floating[w]+(x&&((p=c.offset)==null?void 0:p[l])||0)+(x?0:v.crossAxis),C=i.reference[l]+i.reference[w]+(x?0:((b=c.offset)==null?void 0:b[l])||0)-(x?v.crossAxis:0);g<y?g=y:g>C&&(g=C)}return{[h]:d,[l]:g}}}},dn=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){const{placement:n,rects:r,platform:o,elements:i}=e,{apply:c=()=>{},...a}=H(t,e),s=await rt(e,a),u=V(n),m=J(n),l=tt(n)==="y",{width:h,height:d}=r.floating;let g,f;u==="top"||u==="bottom"?(g=u,f=m===(await(o.isRTL==null?void 0:o.isRTL(i.floating))?"start":"end")?"left":"right"):(f=u,g=m==="end"?"top":"bottom");const v=d-s.top-s.bottom,p=h-s.left-s.right,b=W(d-s[g],v),w=W(h-s[f],p),x=!e.middlewareData.shift;let y=b,C=w;if(l?C=m||x?W(w,p):p:y=m||x?W(b,v):v,x&&!m){const E=k(s.left,0),T=k(s.right,0),z=k(s.top,0),Y=k(s.bottom,0);l?C=h-2*(E!==0||T!==0?E+T:k(s.left,s.right)):y=d-2*(z!==0||Y!==0?z+Y:k(s.top,s.bottom))}await c({...e,availableWidth:C,availableHeight:y});const A=await o.getDimensions(i.floating);return h!==A.width||d!==A.height?{reset:{rects:!0}}:{}}}};function et(t){return ne(t)?(t.nodeName||"").toLowerCase():"#document"}function O(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function I(t){var e;return(e=(ne(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function ne(t){return t instanceof Node||t instanceof O(t).Node}function B(t){return t instanceof Element||t instanceof O(t).Element}function N(t){return t instanceof HTMLElement||t instanceof O(t).HTMLElement}function Yt(t){return typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof O(t).ShadowRoot}function it(t){const{overflow:e,overflowX:n,overflowY:r,display:o}=P(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(o)}function hn(t){return["table","td","th"].includes(et(t))}function mn(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function Lt(t){const e=Tt(),n=P(t);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function vn(t){let e=_(t);for(;N(e)&&!G(e);){if(mn(e))return null;if(Lt(e))return e;e=_(e)}return null}function Tt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function G(t){return["html","body","#document"].includes(et(t))}function P(t){return O(t).getComputedStyle(t)}function pt(t){return B(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function _(t){if(et(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Yt(t)&&t.host||I(t);return Yt(e)?e.host:e}function re(t){const e=_(t);return G(e)?t.ownerDocument?t.ownerDocument.body:t.body:N(e)&&it(e)?e:re(e)}function ot(t,e,n){var r;e===void 0&&(e=[]),n===void 0&&(n=!0);const o=re(t),i=o===((r=t.ownerDocument)==null?void 0:r.body),c=O(o);return i?e.concat(c,c.visualViewport||[],it(o)?o:[],c.frameElement&&n?ot(c.frameElement):[]):e.concat(o,ot(o,[],n))}function oe(t){const e=P(t);let n=parseFloat(e.width)||0,r=parseFloat(e.height)||0;const o=N(t),i=o?t.offsetWidth:n,c=o?t.offsetHeight:r,a=ht(n)!==i||ht(r)!==c;return a&&(n=i,r=c),{width:n,height:r,$:a}}function Pt(t){return B(t)?t:t.contextElement}function U(t){const e=Pt(t);if(!N(e))return X(1);const n=e.getBoundingClientRect(),{width:r,height:o,$:i}=oe(e);let c=(i?ht(n.width):n.width)/r,a=(i?ht(n.height):n.height)/o;return(!c||!Number.isFinite(c))&&(c=1),(!a||!Number.isFinite(a))&&(a=1),{x:c,y:a}}const gn=X(0);function ie(t){const e=O(t);return!Tt()||!e.visualViewport?gn:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function pn(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==O(t)?!1:e}function j(t,e,n,r){e===void 0&&(e=!1),n===void 0&&(n=!1);const o=t.getBoundingClientRect(),i=Pt(t);let c=X(1);e&&(r?B(r)&&(c=U(r)):c=U(t));const a=pn(i,n,r)?ie(i):X(0);let s=(o.left+a.x)/c.x,u=(o.top+a.y)/c.y,m=o.width/c.x,l=o.height/c.y;if(i){const h=O(i),d=r&&B(r)?O(r):r;let g=h,f=g.frameElement;for(;f&&r&&d!==g;){const v=U(f),p=f.getBoundingClientRect(),b=P(f),w=p.left+(f.clientLeft+parseFloat(b.paddingLeft))*v.x,x=p.top+(f.clientTop+parseFloat(b.paddingTop))*v.y;s*=v.x,u*=v.y,m*=v.x,l*=v.y,s+=w,u+=x,g=O(f),f=g.frameElement}}return vt({width:m,height:l,x:s,y:u})}const wn=[":popover-open",":modal"];function Mt(t){return wn.some(e=>{try{return t.matches(e)}catch{return!1}})}function yn(t){let{elements:e,rect:n,offsetParent:r,strategy:o}=t;const i=o==="fixed",c=I(r),a=e?Mt(e.floating):!1;if(r===c||a&&i)return n;let s={scrollLeft:0,scrollTop:0},u=X(1);const m=X(0),l=N(r);if((l||!l&&!i)&&((et(r)!=="body"||it(c))&&(s=pt(r)),N(r))){const h=j(r);u=U(r),m.x=h.x+r.clientLeft,m.y=h.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+m.x,y:n.y*u.y-s.scrollTop*u.y+m.y}}function bn(t){return Array.from(t.getClientRects())}function ce(t){return j(I(t)).left+pt(t).scrollLeft}function xn(t){const e=I(t),n=pt(t),r=t.ownerDocument.body,o=k(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),i=k(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight);let c=-n.scrollLeft+ce(t);const a=-n.scrollTop;return P(r).direction==="rtl"&&(c+=k(e.clientWidth,r.clientWidth)-o),{width:o,height:i,x:c,y:a}}function Sn(t,e){const n=O(t),r=I(t),o=n.visualViewport;let i=r.clientWidth,c=r.clientHeight,a=0,s=0;if(o){i=o.width,c=o.height;const u=Tt();(!u||u&&e==="fixed")&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:c,x:a,y:s}}function Cn(t,e){const n=j(t,!0,e==="fixed"),r=n.top+t.clientTop,o=n.left+t.clientLeft,i=N(t)?U(t):X(1),c=t.clientWidth*i.x,a=t.clientHeight*i.y,s=o*i.x,u=r*i.y;return{width:c,height:a,x:s,y:u}}function $t(t,e,n){let r;if(e==="viewport")r=Sn(t,n);else if(e==="document")r=xn(I(t));else if(B(e))r=Cn(e,n);else{const o=ie(t);r={...e,x:e.x-o.x,y:e.y-o.y}}return vt(r)}function ae(t,e){const n=_(t);return n===e||!B(n)||G(n)?!1:P(n).position==="fixed"||ae(n,e)}function An(t,e){const n=e.get(t);if(n)return n;let r=ot(t,[],!1).filter(a=>B(a)&&et(a)!=="body"),o=null;const i=P(t).position==="fixed";let c=i?_(t):t;for(;B(c)&&!G(c);){const a=P(c),s=Lt(c);!s&&a.position==="fixed"&&(o=null),(i?!s&&!o:!s&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||it(c)&&!s&&ae(t,c))?r=r.filter(m=>m!==c):o=a,c=_(c)}return e.set(t,r),r}function En(t){let{element:e,boundary:n,rootBoundary:r,strategy:o}=t;const c=[...n==="clippingAncestors"?Mt(e)?[]:An(e,this._c):[].concat(n),r],a=c[0],s=c.reduce((u,m)=>{const l=$t(e,m,o);return u.top=k(l.top,u.top),u.right=W(l.right,u.right),u.bottom=W(l.bottom,u.bottom),u.left=k(l.left,u.left),u},$t(e,a,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}}function Rn(t){const{width:e,height:n}=oe(t);return{width:e,height:n}}function kn(t,e,n){const r=N(e),o=I(e),i=n==="fixed",c=j(t,!0,i,e);let a={scrollLeft:0,scrollTop:0};const s=X(0);if(r||!r&&!i)if((et(e)!=="body"||it(o))&&(a=pt(e)),r){const l=j(e,!0,i,e);s.x=l.x+e.clientLeft,s.y=l.y+e.clientTop}else o&&(s.x=ce(o));const u=c.left+a.scrollLeft-s.x,m=c.top+a.scrollTop-s.y;return{x:u,y:m,width:c.width,height:c.height}}function St(t){return P(t).position==="static"}function Xt(t,e){return!N(t)||P(t).position==="fixed"?null:e?e(t):t.offsetParent}function se(t,e){const n=O(t);if(Mt(t))return n;if(!N(t)){let o=_(t);for(;o&&!G(o);){if(B(o)&&!St(o))return o;o=_(o)}return n}let r=Xt(t,e);for(;r&&hn(r)&&St(r);)r=Xt(r,e);return r&&G(r)&&St(r)&&!Lt(r)?n:r||vn(t)||n}const On=async function(t){const e=this.getOffsetParent||se,n=this.getDimensions,r=await n(t.floating);return{reference:kn(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Ln(t){return P(t).direction==="rtl"}const Tn={convertOffsetParentRelativeRectToViewportRelativeRect:yn,getDocumentElement:I,getClippingRect:En,getOffsetParent:se,getElementRects:On,getClientRects:bn,getDimensions:Rn,getScale:U,isElement:B,isRTL:Ln};function Pn(t,e){let n=null,r;const o=I(t);function i(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function c(a,s){a===void 0&&(a=!1),s===void 0&&(s=1),i();const{left:u,top:m,width:l,height:h}=t.getBoundingClientRect();if(a||e(),!l||!h)return;const d=ft(m),g=ft(o.clientWidth-(u+l)),f=ft(o.clientHeight-(m+h)),v=ft(u),b={rootMargin:-d+"px "+-g+"px "+-f+"px "+-v+"px",threshold:k(0,W(1,s))||1};let w=!0;function x(y){const C=y[0].intersectionRatio;if(C!==s){if(!w)return c();C?c(!1,C):r=setTimeout(()=>{c(!1,1e-7)},1e3)}w=!1}try{n=new IntersectionObserver(x,{...b,root:o.ownerDocument})}catch{n=new IntersectionObserver(x,b)}n.observe(t)}return c(!0),i}function Fn(t,e,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:c=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:s=!1}=r,u=Pt(t),m=o||i?[...u?ot(u):[],...ot(e)]:[];m.forEach(p=>{o&&p.addEventListener("scroll",n,{passive:!0}),i&&p.addEventListener("resize",n)});const l=u&&a?Pn(u,n):null;let h=-1,d=null;c&&(d=new ResizeObserver(p=>{let[b]=p;b&&b.target===u&&d&&(d.unobserve(e),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var w;(w=d)==null||w.observe(e)})),n()}),u&&!s&&d.observe(u),d.observe(e));let g,f=s?j(t):null;s&&v();function v(){const p=j(t);f&&(p.x!==f.x||p.y!==f.y||p.width!==f.width||p.height!==f.height)&&n(),f=p,g=requestAnimationFrame(v)}return n(),()=>{var p;m.forEach(b=>{o&&b.removeEventListener("scroll",n),i&&b.removeEventListener("resize",n)}),l==null||l(),(p=d)==null||p.disconnect(),d=null,s&&cancelAnimationFrame(g)}}const Hn=ln,Vn=fn,In=cn,zn=dn,Yn=an,$n=on,Xn=un,_n=(t,e,n)=>{const r=new Map,o={platform:Tn,...n},i={...o.platform,_c:r};return rn(t,e,{...o,platform:i})};export{Wn as R,zn as a,Yn as b,_n as c,$n as d,Fn as e,In as f,Nn as g,Bn as h,Xn as l,Hn as o,Vn as s};
