import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as a,r as w,i as Ce,L as Se}from"./vendor-2ae44a2e.js";import{M as ue,G as be,A as we,X as je,s as M,T as Te,t as oe,B as Ae,a as Pe,b as _e,l as Ee,R as Ie,c as Fe,m as ke,n as De,P as Me}from"./index-b2ff2fa1.js";import{A as Le}from"./index-e429b426.js";import{C as ze}from"./CustomDeleteModal-536cae45.js";import{u as ve}from"./react-hook-form-47c010f8.js";import{o as Re}from"./yup-5abd4662.js";import{c as Ue,a as ce}from"./yup-5c93ed04.js";import{M as ee}from"./MkdInput-a584fac2.js";import{I as Be}from"./InteractiveButton-bff38983.js";import{z as Oe}from"./react-papaparse-b60a38ab.js";import{u as Ve}from"./react-dropzone-7ee839ba.js";import{P as qe}from"./pizzip-fcee35b8.js";import{D as Xe}from"./@xmldom/xmldom-6a8067e2.js";import{_ as Ge}from"./react-pdftotext-3aea4f3a.js";import{I as $e,D as de}from"./InformationCircleIcon-620be23d.js";import{e as Ke,f as Ze,X as We}from"./lucide-react-1246a7ed.js";import{q as N,_,C as te}from"./@headlessui/react-7bce1936.js";import"./UserAddOutboundCampaignPage-0d381a13.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./papaparse-2d1475f9.js";import"./@hookform/resolvers-d6373084.js";import"./MoonLoader-62b0139a.js";import"./@fullcalendar/core-a789a586.js";import"./react-pdf-9659a983.js";import"./@react-pdf-viewer/core-ad80e4c3.js";import"./@craftjs/core-9da1c17f.js";import"./react-calendar-366f51e4.js";import"./moment-timezone-69cd48a8.js";import"./moment-55cb88ed.js";import"./TrashIcon-aa291073.js";let R=new ue;const xe={"English (USA)":"English (USA)","English (UK)":"English (UK)","English (Australia)":"English (Australia)","English (Canada)":"English (Canada)",Japanese:"Japanese",Chinese:"Chinese",German:"German",Hindi:"Hindi","French (France)":"French (France)","French (Canada)":"French (Canada)",Korean:"Korean","Portuguese (Brazil)":"Portuguese (Brazil)","Portuguese (Portugal)":"Portuguese (Portugal)",Italian:"Italian","Spanish (Spain)":"Spanish (Spain)","Spanish (Mexico)":"Spanish (Mexico)",Indonesian:"Indonesian",Dutch:"Dutch",Turkish:"Turkish",Filipino:"Filipino",Polish:"Polish",Swedish:"Swedish",Bulgarian:"Bulgarian",Romanian:"Romanian","Arabic (Saudi Arabia)":"Arabic (Saudi Arabia)","Arabic (UAE)":"Arabic (UAE)",Czech:"Czech",Greek:"Greek",Finnish:"Finnish",Croatian:"Croatian",Malay:"Malay",Slovak:"Slovak",Danish:"Danish",Tamil:"Tamil",Ukrainian:"Ukrainian",Russian:"Russian"},He=({isOpen:U,closeSidebar:C})=>{const{dispatch:j}=a.useContext(be),{state:B,dispatch:O}=a.useContext(we),v=Ue({name:ce(),assistant_id:ce(),language:ce().required("Language is required")}).required(),[E,ae]=a.useState([]);Oe(),a.useState(null),a.useState(!1);const[g,V]=a.useState(null),[q,I]=a.useState(!1),[X,L]=a.useState([]),[S,G]=a.useState(""),[ne,Z]=a.useState(!1),[W,H]=a.useState([]),[T,J]=a.useState(2e3),Q={claude:{totalTokens:15e4,usableTokensForPrompt:112500,MAX_CONTENT_SIZE_LIMIT:84375},gpt_3:{totalTokens:4096,usableTokensForPrompt:3072,MAX_CONTENT_SIZE_LIMIT:2304},gpt_4:{totalTokens:8192,usableTokensForPrompt:6144,MAX_CONTENT_SIZE_LIMIT:4608},gpt_4_extended:{totalTokens:32768,usableTokensForPrompt:24576,MAX_CONTENT_SIZE_LIMIT:18432}},ie=()=>{const n=g.reduce((d,t)=>d+t.wordCount,0)>T;return e.jsx("div",{className:"fixed relative inset-0 z-[100] overflow-y-auto bg-transparent bg-opacity-90 p-4",children:e.jsxs("div",{className:"mx-auto max-w-4xl",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-xl font-bold",children:"Document Preview"}),e.jsxs("p",{children:["Max Words Allowed: ",T," words."]})]}),n&&e.jsxs("div",{className:"mt-4 rounded bg-red-200 p-2 text-red-700",children:[e.jsxs("p",{children:["Content size exceeds the limit of ",T," words."]}),e.jsx("p",{children:"Consider reducing the number of files."})]}),e.jsx("div",{className:"mt-4",children:g.map((d,t)=>e.jsxs("div",{className:"flex items-center justify-between border-b py-2",children:[e.jsx("div",{children:d.name}),e.jsxs("div",{children:[(d.size/1024).toFixed(2)," KB"]}),e.jsxs("div",{children:[d.wordCount," words"]})]},t))})]})})},re=({allow_preview:i})=>{const[n,d]=a.useState(null),t=(l,u)=>{navigator.clipboard.writeText(l),d(u),setTimeout(()=>d(null),2e3)},r=l=>new Promise((u,P)=>{const f=new FileReader;f.onload=async h=>{try{const o=h.target.result,x=m(o),p=x.split(/\s+/).length;console.log(x,"from docx"),u({content:x,wordCount:p})}catch(o){P(o)}},f.onerror=h=>P(h),f.readAsArrayBuffer(l)}),c=l=>new Promise((u,P)=>{const f=new FileReader;f.onload=function(){try{const h=f.result,o=h.split(/\s+/).length;console.log(h,"from docx"),u({content:h,wordCount:o})}catch(h){P(h)}},f.readAsText(l)});function s(l){return l.charCodeAt(0)===65279&&(l=l.substr(1)),new Xe().parseFromString(l,"text/xml")}function m(l){const u=new qe(l),f=s(u.files["word/document.xml"].asText()).getElementsByTagName("w:p"),h=[];for(let o=0,b=f.length;o<b;o++){let x="";const p=f[o].getElementsByTagName("w:t");for(let le=0,Ne=p.length;le<Ne;le++){const pe=p[le];pe.childNodes&&(x+=pe.childNodes[0].nodeValue)}x&&h.push(x)}return h.join(" ")}const K=w.useCallback(async l=>{const u=[];let P="";for(const o of l){let b=0,x="";if(o.type==="application/pdf")try{const p=await Ge(o);b=p.split(/\s+/).length,x=p}catch(p){console.error("Error reading PDF file:",p)}else if(o.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document"){const p=await r(o);x=p.content,b=p.wordCount}else if(o.type==="text/plain"){const p=await c(o);x=p.content,b=p.wordCount,console.log("Word Count:",b,x,"from txt")}console.log(b,"wordCount"),P+=x,u.push({name:o.name,size:o.size,wordCount:b,content:x})}u.reduce((o,b)=>o+b.wordCount,0)>T?(G(""),Z(!1),M(j,"Word Limit Exceeded",5e3,"error")):(G(P),Z(!0)),V(u),I(!0)},[]),{getRootProps:y,getInputProps:Y}=Ve({onDrop:K,accept:{"application/pdf":[".pdf"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"]}});return e.jsxs("div",{className:"mt-5",children:[e.jsxs("label",{className:"mb-2 flex items-center gap-2 text-sm font-bold text-white",children:["Upload Documents",e.jsxs("span",{className:"group relative",children:[e.jsx($e,{className:"h-5 w-5 cursor-pointer text-white/70"}),e.jsx("div",{className:"absolute bottom-full left-0 mb-2 hidden w-64 rounded-md bg-[#2d3947] p-2 text-sm text-white shadow-lg group-hover:block",children:"Documents forming the knowledge base for the AI assistant"})]})]}),e.jsxs("div",{className:"mb-4 flex gap-3 text-sm text-white/70",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(de,{className:"h-4 w-4"}),"Supported formats:"]}),e.jsx("span",{className:"rounded bg-[#2d3947] px-2 py-0.5",children:"PDF"}),e.jsx("span",{className:"rounded bg-[#2d3947] px-2 py-0.5",children:"DOCX"}),e.jsx("span",{className:"rounded bg-[#2d3947] px-2 py-0.5",children:"TXT"})]}),e.jsxs("div",{...y({className:"dropzone"}),children:[e.jsx("input",{...Y()}),e.jsx("div",{className:"flex h-48 w-full flex-col items-center justify-center rounded-lg border-2 border-dashed border-white/30 p-8 transition-colors hover:border-[#19b2f6]/50",children:e.jsxs("div",{className:"flex flex-col items-center text-center text-white",children:[e.jsx(de,{className:"mb-2 h-8 w-8 text-white/70"}),e.jsx("p",{className:"mb-2 text-lg",children:"Drag & drop files here"}),e.jsx("p",{className:"text-sm text-white/70",children:"or click to select files"})]})})]}),g&&g.length>0&&e.jsxs("div",{className:"mt-4 rounded-lg bg-[#2d3947] p-4",children:[e.jsxs("h3",{className:"mb-3 text-sm font-semibold text-white",children:["Uploaded Files (",g.length,")"]}),e.jsx("div",{className:"space-y-2",children:g.map((l,u)=>e.jsxs("div",{className:"flex items-center justify-between rounded bg-[#1d2937] p-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(de,{className:"h-4 w-4 text-white/70"}),e.jsx("span",{className:"text-sm text-white",children:l.name})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("span",{className:"text-sm text-white/70",children:[(l.size/1024).toFixed(2)," KB"]}),e.jsxs("span",{className:"text-sm text-white/70",children:[l.wordCount," words"]}),e.jsx("button",{onClick:()=>t(l.name,u),className:"rounded p-1 transition-colors hover:bg-[#2d3947]",title:"Copy filename",children:n===u?e.jsx(Ke,{className:"h-4 w-4 text-green-400"}):e.jsx(Ze,{className:"h-4 w-4 text-white/70 hover:text-white"})})]})]},u))}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx("button",{type:"button",className:"rounded-md bg-[#19b2f6]/80 px-4 py-2 text-sm text-white transition-colors hover:bg-[#19b2f6]",onClick:()=>I(!0),children:"Preview Documents"})})]}),e.jsxs("div",{className:"mt-2 text-sm text-white/70",children:["Maximum ",T.toLocaleString()," words allowed"]})]})},{register:F,handleSubmit:k,setError:$,formState:{errors:z,isSubmitting:A}}=ve({resolver:Re(v),defaultValues:{name:"",from_number_id:"",assistant_id:"",language:""}}),D=async i=>{let n=new ue;try{(await n.callRawAPI("/v3/api/custom/voiceoutreach/user/inbound_campaign/create",{name:i.name,assistant_id:i.assistant_id,from_number_id:i.from_number_id,knowledgeField:S,language:i.language},"POST")).error||(M(j,"Added"),C&&C())}catch(d){console.log("Error",d),$("name",{type:"manual",message:d.message})}};return a.useEffect(()=>{j({type:"SETPATH",payload:{path:"inbound_campaigns"}}),async function(){R.setTable("assistants");const n=await R.callRestAPI({user_id:B.user,filter:[`user_id,eq,${B.user}`]},"GETALL");n.error||ae(n.list)}(),async function(){R.setTable("numbers");const n=await R.callRestAPI({user_id:B.user,filter:["status,eq,1"]},"GETALL");n.error||L(n.list)}(),async function(){var d,t,r,c;R.setTable("setting");const n=await R.callRestAPI({},"GETALL");if(!n.error){H(n.list);const s=n.list.filter(m=>m.setting_key==="llm");s.length&&(console.log("setting",[(d=s[0])==null?void 0:d.setting_value]),console.log("setting",Q[(t=s[0])==null?void 0:t.setting_value]),console.log("setting",Q[(r=s[0])==null?void 0:r.setting_value].MAX_CONTENT_SIZE_LIMIT),J(Q[(c=s[0])==null?void 0:c.setting_value].MAX_CONTENT_SIZE_LIMIT))}}()},[]),e.jsxs("div",{className:"",children:[e.jsx(N,{appear:!1,show:U,as:w.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[101]",onClose:()=>{q||C()},children:[e.jsx(N.Child,{as:w.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(N.Child,{as:w.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 translate-x-full",enterTo:"opacity-100 translate-x-0",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-x-0",leaveTo:"opacity-0 translate-x-full",children:e.jsx(_.Panel,{className:"h-[95vh] w-full max-w-3xl transform overflow-y-auto bg-[#1d2937]  p-6 text-left align-middle shadow-xl transition-all",children:e.jsxs("div",{className:"p-5",children:[e.jsx("h4",{className:"text-3xl font-medium text-white",children:"Add New Inbound Campaign"}),e.jsxs("form",{className:"mt-7 flex w-full flex-col gap-2",onSubmit:k(D),children:[e.jsx(ee,{type:"text",page:"add",name:"name",errors:z,label:"Campaign Name",placeholder:"Name of the campaign",register:F,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsx(ee,{type:"mapping",page:"add",name:"assistant_id",errors:z,label:"Voice Assistant",placeholder:"Select an Assistant",options:E.map(i=>i.id),mapping:E.reduce((i,n)=>(i[n.id]=n.assistant_name,i),{}),register:F,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsx(ee,{type:"mapping",page:"add",name:"from_number_id",errors:z,label:"Inbound Receiving Phone #",placeholder:"Choose Phone Number",options:X.map(i=>i.id),mapping:X.reduce((i,n)=>(i[n.id]=n.number,i),{}),register:F,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsx(ee,{type:"mapping",page:"add",name:"language",errors:z,label:"Language",placeholder:"Select Language",options:Object.keys(xe),mapping:xe,register:F,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsx(re,{allow_preview:ne}),e.jsx(Be,{type:"submit",loading:A,disabled:A,className:"focus:shadow-outline mt-6 rounded bg-[#19b2f6]/80  px-4 py-2 font-semibold text-white focus:outline-none",children:"Submit"})]})]})})})})})]})}),e.jsx(N,{appear:!1,show:q,as:w.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[102]",onClose:()=>I(!1),children:[e.jsx(N.Child,{as:w.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(N.Child,{as:w.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"mx-auto  h-[95vh] w-full max-w-4xl transform overflow-y-auto rounded-xl bg-[#1d2937] p-6 text-left align-middle text-white shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{className:"text-3xl font-medium",children:"Preview Document"}),e.jsx("button",{type:"button",onClick:()=>I(!1),children:e.jsx(je,{className:"h-6 w-6"})})]}),e.jsx(ie,{})]})})})})]})})]})};let se=new ue;const ye={0:"Inactive",1:"Active",2:"Paused"},he={inactive:0,active:1,paused:2,INACTIVE:0,ACTIVE:1,PAUSED:2,Inactive:0,Active:1,Paused:2},me=[{header:"Action",accessor:""},{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Assistant name",accessor:"assistant_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Contacts",accessor:"called",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:ye}],ge=me.filter(U=>!["","assistant_name","called"].includes(U.accessor)),fe={INACTIVE:0,ACTIVE:1,PAUSED:2},Rt=()=>{const{state:U,dispatch:C}=a.useContext(we),{dispatch:j}=a.useContext(be),[B,O]=a.useState(!1),[v,E]=a.useState([]),[ae,g]=a.useState([]),[V,q]=a.useState(""),[I,X]=a.useState(!1),[L,S]=a.useState(!1),[G,ne]=a.useState([]),[Z,W]=a.useState(!1),[H,T]=a.useState(null),[J,Q]=Ce(),[ie,re]=a.useState({currentPage:0,pageSize:0,totalNumber:0,totalPages:0});ve({defaultValues:{}});const[F,k]=a.useState({}),[$,z]=a.useState("eq");a.useEffect(()=>{j({type:"SETPATH",payload:{path:"inbound_campaigns"}}),D()},[ae,V]);const A=(t,r,c)=>{if(!c){g(y=>y.filter(Y=>!Y.startsWith(t+",")));return}let s=c,m=r||"eq";if(t==="status"){const y=c.toLowerCase();he.hasOwnProperty(y)&&(s=he[y])}else m==="eq"?s=c:s=c.toLowerCase();const K=`${t},${m},${s}`;g(y=>[...y.filter(l=>!l.startsWith(t+",")),K])};async function D(){try{S(!0);const t=new Te,r=await t.getPaginate("campaign",{size:J.get("limit")??50,page:J.get("page")??1,filter:["campaign_type,eq,2",`${t.getProjectId()}_campaign.user_id,eq,${U.user}`],join:"assistants|assistant_id"}),{list:c,total:s,limit:m,num_pages:K,page:y}=r;ne(c),re({currentPage:y,pageSize:m,totalNumber:s,totalPages:K})}catch(t){console.log("ERROR",t),oe(C,t.message),M(j,t.message,5e3,"error")}S(!1)}async function i(t,r){S(!0);try{se.setTable("campaign"),await se.callRestAPI({status:r,id:t},"PUT"),M(j,"Updated"),D()}catch(c){oe(C,c.message),M(j,c.message,5e3,"error")}S(!1)}async function n(t){S(!0);try{se.setTable("campaign"),await se.callRestAPI({id:t},"DELETE"),M(j,"Deleted"),D()}catch(r){oe(C,r.message),M(j,r.message,5e3,"error")}S(!1)}const d=()=>{E([]),g([]),k({}),D()};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5",children:[e.jsx("h3",{className:"my-4 px-8 text-2xl font-bold text-white",children:"Inbound Campaigns"}),e.jsxs("div",{className:"bg-[#1d2937] px-8 py-4",children:[e.jsxs("div",{className:"flex h-fit items-center justify-between gap-3",children:[e.jsx("div",{className:"flex w-[200px] min-w-[200px] items-center justify-between",children:e.jsx("div",{className:"relative z-10 rounded bg-[#1d2937]",children:e.jsxs(te,{children:[e.jsxs("div",{className:"flex items-center gap-4 bg-[#1d2937] text-white",children:[e.jsxs(te.Button,{className:"flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white focus-visible:outline-0 focus-visible:outline-transparent",children:[e.jsx(Ae,{}),e.jsx("span",{children:"Filters"}),v.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:v.length})]}),e.jsxs("div",{className:"focus-within:border-gray-40 flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white",children:[e.jsx(Pe,{className:"text-xl text-white"}),e.jsx("input",{type:"text",placeholder:"search by name",className:"border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none",style:{boxShadow:"0 0 transparent"},value:V,onChange:t=>{q(t.target.value),A("name","cs",t.target.value)}}),V&&e.jsx(_e,{className:"cursor-pointer text-lg text-white",onClick:()=>{q(""),A("name","cs","")}})]})]}),e.jsx(N,{as:w.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(te.Panel,{children:e.jsxs("div",{className:"filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10",children:[e.jsx("span",{className:"absolute left-5 top-2 font-medium text-white",children:"Filters"}),e.jsx(te.Button,{onClick:()=>{console.log("clicked"),E([]),g([]),k({})},children:e.jsx(We,{className:"absolute right-2 top-2 cursor-pointer text-white"})}),v==null?void 0:v.map((t,r)=>{var c;return e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("button",{type:"button",className:"block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none",title:t,style:{WebkitTouchCallout:"none"},children:((c=ge.find(s=>s.accessor===t))==null?void 0:c.header)||t}),e.jsxs("select",{className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none",value:$,onChange:s=>{z(s.target.value),A(t,s.target.value,F[t])},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),t==="status"?e.jsxs("select",{value:F[t]||"",onChange:s=>{k(m=>({...m,[t]:s.target.value})),A(t,$,s.target.value)},className:"h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",children:[e.jsx("option",{value:"",children:"Select Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]}):e.jsx(Ee,{type:"text",labelClassName:"!mb-0",placeholder:"Enter value",setValue:()=>{},showIcon:!1,className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",onReady:s=>A(t,$,s)}),e.jsx(Ie,{className:"cursor-pointer text-2xl text-red-600",onClick:()=>{E(s=>s.filter(m=>m!==t)),g(s=>s.filter(m=>!m.includes(t))),k(s=>{const m={...s};return delete m[t],m})}})]},r)}),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>X(!I),children:[e.jsx(Fe,{}),"Add filter"]}),I&&e.jsx("div",{className:"absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:ge.map(t=>e.jsx("li",{className:`${v.includes(t.accessor)?"cursor-not-allowed text-gray-100":"cursor-pointer text-gray-400 hover:text-white"}`,onClick:()=>{v.includes(t.accessor)||(E(r=>[...r,t.accessor]),k(r=>({...r,[t.accessor]:""}))),X(!1)},children:t.header},t.accessor))})}),v.length>0&&e.jsx("div",{onClick:d,className:"inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white",children:"Clear all filter"})]})]})})})]})})}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsx(Le,{onClick:()=>{O(!0)},showChildren:!0,className:"relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]",children:"Add New"})})]}),e.jsx("div",{className:"mt-4 overflow-x-auto  bg-[#1d2937]",children:e.jsx("div",{className:L?"":"overflow-x-auto border-b border-gray-200 shadow",children:L&&G.length===0?e.jsx(ke,{columns:me}):e.jsxs("table",{className:"min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]",children:[e.jsx("thead",{className:"bg-[#1d2937]",children:e.jsx(De,{actionPosition:"onTable",onSort:()=>{},columns:me,actions:{view:{show:!0}}})}),e.jsx("tbody",{className:"divide-y divide-gray-400 bg-[#1d2937]",children:G.map(t=>{var r;return e.jsxs("tr",{className:"text-white",children:[e.jsx("td",{className:"pl-3",children:e.jsxs("div",{className:"flex max-w-[260px] items-center justify-between gap-3 text-sm",children:[t.status===1?e.jsx("button",{title:"Pause Campaign",className:"rounded-[30px] bg-orange-500/20 p-1.5 px-5 font-medium text-orange-500 transition-colors hover:bg-orange-500/30",disabled:L,onClick:()=>i(t.id,fe.PAUSED),children:"Pause"}):null,t.status!==1?e.jsx("button",{title:"Start Campaign",className:"rounded-[30px] bg-green-500/20 p-1.5 px-5 font-medium text-green-500 transition-colors hover:bg-green-500/30",disabled:L,onClick:()=>i(t.id,fe.ACTIVE),children:"Start"}):null,e.jsx("button",{title:"Delete Campaign",className:"rounded-[30px] bg-red-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-red-500/30",onClick:()=>{W(!0),T(t.id)},children:"Delete"}),e.jsx(Se,{to:`/user/inbound_call_logs?campaign_id=${t.id}`,children:e.jsx("button",{title:"View Call Logs",className:"rounded-[30px] bg-blue-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-blue-500/30",type:"button",children:"View"})})]})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.id}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.name}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:((r=t.assistants)==null?void 0:r.assistant_name)??"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.contacts??"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:ye[t.status]??"N/A"})]},t.id)})})]})})}),e.jsx(Me,{paginationData:ie})]})]}),e.jsx(N,{appear:!0,show:B,as:w.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[100]",onClose:()=>O(!1),children:[e.jsx(N.Child,{as:w.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4",children:e.jsx(N.Child,{as:w.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-3xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(_.Title,{className:"text-xl font-medium text-white",children:"Add New Campaign"}),e.jsx("button",{onClick:()=>O(!1),children:e.jsx(je,{className:"h-6 w-6 text-white/70 hover:text-white"})})]}),e.jsx(He,{closeSidebar:()=>{O(!1),D()}})]})})})})]})}),e.jsx(ze,{isOpen:Z&&!!H,closeModal:()=>W(!1),onDelete:async()=>{await n(H),T(null),W(!1)}})]})};export{Rt as default};
