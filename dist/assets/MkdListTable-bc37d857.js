import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{r as g,R as m,f as S}from"./vendor-2ae44a2e.js";import{m as N}from"./index-b2ff2fa1.js";import k from"./MkdListTableRow-302dd611.js";import T from"./MkdListTableHead-ceaedfb7.js";import{_ as u}from"./qr-scanner-cf010ec4.js";import{C as z}from"./CustomDeleteModal-536cae45.js";import"./react-confirm-alert-783bc3ae.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";g.lazy(()=>u(()=>import("./Modal-f976b228.js"),["assets/Modal-f976b228.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/index.esm-42944128.js","assets/react-icons-f29df01f.js"]).then(_=>({default:_.Modal})));g.lazy(()=>u(()=>import("./ModalPrompt-fb38cd0a.js"),["assets/ModalPrompt-fb38cd0a.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/index-f2c2b086.js","assets/qr-scanner-cf010ec4.js","assets/InteractiveButton-bff38983.js","assets/MoonLoader-62b0139a.js"]));g.lazy(()=>u(()=>import("./ModalAlert-9f46520e.js"),["assets/ModalAlert-9f46520e.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js"]));const ae=({table:_,tableTitle:P,onSort:I,loading:h,columns:p,actions:r,actionPosition:b,tableRole:V,deleteItem:C,deleteLoading:F,actionId:x="id",showDeleteModal:E,currentTableData:d,setShowDeleteModal:R,handleTableCellChange:O,setSelectedItems:i})=>{const[j,A]=m.useState(null),[D,f]=m.useState(!1),[w,n]=m.useState(!1),[s,a]=m.useState([]);function L(e){var y;f(!0);const o=s;if((y=r==null?void 0:r.select)!=null&&y.multiple)if(o.includes(e)){const l=o.filter(c=>c!==e);a(()=>[...l]),i(l)}else{const l=[...o,e];a(()=>[...l]),i(l)}else if(o.includes(e)){const l=o.filter(c=>c!==e);a(()=>[...l]),i(l)}else{const l=[e];a(()=>[...l]),i(l)}console.log(e)}const M=()=>{if(n(e=>!e),w)a([]),i([]);else{const e=d.map(o=>o[x]);a(e),i(e)}};S();const v=async e=>{console.log("id >>",e),R(!0),A(e)};return m.useEffect(()=>{s.length<=0&&(f(!1),n(!1)),s.length===d.length&&(n(!0),f(!0)),s.length<d.length&&s.length>0&&n(!1)},[s,d]),t.jsxs(t.Fragment,{children:[t.jsx("div",{className:`${h?"":"overflow-x-auto"} border-b border-gray-400 bg-[#1d2937] shadow`,children:h&&d.length==0?t.jsx(N,{columns:p}):t.jsx(t.Fragment,{children:t.jsxs("table",{className:"min-w-full divide-y  divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937] bg-[#1d2937]",children:[t.jsx("thead",{className:"bg-[#1d2937]",children:t.jsx(T,{onSort:I,columns:p,actions:r,actionPosition:b,areAllRowsSelected:w,handleSelectAll:M})}),t.jsx("tbody",{className:"divide-y divide-gray-200 bg-[#1d2937]",children:d.map((e,o)=>t.jsx(k,{i:o,row:e,columns:p,actions:r,actionPosition:b,actionId:x,handleTableCellChange:O,handleSelectRow:L,selectedIds:s,setDeleteId:v},o))})]})})}),t.jsx(z,{isOpen:E&&!!j,closeModal:()=>R(!1),onDelete:()=>{r.delete.action(j),v(null)}})]})};export{ae as default};
