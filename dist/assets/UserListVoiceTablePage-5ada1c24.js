import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as a,f as b,u as y,r as i}from"./vendor-2ae44a2e.js";import{A as E,G as j,h as S,i as T,M as C,s as m,t as F}from"./index-b2ff2fa1.js";import{q as c,_ as f}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const D={id:{operations:["eq","lt","le","gt","gte"],type:"number",label:"ID"},create_at:{operations:["eq","lt","le","gt","gte"],type:"string",label:"Created At"},name:{operations:["eq","cs","sw","ew"],type:"string",label:"Name"}},N=[{header:"Action",accessor:""},{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},isSearchable:!0},{header:"Files",accessor:"files",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},format:o=>o.split(",").join("<br /><br />")}],O=()=>{const{state:o,dispatch:d}=a.useContext(E),{dispatch:r}=a.useContext(j),u=b(),{pathname:h}=y(),[x,l]=a.useState(!1),[g,v]=a.useState(),n=i.useRef(null);a.useEffect(()=>{r({type:"SETPATH",payload:{path:"voice_list"}})},[]);async function w(t){var p;try{const s=new C;s.setTable("voice_list"),await s.callRestAPI({id:t},"DELETE"),(p=n.current)==null||p.click(),m(r,"Delete successful")}catch(s){F(d,s.message),m(r,s.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto rounded-lg  bg-[#1d2937] pt-5 shadow md:p-5",children:e.jsx(S,{columns:N,tableRole:"user",table:"voice_list",actionId:"id",defaultFilter:[`user_id,eq,${o.user}`],actions:{view:{show:!1,action:null,multiple:!1},edit:{show:!1,multiple:!1,action:t=>{v(t[0]),l(!0)}},delete:{show:!1,action:w,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>u(`/user/onboarding?redirect_uri=${h}`),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:"onTable",refreshRef:n,filterConfig:D})})}),e.jsx(c,{appear:!0,show:x,as:i.Fragment,children:e.jsxs(f,{as:"div",className:"relative z-[100]",onClose:()=>l(!1),children:[e.jsx(c.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-transparent/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(c.Child,{as:i.Fragment,enter:"duration-300",enterFrom:"opacity-0 translate-x-0",enterTo:"opacity-100 translate-x-0",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-x-0",leaveTo:"opacity-0 translate-x-full",children:e.jsx(f.Panel,{className:"h-fit w-full max-w-3xl transform overflow-y-auto bg-[#1d2937]  p-6 text-left align-middle shadow-xl transition-all",children:e.jsx(T,{activeId:g,closeSidebar:()=>{var t;l(!1),(t=n.current)==null||t.click()}})})})})})]})})]})};export{O as default};
