import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as a}from"./vendor-2ae44a2e.js";import{X as l}from"./index-b2ff2fa1.js";import{q as s,_ as r}from"./@headlessui/react-7bce1936.js";function x({isOpen:i,closeModal:t,onDelete:n}){return e.jsx(s,{appear:!0,show:i,as:a.Fragment,children:e.jsxs(r,{as:"div",className:"relative z-[100]",onClose:t,children:[e.jsx(s.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(s.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(r.Panel,{className:"w-full max-w-lg transform overflow-hidden rounded-md bg-[#1d2937] p-6  text-left align-middle text-white shadow-xl transition-all",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(r.Title,{as:"h3",className:"text-xl font-medium leading-6 text-white",children:"Are you sure?"}),e.jsx("button",{onClick:t,type:"button",className:"text-gray-100 duration-100 hover:text-gray-300",children:e.jsx(l,{className:"w-6 h-6"})})]}),e.jsx("p",{className:"mt-2 text-gray-100",children:"Are you sure you want to delete"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-6 font-semibold",children:[e.jsx("button",{className:"rounded-md border bg-[#1d2937] py-2 text-center",type:"button",onClick:t,children:"Cancel"}),e.jsx("button",{onClick:()=>{n(),t()},className:"py-2 text-center text-white bg-red-600 rounded-md",children:"Delete"})]})]})})})})]})})}export{x as C};
