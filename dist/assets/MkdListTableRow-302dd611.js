import{j as r}from"./@react-google-maps/api-c55ecefa.js";import"./vendor-2ae44a2e.js";function b(h){const s=new Date(h),p={year:"numeric",month:"long",day:"numeric"};return s.toLocaleDateString("en-US",p)}function v(h){const p=h.toString().replace(/\D/g,"").match(/^(\d{3})(\d{4})(\d{4})$/);return p?`(${p[1]}) ${p[2]}-${p[3]}`:null}const k=({i:h,row:s,columns:p,actions:e,actionPosition:o,actionId:d="id",handleTableCellChange:j,selectedIds:g=[],handleSelectRow:y,setDeleteId:N})=>r.jsx(r.Fragment,{children:r.jsx("tr",{children:p.map((t,a)=>{var n,i,m,c,u,f,w,l;return t.format?r.jsx("td",{className:"px-6 py-4 text-left text-white whitespace-nowrap",dangerouslySetInnerHTML:{__html:t.format(s[t.accessor])}},a):t.accessor.indexOf("image")>-1?r.jsx("td",{className:"px-6 py-4 text-left text-white whitespace-nowrap",children:r.jsx("img",{src:s[t.accessor],className:"h-[3.rem] w-[9.375rem]",alt:""})},a):t.accessor.indexOf("pdf")>-1||t.accessor.indexOf("doc")>-1||t.accessor.indexOf("video")>-1?r.jsx("td",{className:"px-6 py-4 text-left text-white whitespace-nowrap",children:r.jsxs("a",{className:"text-white",target:"_blank",href:s[t.accessor],rel:"noreferrer",children:[" ","View"]})},a):t.accessor==="create_at"?r.jsx("td",{className:"px-6 py-4 text-left text-white whitespace-nowrap",children:b(s[t.accessor])},a):t.accessor==="number"?r.jsx("td",{className:"px-6 py-4 text-left text-white whitespace-nowrap",children:v(s[t.accessor])},a):t.accessor===""?[(n=e==null?void 0:e.select)==null?void 0:n.show,(i=e==null?void 0:e.view)==null?void 0:i.show,(m=e==null?void 0:e.edit)==null?void 0:m.show,(c=e==null?void 0:e.delete)==null?void 0:c.show].includes(!0)?r.jsx("td",{className:"flex !w-full whitespace-nowrap px-6 py-6 text-left text-white",children:r.jsxs("div",{className:"items-center space-x-3 text-sm",children:[((u=e==null?void 0:e.select)==null?void 0:u.show)&&r.jsx("span",{children:r.jsx("input",{className:"mr-1",type:"checkbox",name:"select_item",checked:g.includes(s[d]),onChange:()=>y(s[d])})}),o==="onTable"&&r.jsxs(r.Fragment,{children:[((f=e==null?void 0:e.edit)==null?void 0:f.show)&&r.jsx("button",{title:"Edit Entry",className:"rounded-[30px] bg-green-400/20 p-1.5 px-5 font-medium text-white ",onClick:()=>{var x;(x=e==null?void 0:e.edit)!=null&&x.action&&e.edit.action([s[d]])},children:r.jsx("span",{children:"Edit"})}),((w=e==null?void 0:e.view)==null?void 0:w.show)&&r.jsx("button",{title:"View Entry",className:"rounded-[30px] bg-blue-500/20 p-1.5 px-5 font-medium text-white ",onClick:()=>{var x;(x=e==null?void 0:e.view)!=null&&x.action&&e.view.action([s[d]])},children:r.jsx("span",{children:"View"})}),((l=e==null?void 0:e.delete)==null?void 0:l.show)&&r.jsx("button",{title:"Delete Entry",className:"rounded-[30px] bg-red-500/20 p-1.5 px-5 font-medium text-white ",onClick:()=>{window.confirm("Are you sure you want to delete this entry?")&&N(s[d])},children:r.jsx("span",{children:"Delete"})})]})]})},a):null:t.mappingExist?r.jsx("td",{className:"px-6 py-4 text-left text-white whitespace-nowrap",children:t.mappings[s[t.accessor]]},a):!t.mappingExist&&t.accessor!=="id"&&t.accessor!=="create_at"&&t.accessor!=="update_at"&&t.accessor!=="user_id"?r.jsx("td",{title:s[t.accessor].length>40?s[t.accessor]:"",className:"max-w-[200px] overflow-x-hidden truncate whitespace-nowrap px-6 py-4 text-white",children:t.editable?r.jsx("input",{placeholder:t.accessor,className:"bg-transparent text-ellipsis",type:"text",value:s[t.accessor],onChange:x=>j(s[d],x.target.value,h,t.accessor)}):s[t.accessor]},a):r.jsx("td",{title:s[t.accessor].length>40?s[t.accessor]:"",className:"max-w-[200px] overflow-x-hidden truncate whitespace-nowrap px-6 py-4 text-white",children:s[t.accessor]??"N/A"},a)})})});export{k as default};
