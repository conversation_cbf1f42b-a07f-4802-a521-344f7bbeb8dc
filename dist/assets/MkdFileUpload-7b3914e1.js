import{j as n}from"./@react-google-maps/api-c55ecefa.js";import{r as l,R}from"./vendor-2ae44a2e.js";import"./papaparse-2d1475f9.js";const L=c=>{switch(c){case"excel":case"xls":case"xlsx":case".xlsx":case".xls":case".xlsx,.xls":case"xlsx,xls":case".xls,.xlsx":case"xls,xlsx":return".xlsx,.xls";case"csv":case".csv":return".csv";case".png":case"png":return".png";case".jpg":case"jpg":return".jpg";case".jpeg":case"jpeg":return".jpeg";case"image":case".png,.jpg,.jpeg":case".png,.jpeg,.jpg":case".jpg,.png,.jpeg":case".jpg,.jpeg,.png":case".jpeg,.png,.jpg":case".jpeg,.jpg,.png":return".png,.jpg,.jpeg";case"video/audio":return".mp4,.mp3,.wav";case"audio":return".mp3,.wav";case"doc":case"docx":case"document":case".doc":case".docx":case".docx,.doc":case".doc,.docx":case"docx,doc":case"doc,docx":return".docx,.doc";case"pdf":case".pdf":return".pdf";default:return"*"}},O=c=>{switch(c){case"excel":case"xls":case"xlsx":case".xlsx":case".xls":case".xlsx,.xls":case"xlsx,xls":case".xls,.xlsx":case"xls,xlsx":return"an excel";case"csv":case".csv":return"a csv";case".png":case"png":return"a png";case".jpg":case"jpg":return"a jpg";case".jpeg":case"jpeg":return"a jpeg";case"image":case".png,.jpg,.jpeg":case".png,.jpeg,.jpg":case".jpg,.png,.jpeg":case".jpg,.jpeg,.png":case".jpeg,.png,.jpg":case".jpeg,.jpg,.png":return"an image";case"video/audio":return"video/audio";case"audio":return"audio";case"doc":case"docx":case"document":case".doc":case".docx":case".docx,.doc":case".doc,.docx":case"docx,doc":case"doc,docx":return"a word document";case"pdf":case".pdf":return"a pdf";default:return"any"}},N=({fileType:c,name:o="fileData",multiple:i=!1,onAddSuccess:u,removeWidthStyles:g})=>{const x=l.useId(),t=l.useRef(null),[f,U]=l.useState(!1);l.useState([]);const[j,r]=l.useState(!1),[S,m]=l.useState(null),[v,h]=R.useState({}),D=(e,s,p=!1)=>{let a=v;p?a[e]?a[e]=[...a[e],{file:s.files[0],tempFile:{url:URL.createObjectURL(s.files[0]),name:s.files[0].name,type:s.files[0].type}}]:a[e]=[{file:s.files[0],tempFile:{url:URL.createObjectURL(s.files[0]),name:s.files[0].name,type:s.files[0].type}}]:a[e]={file:s.files[0],tempFile:{url:URL.createObjectURL(s.files[0]),name:s.files[0].name,type:s.files[0].type}},h({...a}),u({...a})},d=(e,s=!1)=>{D(o,s?e.dataTransfer:e.target,i),t.current.value=""},b=e=>{e.preventDefault(),r(!0)},w=e=>{e.preventDefault(),r(!0)},F=()=>{r(!1)},y=e=>{e.preventDefault(),r(!1);const s=e.dataTransfer.files;if(console.log(e.dataTransfer.files),s.length>0){const p=s[0];m(p),d(e,!0)}};return n.jsxs(n.Fragment,{children:[n.jsx("input",{id:x,disabled:f,className:"hidden w-[20%] cursor-pointer rounded bg-[#2cc9d5] p-4 text-white",type:"file",accept:L(c),ref:t,onChange:d}),n.jsx("div",{onDragEnter:b,onDragOver:w,onDragLeave:F,onDrop:y,onClick:()=>t.current.click(),className:`flex min-h-[15.375rem] w-full cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-600 ${g?"":"md:w-[31.25rem]  md:min-w-[41.25rem] md:max-w-[31.25rem]"} ${j?"border-green-500":"border-black"}`,children:n.jsx("div",{className:"flex h-full max-h-full min-h-full w-full min-w-full max-w-full flex-col items-center justify-center py-4",children:n.jsxs("div",{className:"font-bold",children:["Select/Drag and Drop ",O(c)," File."]})})})]})};export{N as default};
