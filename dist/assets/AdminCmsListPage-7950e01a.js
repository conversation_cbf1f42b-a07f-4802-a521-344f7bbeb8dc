import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as r,f as re}from"./vendor-2ae44a2e.js";import{u as ie}from"./react-hook-form-47c010f8.js";import{o as ne}from"./yup-5abd4662.js";import{c as oe,a as S}from"./yup-5c93ed04.js";import{M as le,A as ce,G as de,B as pe,a as ue,b as me,R as xe,c as he,t as ge,g as w}from"./index-b2ff2fa1.js";import{M as D}from"./index-9aa09a5c.js";import fe from"./EditAdminCmsPage-b9f8d8c4.js";import ye from"./AddAdminCmsPage-67ebe86e.js";import{A as je}from"./index-e429b426.js";import be from"./Skeleton-37d5201c.js";import{P as Se}from"./index-132fbad2.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let F=new le;const n=[{header:"Page",accessor:"page",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Identifier",accessor:"content_key",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Content Type",accessor:"content_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],st=()=>{const{dispatch:R}=r.useContext(ce),{dispatch:M}=r.useContext(de);r.useState("");const[p,T]=r.useState([]),[c,v]=r.useState(10),[N,O]=r.useState(0),[we,$]=r.useState(0),[d,q]=r.useState(0),[B,z]=r.useState(!1),[L,_]=r.useState(!1),[x,h]=r.useState(!1),[I,g]=r.useState(!1),[G,ve]=r.useState(!1),[C,H]=r.useState(!1),[P,k]=r.useState(!1),[o,f]=r.useState([]),[K,y]=r.useState([]),[Ne,U]=r.useState(""),[V,J]=r.useState("eq"),[Q,W]=r.useState();re();const X=oe({page:S(),key:S(),type:S()}),{register:Ce,handleSubmit:Y,setError:Pe,formState:{errors:ke}}=ie({resolver:ne(X)});function Z(t){console.log(n[t]),n[t].isSorted?n[t].isSortedDesc=!n[t].isSortedDesc:(n.map(s=>s.isSorted=!1),n.map(s=>s.isSortedDesc=!1),n[t].isSorted=!0),async function(){await l(0,c)}()}function ee(t){(async function(){v(t),await l(0,t)})()}function te(){(async function(){await l(d-1>0?d-1:0,c)})()}function se(){(async function(){await l(d+1<=N?d+1:0,c)})()}const A=(t,s,a)=>{const i=s==="eq"&&isNaN(a)?`"${a}"`:a,u=`${t},${s},${i}`;y(j=>[...j.filter(m=>!m.includes(t)),u]),U(a)};async function l(t,s,a){try{F.setTable("cms");const i=await F.callRestAPI({payload:{...a},page:t,limit:s},"PAGINATE"),{list:u,total:j,limit:E,num_pages:m,page:b}=i;T(u),v(E),O(m),q(b),$(j),z(b>1),_(b+1<=m)}catch(i){console.log("ERROR",i),ge(R,i.message)}}const ae=t=>{let s=w(t.page),a=w(t.key),i=w(t.type);l(0,10,{page:s,content_key:a,content_type:i})};return r.useEffect(()=>{M({type:"SETPATH",payload:{path:"cms"}}),async function(){await l(1,c)}()},[]),r.useEffect(()=>{x||l(1,c,K)},[x]),e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"flex items-center justify-between py-3 text-black",children:[e.jsxs("form",{className:"relative rounded bg-white",onSubmit:Y(ae),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>H(!C),children:[e.jsx(pe,{}),e.jsx("span",{children:"Filters"}),o.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:o.length>0?o.length:null})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1  focus-within:border-gray-400",children:[e.jsx(ue,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search",className:"border-none p-0 placeholder:text-left focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var s;return A("name","cs",(s=t.target)==null?void 0:s.value)}}),e.jsx(me,{className:"text-lg text-gray-200"})]})]}),C&&e.jsxs("div",{className:"absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-6 shadow-lg",children:[o==null?void 0:o.map((t,s)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:"w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0",onChange:a=>{J(a.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",onChange:a=>A(t,V,a.target.value)}),e.jsx(xe,{className:"cursor-pointer text-2xl",onClick:()=>{f(a=>a.filter(i=>i!==t)),y(a=>a.filter(i=>!i.includes(t)))}})]},s)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex h-[40px] w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>{k(!P)},children:[e.jsx(he,{}),"Add filter"]}),P&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:n.slice(0,-1).map(t=>e.jsx("li",{className:`${o.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{o.includes(t.header)||f(s=>[...s,t.header]),k(!1)},children:t.header},t.header))})}),o.length>0&&e.jsx("div",{onClick:()=>{f([]),y([])},className:"inline-block cursor-pointer  rounded py-2.5  pl-6 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})]}),e.jsx(je,{onClick:()=>h(!0)})]}),G?e.jsx(be,{}):e.jsx("div",{children:e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 text-black",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:n.map((t,s)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",onClick:()=>Z(s),children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},s))})}),e.jsxs("tbody",{className:"divide-y divide-gray-200 bg-white",children:[(p==null?void 0:p.length)===0&&e.jsx("div",{className:"w-full py-3 text-center",children:"No data"}),p.map((t,s)=>e.jsx("tr",{children:n.map((a,i)=>a.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"cursor-pointer text-xs text-[black]",onClick:()=>{W(t==null?void 0:t.id),g(!0)},children:[" ","Edit"]})},i):a.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.mapping[t[a.accessor]]},i):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[a.accessor]},i))},s))]})]})})}),e.jsx(Se,{currentPage:d,pageCount:N,pageSize:c,canPreviousPage:B,canNextPage:L,updatePageSize:ee,previousPage:te,nextPage:se}),e.jsx(D,{isModalActive:x,closeModalFn:()=>h(!1),children:e.jsx(ye,{setSidebar:h})}),e.jsx(D,{isModalActive:I,closeModalFn:()=>g(!1),children:e.jsx(fe,{activeId:Q,setSidebar:g})})]})};export{st as default};
