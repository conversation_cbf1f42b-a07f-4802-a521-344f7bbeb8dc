import{j as s}from"./@react-google-maps/api-c55ecefa.js";import"./vendor-2ae44a2e.js";import{C as x}from"./index-f2c2b086.js";import{I as p}from"./InteractiveButton-bff38983.js";import"./qr-scanner-cf010ec4.js";import"./MoonLoader-62b0139a.js";const y=({closeModalFunction:i,actionHandler:u,message:n,title:o,messageClasses:b="font-normal text-base",titleClasses:h="text-center font-bold text-lg",acceptText:e="YES",rejectText:l="NO",loading:r=!1,isInfo:d=!1})=>{var a,m,t,f;return s.jsx("aside",{className:"fixed bottom-0 left-0 right-0 top-0 z-50 flex items-center justify-center bg-black/50",children:s.jsxs("section",{className:"flex w-auto min-w-[27rem]  flex-col gap-3 rounded-[.5rem] bg-white py-6",children:[s.jsxs("div",{className:"flex justify-between px-6",children:[s.jsx("div",{children:o?s.jsx("div",{className:` ${h} `,children:o}):null}),s.jsx("button",{disabled:r,onClick:i,children:s.jsx(x,{className:"w-4 h-4"})})]}),n?s.jsxs("div",{children:[s.jsx("div",{className:`px-6 text-[#525252] ${b} `,children:n}),!d&&s.jsx("div",{className:"px-6 text-[#525252] font-normal pt-3 pb-1",children:"This action cannot be undone."})]}):null,s.jsxs("div",{className:"flex justify-between px-6 font-medium leading-[1.5rem] text-[base]",children:[s.jsx("button",{disabled:r,className:"flex h-[2.75rem] items-center justify-center rounded-[.5rem] border border-[#d8dae5] text-[#667085] w-full mr-2",onClick:i,children:((a=l==null?void 0:l.charAt(0))==null?void 0:a.toUpperCase())+((m=l==null?void 0:l.slice(1))==null?void 0:m.toLowerCase())}),s.jsxs(p,{disabled:r,loading:r,className:`flex items-center justify-center gap-x-5 rounded-[.5rem]  ${d?"bg-primaryBlue":"bg-[#E11D48]"} px-6 text-white w-full ml-2`,onClick:u,children:["Yes, ",((t=e==null?void 0:e.charAt(0))==null?void 0:t.toUpperCase())+((f=e==null?void 0:e.slice(1))==null?void 0:f.toLowerCase())]})]})]})})};export{y as default};
