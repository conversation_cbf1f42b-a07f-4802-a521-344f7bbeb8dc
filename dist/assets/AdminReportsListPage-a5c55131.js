import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as s,f as W}from"./vendor-2ae44a2e.js";import{M as Y,G as Z,A as ee,B as te,a as se,b as ae,R as re,c as ie,t as oe}from"./index-b2ff2fa1.js";import{o as ne}from"./yup-5abd4662.js";import{u as le}from"./react-hook-form-47c010f8.js";import{c as ce,a as y}from"./yup-5c93ed04.js";import{P as de}from"./index-132fbad2.js";import"./index-e429b426.js";import{S as xe}from"./index-a74110af.js";import"./index-9aa09a5c.js";import{X as pe}from"./lucide-react-1246a7ed.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";let k=new Y;const A=[{header:"First Name",accessor:"first_name"},{header:"Email",accessor:"email"},{header:"Bugs",accessor:"query"}],Ge=()=>{const{dispatch:q}=s.useContext(Z),{dispatch:R}=s.useContext(ee);s.useState("");const[f,E]=s.useState([]),[n,j]=s.useState(10),[b,T]=s.useState(0),[ue,D]=s.useState(0),[l,O]=s.useState(0),[$,B]=s.useState(!1),[z,_]=s.useState(!1),[v,I]=s.useState(!1),[w,N]=s.useState(!1),[o,d]=s.useState([]),[L,x]=s.useState([]),[G,M]=s.useState(""),[S,H]=s.useState("eq"),[C,u]=s.useState(!0);s.useState(!1),s.useState(!1),s.useState(),W();const J=ce({first_name:y(),email:y(),query:y()}),{register:me,handleSubmit:K,formState:{errors:he}}=le({resolver:ne(J)});function V(t){(async function(){j(t),await c(0,t)})()}function X(){(async function(){await c(l-1>0?l-1:0,n)})()}function Q(){(async function(){await c(l+1<=b?l+1:0,n)})()}const P=(t,a,r)=>{const i=a==="eq"&&isNaN(r)?`"${r}"`:r,m=`${t},${a},${i}`;x(h=>[...h.filter(p=>!p.includes(t)),m]),M(r)};async function c(t,a,r){u(!0);try{k.setTable("reports");const i=await k.callJoinRestAPI("reports","user","user_id","id","*",[],"PAGINATE",t,a);i&&u(!1);const{list:m,total:h,limit:F,num_pages:p,page:g}=i;E(m),j(F),T(p),O(g),D(h),B(g>1),_(g+1<=p)}catch(i){u(!1),console.log("ERROR",i),oe(R,i.message)}}const U=t=>{c(0,n)};return s.useEffect(()=>{q({type:"SETPATH",payload:{path:"bugs"}});const a=setTimeout(async()=>{await c(1,n)},700);return()=>{clearTimeout(a)}},[G,L,S]),e.jsxs("div",{className:"min-h-screen bg-white",children:[e.jsx("div",{className:"flex items-center justify-between px-8 py-6",children:e.jsxs("form",{className:"relative",onSubmit:K(U),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 hover:border-gray-300",onClick:()=>I(!v),children:[e.jsx(te,{}),e.jsx("span",{children:"Filters"}),o.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-white",children:o.length})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 focus-within:border-gray-300",children:[e.jsx(se,{className:"text-xl text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search",className:"border-none p-0 placeholder:text-gray-400 focus:outline-none",style:{boxShadow:"none"},onInput:t=>{var a;return P("query","cs",(a=t.target)==null?void 0:a.value)}}),e.jsx(ae,{className:"text-lg text-gray-400 hover:text-gray-600"})]})]}),v&&e.jsxs("div",{className:"absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-5 shadow-lg",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-700",children:"Filters"}),e.jsx(pe,{onClick:()=>{d([]),x([]),setFilterValues({})},className:"cursor-pointer text-lg text-gray-400 hover:text-gray-600"})]}),o==null?void 0:o.map((t,a)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:"w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0",onChange:r=>{H(r.target.value)},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",onChange:r=>P(t,S,r.target.value)}),e.jsx(re,{className:"cursor-pointer text-2xl text-red-500 hover:text-red-600",onClick:()=>{d(r=>r.filter(i=>i!==t)),x(r=>r.filter(i=>!i.includes(t)))}})]},a)),e.jsxs("div",{className:"relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"flex cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 text-gray-600 hover:bg-gray-200",onClick:()=>{N(!w)},children:[e.jsx(ie,{}),"Add filter"]}),w&&e.jsx("div",{className:"absolute top-11 z-10 w-48 rounded-md border border-gray-200 bg-white py-2 shadow-lg",children:e.jsx("ul",{className:"flex flex-col",children:A.map(t=>e.jsx("li",{className:`px-4 py-2 ${o.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer text-gray-700 hover:bg-gray-50"}`,onClick:()=>{o.includes(t.header)||d(a=>[...a,t.header]),N(!1)},children:t.header},t.header))})}),o.length>0&&e.jsx("button",{type:"button",onClick:()=>{d([]),x([])},className:"py-2 pl-4 text-gray-600 hover:text-gray-800",children:"Clear all filter"})]})]})]})}),C?e.jsx(xe,{}):e.jsx("div",{className:"px-8",children:e.jsxs("div",{className:"overflow-x-auto rounded-lg border border-gray-200 bg-white shadow",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:A.map((t,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:f.map((t,a)=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:t.first_name}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:t.email}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:t.query})]},a))})]}),!C&&f.length===0&&e.jsx("div",{className:"px-6 py-4 text-center text-sm text-gray-500",children:"No reports found"})]})}),e.jsx("div",{className:"px-8 py-4",children:e.jsx(de,{currentPage:l,pageCount:b,pageSize:n,canPreviousPage:$,canNextPage:z,updatePageSize:V,previousPage:X,nextPage:Q})})]})};export{Ge as default};
