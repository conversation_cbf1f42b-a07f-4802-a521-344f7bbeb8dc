import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as a,R as Y}from"./vendor-2ae44a2e.js";import{u as Z}from"./react-hook-form-47c010f8.js";import{M as I,A as ee,G as se,s as C,t as te}from"./index-b2ff2fa1.js";import{o as ae}from"./yup-5abd4662.js";import{c as ne,a as u}from"./yup-5c93ed04.js";import{b as re}from"./websocket-791f88c1.js";import{_ as oe}from"./MoonLoader-62b0139a.js";import{$ as o}from"./@headlessui/react-7bce1936.js";import{M as A,S as le}from"./SpeakerWaveIcon-4df22e9a.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";const l={INACTIVE:"inactive",IN_PROGRESS:"recording"},R={"English (USA)":"English (USA)","English (UK)":"English (UK)","English (Australia)":"English (Australia)","English (Canada)":"English (Canada)","French (France)":"French (France)","French (Canada)":"French (Canada)","Spanish (Spain)":"Spanish (Spain)","Spanish (Mexico)":"Spanish (Mexico)",Russian:"Russian"},P="audio/webm";let m=new I;const Fe=()=>{var N,S,y,v,_,E;const{state:h,dispatch:k}=a.useContext(ee),{dispatch:x}=a.useContext(se),[g,ie]=a.useState(!1),c=a.useRef(null),[p,b]=a.useState(l.INACTIVE),[T,ce]=a.useState(null);a.useState([]);const[$,U]=a.useState("+1"),[F,G]=a.useState([]),[L,M]=a.useState([]),[f,V]=a.useState(null),[d,w]=a.useState(null),O=ne({assistant_id:u().required("Please select an assistant"),number_id:u().required("Please select the inbound number"),to_number:u().required("Please enter the destination number"),language:u().required("Please select a language")}),{register:i,handleSubmit:q,setError:K,setValue:de,formState:{errors:r,isSubmitting:W,isValid:D},reset:z}=Z({mode:"onChange",resolver:ae(O),defaultValues:{assistant_id:"",number_id:"",to_number:""}}),B=async s=>{let t=new I;try{if(t.setTable("numbers"),!s.number_id||!s.assistant_id)throw new Error("Missing values");await t.callRawAPI("/v3/api/custom/voiceoutreach/user/test_call",{from_number:s.number_id,assistant_id:s.assistant_id,to_number:$+s.to_number,language:s.language},"POST"),C(x,"Successful"),z()}catch(n){te(k,n.message),C(x,n.message,1e4,"error"),K("assistant_id",{type:"manual",message:n.message})}},H=async()=>{b(l.IN_PROGRESS);const s=new MediaRecorder(T,{mimeType:P});c.current=s,c.current.start(),c.current.ondataavailable=t=>{typeof t.data>"u"||t.data.size!==0&&d&&d.send(t.data)}},J=()=>{b(l.INACTIVE),c.current.stop()},Q=()=>{const s=new re.w3cwebsocket("wss://callagentds.manaknightdigital.com/1");s.onopen=()=>{},s.onmessage=t=>{const n=new Blob([t.data],{type:P}),X=URL.createObjectURL(n);V(X)},s.onclose=()=>{},s.onerror=t=>{console.error("WebSocket error:",t)},w(s)},j=()=>{d&&(d.close(),w(null))};return Y.useEffect(()=>(x({type:"SETPATH",payload:{path:"voice"}}),async function(){m.setTable("numbers");const t=await m.callRestAPI({},"GETALL");t.error||M(t.list?t.list.map(n=>({number_id:n.id,name:n.number})):[])}(),async function(){m.setTable("assistants");const t=await m.callRestAPI({user_id:h.user,filter:[`user_id,eq,${h.user}`]},"GETALL");t.error||G(t.list?t.list.map(n=>({assistant_id:n.id,name:n.assistant_name})):[])}(),()=>{j()}),[]),e.jsx("div",{className:"mx-auto flex w-full justify-center rounded-md bg-[#1d2937] px-5 py-10 sm:w-[90%] md:max-w-2xl md:px-0 lg:max-w-2xl 2xl:max-w-2xl",children:e.jsxs(o.Group,{as:"div",className:"w-full max-w-xl rounded-xl bg-[#1d2937] px-0 py-0 text-[#ffffff]",children:[e.jsx(o.List,{className:"flex items-center justify-center",children:e.jsx(o,{className:({selected:s})=>` rounded-lg px-3 py-1 text-2xl ${s?" text-white":"text-white"} `,children:"Call your phone"})}),e.jsxs(o.Panels,{children:[e.jsxs(o.Panel,{as:"form",className:"max-w-xl",onSubmit:q(B),children:[e.jsxs("div",{className:"mt-3 flex max-w-xl flex-col items-center justify-center",children:[e.jsx("audio",{className:"h-[fit-content] w-[100%]",loop:!1,src:"https://via.placeholder.com/50?text=%20",controls:!1,muted:!1,autoPlay:!1}),null,g&&p===l.INACTIVE?e.jsxs(e.Fragment,{children:[e.jsx(A,{onClick:()=>{H(),Q()},className:"h-[50px] w-[50px] cursor-pointer"}),e.jsxs("div",{className:"my-4 flex items-center",children:[e.jsx("span",{className:"block  h-[15px] w-[15px] rounded-full bg-[red]"}),e.jsx("span",{className:"mx-2 block font-bold",children:"Start recording"})]})]}):null,p===l.IN_PROGRESS?e.jsxs(e.Fragment,{children:[e.jsx(A,{onClick:()=>{J(),j()},className:"h-[50px] w-[50px] cursor-pointer"}),e.jsx("div",{className:"my-4 flex items-center",children:e.jsx("span",{className:"block h-[15px] w-[15px] animate-pulse rounded-full bg-[red]"})})]}):null,f&&p===l.INACTIVE?e.jsx("div",{className:"audio-player",children:e.jsx("audio",{src:f,controls:!0})}):null]}),e.jsxs("div",{className:"bg-[#1d2937]",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Assistants"}),e.jsxs("select",{type:"dropdown",id:"assistant_id",...i("assistant_id"),className:`focus:shadow-outline w-full appearance-none rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none md:text-base ${(N=r.assistant_id)!=null&&N.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Choose Assistant"}),F.map((s,t)=>e.jsx("option",{value:s.assistant_id,children:s.name},t+1))]})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Phone Number"}),e.jsxs("select",{type:"dropdown",id:"number_id",...i("number_id"),className:`focus:shadow-outline flex h-[30px] w-full appearance-none items-center rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none md:text-base lg:h-[38px] xl:h-[43px] xl:h-[43px] ${(S=r.number_id)!=null&&S.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Choose Phone Number"}),L.map((s,t)=>e.jsx("option",{value:s.number_id,children:s.name},t+1))]})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{className:"mb-3 text-[15px] font-medium",children:"Phone to Call"}),e.jsxs("div",{className:"mt-2 flex",children:[e.jsxs("select",{id:"country_code",...i("country_code"),onChange:s=>U(s.target.value),className:`focus:shadow-outline h-[30px] appearance-none rounded-l border bg-[#1d2937] px-3 py-1 pr-6 text-[12px] leading-tight text-white shadow focus:outline-none md:text-base md:text-base lg:h-[38px] xl:h-[43px] xl:h-[43px] ${(y=r.country_code)!=null&&y.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Country Code"}),e.jsx("option",{value:"+1",children:"+1 (USA)"}),e.jsx("option",{value:"+1",children:"+1 (CA)"}),e.jsx("option",{value:"+44",children:"+44 (UK)"}),e.jsx("option",{value:"+234",children:"+234 (Nigeria)"}),e.jsx("option",{value:"+40",children:"+40 (ROM)"}),e.jsx("option",{value:"+92",children:"+92 (PK)"})]}),e.jsx("input",{type:"text",placeholder:"Enter Number",...i("to_number"),className:`focus:shadow-outline h-[30px] w-full appearance-none rounded-r border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow placeholder:text-gray-300 focus:outline-none md:text-base lg:h-[38px] xl:h-[43px] ${(v=r.to_number)!=null&&v.message?"border-red-500":""}`})]})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Language"}),e.jsxs("select",{type:"dropdown",id:"language",...i("language"),className:`focus:shadow-outline w-full appearance-none rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none md:text-base ${(_=r.language)!=null&&_.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Select Language"}),Object.keys(R).map(s=>e.jsx("option",{value:s,children:R[s]},s))]}),((E=r.language)==null?void 0:E.message)&&e.jsx("p",{className:"text-xs italic text-red-500",children:r.language.message})]}),e.jsx("div",{className:"flex w-full items-center justify-center",children:e.jsxs("button",{disabled:!D,className:"mt-4 flex w-full items-center justify-center gap-2 rounded-[3px] bg-[#19b2f6]/80 px-5 py-2 text-white shadow-md shadow-black/30 hover:bg-[#19b2f6]/90 disabled:cursor-not-allowed disabled:opacity-50",children:[W?e.jsx(oe,{color:"white",loading:!0,size:20}):null,e.jsx("span",{children:"Send Call"})]})})]}),e.jsxs(o.Panel,{as:"div",className:"flex h-[435px] flex-col items-center justify-center gap-4",children:[e.jsx(le,{className:"h-12 w-12"}),e.jsx("h3",{children:"Coming soon"})]})]})]})})};export{Fe as default};
