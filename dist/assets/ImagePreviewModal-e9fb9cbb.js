import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as l}from"./vendor-2ae44a2e.js";const c=({file:s,handleFileUpload:n,cancelFileUpload:t})=>{const[a,i]=l.useState();return l.useEffect(()=>{s.length>0&&i(URL.createObjectURL(s[0]))},[s]),e.jsx(e.Fragment,{children:e.jsxs("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:[e.jsx("div",{className:"fixed inset-0 w-full h-full bg-black opacity-40",onClick:t}),e.jsx("div",{className:"flex items-center min-h-screen px-4 py-8",children:e.jsx("div",{className:"relative w-full max-w-lg p-4 mx-auto bg-white rounded-md shadow-lg",children:e.jsx("div",{className:"mt-3 sm:flex",children:e.jsxs("div",{className:"mt-2 text-center sm:ml-4 sm:text-left",children:[e.jsxs("h4",{className:"text-lg font-medium text-gray-800",children:["Send Image",s.length>1&&"s"]}),e.jsx("img",{className:"block",src:a}),e.jsxs("div",{className:"items-center gap-2 mt-3 sm:flex",children:[e.jsx("button",{className:"w-full mt-2 p-2.5 flex-1 mr-4 text-white bg-blue-600 whitespace-nowrap rounded-md outline-none ring-offset-2 ring-blue-600 focus:ring-2",onClick:n,children:"Send Message"}),e.jsx("button",{className:"w-full mt-2 p-2.5 flex-1 text-gray-800 rounded-md outline-none border ring-offset-2 ring-indigo-600 focus:ring-2",onClick:t,children:"Cancel"})]})]})})})})]})})};export{c as default};
