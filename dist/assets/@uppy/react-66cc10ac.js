import{r as a}from"../vendor-2ae44a2e.js";import{a as m}from"./core-a7fbc19c.js";import{P as n}from"../@fortawesome/react-fontawesome-27c5bed3.js";import{D as f,S as w}from"./dashboard-af41baed.js";import{D as E}from"./drag-drop-aa7c4730.js";import{P as U}from"./progress-bar-3613651c.js";import{F as O}from"./file-input-ca192629.js";const u=n.instanceOf(m),y=n.arrayOf(n.string),g=n.shape({strings:n.object,pluralize:n.func}),R=n.shape({id:n.string.isRequired,name:n.string.isRequired,placeholder:n.string}),P=n.oneOfType([n.arrayOf(R),n.func]),c=n.oneOfType([n.string,n.number]),v=["defaultChecked","defaultValue","suppressContentEditableWarning","suppressHydrationWarning","dangerouslySetInnerHTML","accessKey","className","contentEditable","contextMenu","dir","draggable","hidden","id","lang","placeholder","slot","spellCheck","style","tabIndex","title","translate","radioGroup","role","about","datatype","inlist","prefix","property","resource","typeof","vocab","autoCapitalize","autoCorrect","autoSave","color","itemProp","itemScope","itemType","itemID","itemRef","results","security","unselectable","inputMode","is","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],F=/^(aria-|data-)/,p=i=>Object.fromEntries(Object.entries(i).filter(e=>{let[t]=e;return F.test(t)||v.includes(t)}));function d(i,e){return Object.keys(i).some(t=>!Object.hasOwn(i,t)&&i[t]!==e[t])}class A extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){if(e.uppy!==this.props.uppy)this.uninstallPlugin(e),this.installPlugin();else if(d(this.props,e)){const{uppy:t,...o}={...this.props,target:this.container};this.plugin.setOptions(o)}}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e,...t}={id:"react:Dashboard",inline:!0,...this.props,target:this.container};e.use(f,t),this.plugin=e.getPlugin(t.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:t}=e;t.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...p(this.props)})}}A.propsTypes={uppy:u,disableInformer:n.bool,disableStatusBar:n.bool,disableThumbnailGenerator:n.bool,height:c,hideProgressAfterFinish:n.bool,hideUploadButton:n.bool,locale:g,metaFields:P,note:n.string,plugins:y,proudlyDisplayPoweredByUppy:n.bool,showProgressDetails:n.bool,width:c,thumbnailType:n.string,thumbnailWidth:n.number};class b extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){const{uppy:t,open:o,onRequestClose:s}=this.props;if(e.uppy!==t)this.uninstallPlugin(e),this.installPlugin();else if(d(this.props,e)){const{uppy:r,...l}={...this.props,inline:!1,onRequestCloseModal:s};this.plugin.setOptions(l)}e.open&&!o?this.plugin.closeModal():!e.open&&o&&this.plugin.openModal()}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{target:e=this.container,open:t,onRequestClose:o,uppy:s,...r}=this.props,l={...r,id:"react:DashboardModal",inline:!1,target:e,open:t,onRequestCloseModal:o};s.use(f,l),this.plugin=s.getPlugin(l.id),t&&this.plugin.openModal()}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:t}=e;t.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...p(this.props)})}}b.propTypes={uppy:u.isRequired,target:typeof window<"u"?n.instanceOf(window.HTMLElement):n.any,open:n.bool,onRequestClose:n.func,closeModalOnClickOutside:n.bool,disablePageScrollWhenModalOpen:n.bool,plugins:y,width:c,height:c,showProgressDetails:n.bool,note:n.string,metaFields:P,proudlyDisplayPoweredByUppy:n.bool,autoOpenFileEditor:n.bool,animateOpenClose:n.bool,browserBackButtonClose:n.bool,closeAfterFinish:n.bool,disableStatusBar:n.bool,disableInformer:n.bool,disableThumbnailGenerator:n.bool,disableLocalFiles:n.bool,disabled:n.bool,hideCancelButton:n.bool,hidePauseResumeButton:n.bool,hideProgressAfterFinish:n.bool,hideRetryButton:n.bool,hideUploadButton:n.bool,showLinkToFileUploadResult:n.bool,showRemoveButtonAfterComplete:n.bool,showSelectedFiles:n.bool,waitForThumbnailsBeforeUpload:n.bool,fileManagerSelectionType:n.string,theme:n.string,thumbnailType:n.string,thumbnailWidth:n.number,locale:g};b.defaultProps={metaFields:[],plugins:[],width:750,height:550,thumbnailWidth:280,thumbnailType:"image/jpeg",waitForThumbnailsBeforeUpload:!1,showLinkToFileUploadResult:!1,showProgressDetails:!1,hideUploadButton:!1,hideCancelButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideProgressAfterFinish:!1,note:null,closeModalOnClickOutside:!1,closeAfterFinish:!1,disableStatusBar:!1,disableInformer:!1,disableThumbnailGenerator:!1,disablePageScrollWhenModalOpen:!0,animateOpenClose:!0,fileManagerSelectionType:"files",proudlyDisplayPoweredByUppy:!0,showSelectedFiles:!0,showRemoveButtonAfterComplete:!1,browserBackButtonClose:!1,theme:"light",autoOpenFileEditor:!1,disabled:!1,disableLocalFiles:!1,open:void 0,target:void 0,locale:null,onRequestClose:void 0};class D extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){if(e.uppy!==this.props.uppy)this.uninstallPlugin(e),this.installPlugin();else if(d(this.props,e)){const{uppy:t,...o}={...this.props,target:this.container};this.plugin.setOptions(o)}}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e,locale:t,inputName:o,width:s,height:r,note:l}=this.props,h={id:"react:DragDrop",locale:t,inputName:o,width:s,height:r,note:l,target:this.container};e.use(E,h),this.plugin=e.getPlugin(h.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:t}=e;t.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...p(this.props)})}}D.propTypes={uppy:u.isRequired,locale:g,inputName:n.string,width:n.string,height:n.string,note:n.string};D.defaultProps={locale:null,inputName:"files[]",width:"100%",height:"100%",note:null};class B extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){if(e.uppy!==this.props.uppy)this.uninstallPlugin(e),this.installPlugin();else if(d(this.props,e)){const{uppy:t,...o}={...this.props,target:this.container};this.plugin.setOptions(o)}}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e,fixed:t,hideAfterFinish:o}=this.props,s={id:"react:ProgressBar",fixed:t,hideAfterFinish:o,target:this.container};e.use(U,s),this.plugin=e.getPlugin(s.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:t}=e;t.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...p(this.props)})}}B.propTypes={uppy:u.isRequired,fixed:n.bool,hideAfterFinish:n.bool};B.defaultProps={fixed:!1,hideAfterFinish:!0};class M extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){if(e.uppy!==this.props.uppy)this.uninstallPlugin(e),this.installPlugin();else if(d(this.props,e)){const{uppy:t,...o}={...this.props,target:this.container};this.plugin.setOptions(o)}}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e,hideUploadButton:t,hideRetryButton:o,hidePauseResumeButton:s,hideCancelButton:r,showProgressDetails:l,hideAfterFinish:h,doneButtonHandler:T}=this.props,C={id:"react:StatusBar",hideUploadButton:t,hideRetryButton:o,hidePauseResumeButton:s,hideCancelButton:r,showProgressDetails:l,hideAfterFinish:h,doneButtonHandler:T,target:this.container};e.use(w,C),this.plugin=e.getPlugin(C.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:t}=e;t.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e},...p(this.props)})}}M.propTypes={uppy:u.isRequired,hideUploadButton:n.bool,hideRetryButton:n.bool,hidePauseResumeButton:n.bool,hideCancelButton:n.bool,showProgressDetails:n.bool,hideAfterFinish:n.bool,doneButtonHandler:n.func};M.defaultProps={hideUploadButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideCancelButton:!1,showProgressDetails:!1,hideAfterFinish:!0,doneButtonHandler:null};class S extends a.Component{componentDidMount(){this.installPlugin()}componentDidUpdate(e){e.uppy!==this.props.uppy&&(this.uninstallPlugin(e),this.installPlugin())}componentWillUnmount(){this.uninstallPlugin()}installPlugin(){const{uppy:e,locale:t,pretty:o,inputName:s}=this.props,r={id:"react:FileInput",locale:t,pretty:o,inputName:s,target:this.container};e.use(O,r),this.plugin=e.getPlugin(r.id)}uninstallPlugin(e){e===void 0&&(e=this.props);const{uppy:t}=e;t.removePlugin(this.plugin)}render(){return a.createElement("div",{className:"uppy-Container",ref:e=>{this.container=e}})}}S.propTypes={uppy:u.isRequired,locale:g,pretty:n.bool,inputName:n.string};S.defaultProps={locale:void 0,pretty:!0,inputName:"files[]"};function H(i){if(typeof i!="function")throw new TypeError("useUppy: expected a function that returns a new Uppy instance");const e=a.useRef(void 0);if(e.current===void 0&&(e.current=i(),!(e.current instanceof m)))throw new TypeError(`useUppy: factory function must return an Uppy instance, got ${typeof e.current}`);return a.useEffect(()=>()=>{var t;(t=e.current)==null||t.close({reason:"unmount"}),e.current=void 0},[e]),e.current}export{A as D,H as u};
