import{y as r,x as pe,d as Fi,j as Q,F as Lt,c as Te,e as <PERSON>,p as Ae,T as Ci,h as Xe,f as Be}from"../@fullcalendar/core-a789a586.js";import{U as De,g as Si,i as Ti}from"./core-a7fbc19c.js";import{g as xt}from"../vendor-2ae44a2e.js";import{p as Ye}from"./compressor-d7e7d557.js";import{d as Ai,n as _t}from"./aws-s3-a38c5234.js";function dt(i,e,t,s){return t===0||i===e?i:s===0?e:i+(e-i)*2**(-s/t)}const $={STATE_ERROR:"error",STATE_WAITING:"waiting",STATE_PREPROCESSING:"preprocessing",STATE_UPLOADING:"uploading",STATE_POSTPROCESSING:"postprocessing",STATE_COMPLETE:"complete"};var zt={exports:{}};/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(i){(function(){var e={}.hasOwnProperty;function t(){for(var a="",d=0;d<arguments.length;d++){var h=arguments[d];h&&(a=n(a,s(h)))}return a}function s(a){if(typeof a=="string"||typeof a=="number")return a;if(typeof a!="object")return"";if(Array.isArray(a))return t.apply(null,a);if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]"))return a.toString();var d="";for(var h in a)e.call(a,h)&&a[h]&&(d=n(d,h));return d}function n(a,d){return d?a?a+" "+d:a+d:a}i.exports?(t.default=t,i.exports=t):window.classNames=t})()})(zt);var Ei=zt.exports;const x=xt(Ei);function Ie(i){const e=[];let t="indeterminate",s;for(const{progress:a}of Object.values(i)){const{preprocess:d,postprocess:h}=a;s==null&&(d||h)&&({mode:t,message:s}=d||h),(d==null?void 0:d.mode)==="determinate"&&e.push(d.value),(h==null?void 0:h.mode)==="determinate"&&e.push(h.value)}const n=e.reduce((a,d)=>a+d/e.length,0);return{mode:t,message:s,value:n}}function Di(i){const e=Math.floor(i/3600)%24,t=Math.floor(i/60)%60,s=Math.floor(i%60);return{hours:e,minutes:t,seconds:s}}function ki(i){const e=Di(i),t=e.hours===0?"":`${e.hours}h`,s=e.minutes===0?"":`${e.hours===0?e.minutes:` ${e.minutes.toString(10).padStart(2,"0")}`}m`,n=e.hours!==0?"":`${e.minutes===0?e.seconds:` ${e.seconds.toString(10).padStart(2,"0")}`}s`;return`${t}${s}${n}`}const Oi="·",ut=()=>` ${Oi} `;function Bi(i){const{newFiles:e,isUploadStarted:t,recoveredState:s,i18n:n,uploadState:a,isSomeGhost:d,startUpload:h}=i,o=x("uppy-u-reset","uppy-c-btn","uppy-StatusBar-actionBtn","uppy-StatusBar-actionBtn--upload",{"uppy-c-btn-primary":a===$.STATE_WAITING},{"uppy-StatusBar-actionBtn--disabled":d}),l=e&&t&&!s?n("uploadXNewFiles",{smart_count:e}):n("uploadXFiles",{smart_count:e});return r("button",{type:"button",className:o,"aria-label":n("uploadXFiles",{smart_count:e}),onClick:h,disabled:d,"data-uppy-super-focusable":!0},l)}function Ii(i){const{i18n:e,uppy:t}=i;return r("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--retry","aria-label":e("retryUpload"),onClick:()=>t.retryAll().catch(()=>{}),"data-uppy-super-focusable":!0,"data-cy":"retry"},r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"8",height:"10",viewBox:"0 0 8 10"},r("path",{d:"M4 2.408a2.75 2.75 0 1 0 2.75 2.75.626.626 0 0 1 1.25.018v.023a4 4 0 1 1-4-4.041V.25a.25.25 0 0 1 .389-.208l2.299 1.533a.25.25 0 0 1 0 .416l-2.3 1.533A.25.25 0 0 1 4 3.316v-.908z"})),e("retry"))}function Ui(i){const{i18n:e,uppy:t}=i;return r("button",{type:"button",className:"uppy-u-reset uppy-StatusBar-actionCircleBtn",title:e("cancel"),"aria-label":e("cancel"),onClick:()=>t.cancelAll(),"data-cy":"cancel","data-uppy-super-focusable":!0},r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"16",height:"16",viewBox:"0 0 16 16"},r("g",{fill:"none",fillRule:"evenodd"},r("circle",{fill:"#888",cx:"8",cy:"8",r:"8"}),r("path",{fill:"#FFF",d:"M9.283 8l2.567 2.567-1.283 1.283L8 9.283 5.433 11.85 4.15 10.567 6.717 8 4.15 5.433 5.433 4.15 8 6.717l2.567-2.567 1.283 1.283z"}))))}function Ni(i){const{isAllPaused:e,i18n:t,isAllComplete:s,resumableUploads:n,uppy:a}=i,d=t(e?"resume":"pause");function h(){if(!s){if(!n){a.cancelAll();return}if(e){a.resumeAll();return}a.pauseAll()}}return r("button",{title:d,"aria-label":d,className:"uppy-u-reset uppy-StatusBar-actionCircleBtn",type:"button",onClick:h,"data-cy":"togglePauseResume","data-uppy-super-focusable":!0},r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"16",height:"16",viewBox:"0 0 16 16"},r("g",{fill:"none",fillRule:"evenodd"},r("circle",{fill:"#888",cx:"8",cy:"8",r:"8"}),r("path",{fill:"#FFF",d:e?"M6 4.25L11.5 8 6 11.75z":"M5 4.5h2v7H5v-7zm4 0h2v7H9v-7z"}))))}function Ri(i){const{i18n:e,doneButtonHandler:t}=i;return r("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--done",onClick:t,"data-uppy-super-focusable":!0},e("done"))}function $t(){return r("svg",{className:"uppy-StatusBar-spinner","aria-hidden":"true",focusable:"false",width:"14",height:"14"},r("path",{d:"M13.983 6.547c-.12-2.509-1.64-4.893-3.939-5.936-2.48-1.127-5.488-.656-7.556 1.094C.524 3.367-.398 6.048.162 8.562c.556 2.495 2.46 4.52 4.94 5.183 2.932.784 5.61-.602 7.256-3.015-1.493 1.993-3.745 3.309-6.298 2.868-2.514-.434-4.578-2.349-5.153-4.84a6.226 6.226 0 0 1 2.98-6.778C6.34.586 9.74 1.1 11.373 3.493c.407.596.693 1.282.842 1.988.127.598.073 1.197.161 1.794.078.525.543 1.257 1.15.864.525-.341.49-1.05.456-1.592-.007-.15.02.3 0 0",fillRule:"evenodd"}))}function Mi(i){const{progress:e}=i,{value:t,mode:s,message:n}=e,a="·";return r("div",{className:"uppy-StatusBar-content"},r($t,null),s==="determinate"?`${Math.round(t*100)}% ${a} `:"",n)}function Li(i){const{numUploads:e,complete:t,totalUploadedSize:s,totalSize:n,totalETA:a,i18n:d}=i,h=e>1;return r("div",{className:"uppy-StatusBar-statusSecondary"},h&&d("filesUploadedOfTotal",{complete:t,smart_count:e}),r("span",{className:"uppy-StatusBar-additionalInfo"},h&&ut(),d("dataUploadedOfTotal",{complete:Ye(s),total:Ye(n)}),ut(),d("xTimeLeft",{time:ki(a)})))}function Ht(i){const{i18n:e,complete:t,numUploads:s}=i;return r("div",{className:"uppy-StatusBar-statusSecondary"},e("filesUploadedOfTotal",{complete:t,smart_count:s}))}function xi(i){const{i18n:e,newFiles:t,startUpload:s}=i,n=x("uppy-u-reset","uppy-c-btn","uppy-StatusBar-actionBtn","uppy-StatusBar-actionBtn--uploadNewlyAdded");return r("div",{className:"uppy-StatusBar-statusSecondary"},r("div",{className:"uppy-StatusBar-statusSecondaryHint"},e("xMoreFilesAdded",{smart_count:t})),r("button",{type:"button",className:n,"aria-label":e("uploadXFiles",{smart_count:t}),onClick:s},e("upload")))}function _i(i){const{i18n:e,supportsUploadProgress:t,totalProgress:s,showProgressDetails:n,isUploadStarted:a,isAllComplete:d,isAllPaused:h,newFiles:o,numUploads:l,complete:u,totalUploadedSize:c,totalSize:p,totalETA:g,startUpload:f}=i,y=o&&a;if(!a||d)return null;const m=e(h?"paused":"uploading");function w(){return!h&&!y&&n?t?r(Li,{numUploads:l,complete:u,totalUploadedSize:c,totalSize:p,totalETA:g,i18n:e}):r(Ht,{i18n:e,complete:u,numUploads:l}):null}return r("div",{className:"uppy-StatusBar-content","aria-label":m,title:m},h?null:r($t,null),r("div",{className:"uppy-StatusBar-status"},r("div",{className:"uppy-StatusBar-statusPrimary"},t?`${m}: ${s}%`:m),w(),y?r(xi,{i18n:e,newFiles:o,startUpload:f}):null))}function zi(i){const{i18n:e}=i;return r("div",{className:"uppy-StatusBar-content",role:"status",title:e("complete")},r("div",{className:"uppy-StatusBar-status"},r("div",{className:"uppy-StatusBar-statusPrimary"},r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-StatusBar-statusIndicator uppy-c-icon",width:"15",height:"11",viewBox:"0 0 15 11"},r("path",{d:"M.414 5.843L1.627 4.63l3.472 3.472L13.202 0l1.212 1.213L5.1 10.528z"})),e("complete"))))}function $i(i){const{error:e,i18n:t,complete:s,numUploads:n}=i;function a(){const d=`${t("uploadFailed")} 

 ${e}`;alert(d)}return r("div",{className:"uppy-StatusBar-content",title:t("uploadFailed")},r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-StatusBar-statusIndicator uppy-c-icon",width:"11",height:"11",viewBox:"0 0 11 11"},r("path",{d:"M4.278 5.5L0 1.222 1.222 0 5.5 4.278 9.778 0 11 1.222 6.722 5.5 11 9.778 9.778 11 5.5 6.722 1.222 11 0 9.778z"})),r("div",{className:"uppy-StatusBar-status"},r("div",{className:"uppy-StatusBar-statusPrimary"},t("uploadFailed"),r("button",{className:"uppy-u-reset uppy-StatusBar-details","aria-label":t("showErrorDetails"),"data-microtip-position":"top-right","data-microtip-size":"medium",onClick:a,type:"button"},"?")),r(Ht,{i18n:t,complete:s,numUploads:n})))}const{STATE_ERROR:ht,STATE_WAITING:ct,STATE_PREPROCESSING:Ue,STATE_UPLOADING:ye,STATE_POSTPROCESSING:Ne,STATE_COMPLETE:be}=$;function Vt(i){const{newFiles:e,allowNewUpload:t,isUploadInProgress:s,isAllPaused:n,resumableUploads:a,error:d,hideUploadButton:h,hidePauseResumeButton:o,hideCancelButton:l,hideRetryButton:u,recoveredState:c,uploadState:p,totalProgress:g,files:f,supportsUploadProgress:y,hideAfterFinish:m,isSomeGhost:w,doneButtonHandler:C,isUploadStarted:b,i18n:F,startUpload:T,uppy:I,isAllComplete:A,showProgressDetails:E,numUploads:N,complete:Z,totalSize:W,totalETA:fe,totalUploadedSize:me}=i;function ee(){switch(p){case Ne:case Ue:{const ge=Ie(f);return ge.mode==="determinate"?ge.value*100:g}case ht:return null;case ye:return y?g:null;default:return g}}function G(){switch(p){case Ne:case Ue:{const{mode:ge}=Ie(f);return ge==="indeterminate"}case ye:return!y;default:return!1}}function ke(){if(c)return!1;switch(p){case ct:return h||e===0;case be:return m;default:return!1}}const ne=ee(),pi=ke(),Oe=ne??100,fi=!d&&e&&!s&&!n&&t&&!h,mi=!l&&p!==ct&&p!==be,gi=a&&!o&&p===ye,yi=d&&!A&&!u,bi=C&&p===be,vi=x("uppy-StatusBar-progress",{"is-indeterminate":G()}),wi=x("uppy-StatusBar",`is-${p}`,{"has-ghosts":w});return r("div",{className:wi,"aria-hidden":pi},r("div",{className:vi,style:{width:`${Oe}%`},role:"progressbar","aria-label":`${Oe}%`,"aria-valuetext":`${Oe}%`,"aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":ne}),(()=>{switch(p){case Ue:case Ne:return r(Mi,{progress:Ie(f)});case be:return r(zi,{i18n:F});case ht:return r($i,{error:d,i18n:F,numUploads:N,complete:Z});case ye:return r(_i,{i18n:F,supportsUploadProgress:y,totalProgress:g,showProgressDetails:E,isUploadStarted:b,isAllComplete:A,isAllPaused:n,newFiles:e,numUploads:N,complete:Z,totalUploadedSize:me,totalSize:W,totalETA:fe,startUpload:T});default:return null}})(),r("div",{className:"uppy-StatusBar-actions"},c||fi?r(Bi,{newFiles:e,isUploadStarted:b,recoveredState:c,i18n:F,isSomeGhost:w,startUpload:T,uploadState:p}):null,yi?r(Ii,{i18n:F,uppy:I}):null,gi?r(Ni,{isAllPaused:n,i18n:F,isAllComplete:A,resumableUploads:a,uppy:I}):null,mi?r(Ui,{i18n:F,uppy:I}):null,bi?r(Ri,{i18n:F,doneButtonHandler:C}):null))}Vt.defaultProps={doneButtonHandler:void 0,hideAfterFinish:!1,hideCancelButton:!1,hidePauseResumeButton:!1,hideRetryButton:!1,hideUploadButton:void 0,showProgressDetails:void 0};const Hi={strings:{uploading:"Uploading",complete:"Complete",uploadFailed:"Upload failed",paused:"Paused",retry:"Retry",cancel:"Cancel",pause:"Pause",resume:"Resume",done:"Done",filesUploadedOfTotal:{0:"%{complete} of %{smart_count} file uploaded",1:"%{complete} of %{smart_count} files uploaded"},dataUploadedOfTotal:"%{complete} of %{total}",xTimeLeft:"%{time} left",uploadXFiles:{0:"Upload %{smart_count} file",1:"Upload %{smart_count} files"},uploadXNewFiles:{0:"Upload +%{smart_count} file",1:"Upload +%{smart_count} files"},upload:"Upload",retryUpload:"Retry upload",xMoreFilesAdded:{0:"%{smart_count} more file added",1:"%{smart_count} more files added"},showErrorDetails:"Show error details"}};function S(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var Vi=0;function se(i){return"__private_"+Vi+++"_"+i}const qi={version:"3.3.3"},ji=2e3,Wi=2e3;function Gi(i,e,t,s){if(i)return $.STATE_ERROR;if(e)return $.STATE_COMPLETE;if(t)return $.STATE_WAITING;let n=$.STATE_WAITING;const a=Object.keys(s);for(let d=0;d<a.length;d++){const{progress:h}=s[a[d]];if(h.uploadStarted&&!h.uploadComplete)return $.STATE_UPLOADING;h.preprocess&&(n=$.STATE_PREPROCESSING),h.postprocess&&n!==$.STATE_PREPROCESSING&&(n=$.STATE_POSTPROCESSING)}return n}const Ki={target:"body",hideUploadButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideCancelButton:!1,showProgressDetails:!1,hideAfterFinish:!0,doneButtonHandler:null};var U=se("lastUpdateTime"),R=se("previousUploadedBytes"),q=se("previousSpeed"),B=se("previousETA"),Re=se("computeSmoothETA"),ae=se("onUploadStart");let qt=class extends De{constructor(e,t){super(e,{...Ki,...t}),Object.defineProperty(this,Re,{value:Xi}),Object.defineProperty(this,U,{writable:!0,value:void 0}),Object.defineProperty(this,R,{writable:!0,value:void 0}),Object.defineProperty(this,q,{writable:!0,value:void 0}),Object.defineProperty(this,B,{writable:!0,value:void 0}),this.startUpload=()=>this.uppy.upload().catch(()=>{}),Object.defineProperty(this,ae,{writable:!0,value:()=>{const{recoveredState:s}=this.uppy.getState();if(S(this,q)[q]=null,S(this,B)[B]=null,s){S(this,R)[R]=Object.values(s.files).reduce((n,a)=>{let{progress:d}=a;return n+d.bytesUploaded},0),this.uppy.emit("restore-confirmed");return}S(this,U)[U]=performance.now(),S(this,R)[R]=0}}),this.id=this.opts.id||"StatusBar",this.title="StatusBar",this.type="progressindicator",this.defaultLocale=Hi,this.i18nInit(),this.render=this.render.bind(this),this.install=this.install.bind(this)}render(e){const{capabilities:t,files:s,allowNewUpload:n,totalProgress:a,error:d,recoveredState:h}=e,{newFiles:o,startedFiles:l,completeFiles:u,isUploadStarted:c,isAllComplete:p,isAllErrored:g,isAllPaused:f,isUploadInProgress:y,isSomeGhost:m}=this.uppy.getObjectOfFilesPerState(),w=h?Object.values(s):o,C=!!t.resumableUploads,b=t.uploadProgress!==!1;let F=0,T=0;l.forEach(A=>{F+=A.progress.bytesTotal||0,T+=A.progress.bytesUploaded||0});const I=S(this,Re)[Re]({uploaded:T,total:F,remaining:F-T});return Vt({error:d,uploadState:Gi(d,p,h,e.files||{}),allowNewUpload:n,totalProgress:a,totalSize:F,totalUploadedSize:T,isAllComplete:!1,isAllPaused:f,isAllErrored:g,isUploadStarted:c,isUploadInProgress:y,isSomeGhost:m,recoveredState:h,complete:u.length,newFiles:w.length,numUploads:l.length,totalETA:I,files:s,i18n:this.i18n,uppy:this.uppy,startUpload:this.startUpload,doneButtonHandler:this.opts.doneButtonHandler,resumableUploads:C,supportsUploadProgress:b,showProgressDetails:this.opts.showProgressDetails,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,hideCancelButton:this.opts.hideCancelButton,hideAfterFinish:this.opts.hideAfterFinish,isTargetDOMEl:this.isTargetDOMEl})}onMount(){const e=this.el;Si(e)||(e.dir="ltr")}install(){const{target:e}=this.opts;e&&this.mount(e,this),this.uppy.on("upload",S(this,ae)[ae]),S(this,U)[U]=performance.now(),S(this,R)[R]=this.uppy.getFiles().reduce((t,s)=>t+s.progress.bytesUploaded,0)}uninstall(){this.unmount(),this.uppy.off("upload",S(this,ae)[ae])}};function Xi(i){var e,t;if(i.total===0||i.remaining===0)return 0;(t=(e=S(this,U))[U])!=null||(e[U]=performance.now());const s=performance.now()-S(this,U)[U];if(s===0){var n;return Math.round(((n=S(this,B)[B])!=null?n:0)/100)/10}const a=i.uploaded-S(this,R)[R];if(S(this,R)[R]=i.uploaded,a<=0){var d;return Math.round(((d=S(this,B)[B])!=null?d:0)/100)/10}const h=a/s,o=S(this,q)[q]==null?h:dt(h,S(this,q)[q],ji,s);S(this,q)[q]=o;const l=i.remaining/o,u=Math.max(S(this,B)[B]-s,0),c=S(this,B)[B]==null?l:dt(l,u,Wi,s);return S(this,B)[B]=c,S(this,U)[U]=performance.now(),Math.round(c/100)/10}qt.VERSION=qi.version;const pt=300;class Yi extends pe{constructor(){super(...arguments),this.ref=Fi()}componentWillEnter(e){this.ref.current.style.opacity="1",this.ref.current.style.transform="none",setTimeout(e,pt)}componentWillLeave(e){this.ref.current.style.opacity="0",this.ref.current.style.transform="translateY(350%)",setTimeout(e,pt)}render(){const{children:e}=this.props;return r("div",{className:"uppy-Informer-animated",ref:this.ref},e)}}function Qi(i,e){return Object.assign(i,e)}function Ji(i,e){var t;return(t=i==null?void 0:i.key)!=null?t:e}function Zi(i,e){const t=i._ptgLinkedRefs||(i._ptgLinkedRefs={});return t[e]||(t[e]=s=>{i.refs[e]=s})}function re(i){const e={};for(let t=0;t<i.length;t++)if(i[t]!=null){const s=Ji(i[t],t.toString(36));e[s]=i[t]}return e}function es(i,e){i=i||{},e=e||{};const t=d=>e.hasOwnProperty(d)?e[d]:i[d],s={};let n=[];for(const d in i)e.hasOwnProperty(d)?n.length&&(s[d]=n,n=[]):n.push(d);const a={};for(const d in e){if(s.hasOwnProperty(d))for(let h=0;h<s[d].length;h++){const o=s[d][h];a[s[d][h]]=t(o)}a[d]=t(d)}for(let d=0;d<n.length;d++)a[n[d]]=t(n[d]);return a}const ts=i=>i;class jt extends pe{constructor(e,t){super(e,t),this.refs={},this.state={children:re(Q(Q(this.props.children))||[])},this.performAppear=this.performAppear.bind(this),this.performEnter=this.performEnter.bind(this),this.performLeave=this.performLeave.bind(this)}componentWillMount(){this.currentlyTransitioningKeys={},this.keysToAbortLeave=[],this.keysToEnter=[],this.keysToLeave=[]}componentDidMount(){const e=this.state.children;for(const t in e)e[t]&&this.performAppear(t)}componentWillReceiveProps(e){const t=re(Q(e.children)||[]),s=this.state.children;this.setState(a=>({children:es(a.children,t)}));let n;for(n in t)if(t.hasOwnProperty(n)){const a=s&&s.hasOwnProperty(n);t[n]&&a&&this.currentlyTransitioningKeys[n]?(this.keysToEnter.push(n),this.keysToAbortLeave.push(n)):t[n]&&!a&&!this.currentlyTransitioningKeys[n]&&this.keysToEnter.push(n)}for(n in s)if(s.hasOwnProperty(n)){const a=t&&t.hasOwnProperty(n);s[n]&&!a&&!this.currentlyTransitioningKeys[n]&&this.keysToLeave.push(n)}}componentDidUpdate(){const{keysToEnter:e}=this;this.keysToEnter=[],e.forEach(this.performEnter);const{keysToLeave:t}=this;this.keysToLeave=[],t.forEach(this.performLeave)}_finishAbort(e){const t=this.keysToAbortLeave.indexOf(e);t!==-1&&this.keysToAbortLeave.splice(t,1)}performAppear(e){this.currentlyTransitioningKeys[e]=!0;const t=this.refs[e];t!=null&&t.componentWillAppear?t.componentWillAppear(this._handleDoneAppearing.bind(this,e)):this._handleDoneAppearing(e)}_handleDoneAppearing(e){const t=this.refs[e];t!=null&&t.componentDidAppear&&t.componentDidAppear(),delete this.currentlyTransitioningKeys[e],this._finishAbort(e);const s=re(Q(this.props.children)||[]);(!s||!s.hasOwnProperty(e))&&this.performLeave(e)}performEnter(e){this.currentlyTransitioningKeys[e]=!0;const t=this.refs[e];t!=null&&t.componentWillEnter?t.componentWillEnter(this._handleDoneEntering.bind(this,e)):this._handleDoneEntering(e)}_handleDoneEntering(e){const t=this.refs[e];t!=null&&t.componentDidEnter&&t.componentDidEnter(),delete this.currentlyTransitioningKeys[e],this._finishAbort(e);const s=re(Q(this.props.children)||[]);(!s||!s.hasOwnProperty(e))&&this.performLeave(e)}performLeave(e){if(this.keysToAbortLeave.indexOf(e)!==-1)return;this.currentlyTransitioningKeys[e]=!0;const s=this.refs[e];s!=null&&s.componentWillLeave?s.componentWillLeave(this._handleDoneLeaving.bind(this,e)):this._handleDoneLeaving(e)}_handleDoneLeaving(e){if(this.keysToAbortLeave.indexOf(e)!==-1)return;const s=this.refs[e];s!=null&&s.componentDidLeave&&s.componentDidLeave(),delete this.currentlyTransitioningKeys[e];const n=re(Q(this.props.children)||[]);if(n&&n.hasOwnProperty(e))this.performEnter(e);else{const a=Qi({},this.state.children);delete a[e],this.setState({children:a})}}render(e,t){let{childFactory:s,transitionLeave:n,transitionName:a,transitionAppear:d,transitionEnter:h,transitionLeaveTimeout:o,transitionEnterTimeout:l,transitionAppearTimeout:u,component:c,...p}=e,{children:g}=t;const f=Object.entries(g).map(y=>{let[m,w]=y;if(!w)return;const C=Zi(this,m);return Lt(s(w),{ref:C,key:m})}).filter(Boolean);return r(c,p,f)}}jt.defaultProps={component:"span",childFactory:ts};const is={version:"3.1.0"};class Wt extends De{constructor(e,t){super(e,t),this.render=s=>r("div",{className:"uppy uppy-Informer"},r(jt,null,s.info.map(n=>r(Yi,{key:n.message},r("p",{role:"alert"},n.message," ",n.details&&r("span",{"aria-label":n.details,"data-microtip-position":"top-left","data-microtip-size":"medium",role:"tooltip",onClick:()=>alert(`${n.message} 

 ${n.details}`)},"?")))))),this.type="progressindicator",this.id=this.opts.id||"Informer",this.title="Informer"}install(){const{target:e}=this.opts;e&&this.mount(e,this)}}Wt.VERSION=is.version;const ss=/^data:([^/]+\/[^,;]+(?:[^,]*?))(;base64)?,([\s\S]*)$/;function ns(i,e,t){var s,n;const a=ss.exec(i),d=(s=(n=e.mimeType)!=null?n:a==null?void 0:a[1])!=null?s:"plain/text";let h;if((a==null?void 0:a[2])!=null){const o=atob(decodeURIComponent(a[3])),l=new Uint8Array(o.length);for(let u=0;u<o.length;u++)l[u]=o.charCodeAt(u);h=[l]}else(a==null?void 0:a[3])!=null&&(h=[decodeURIComponent(a[3])]);return t?new File(h,e.name||"",{type:d}):new Blob(h,{type:d})}function ft(i){return i.startsWith("blob:")}function mt(i){return i?/^[^/]+\/(jpe?g|gif|png|svg|svg\+xml|bmp|webp|avif)$/.test(i):!1}function v(i,e,t){return e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}var Gt=typeof self<"u"?self:global;const ce=typeof navigator<"u",as=ce&&typeof HTMLImageElement>"u",gt=!(typeof global>"u"||typeof process>"u"||!process.versions||!process.versions.node),Kt=Gt.Buffer,Xt=!!Kt,rs=i=>i!==void 0;function Yt(i){return i===void 0||(i instanceof Map?i.size===0:Object.values(i).filter(rs).length===0)}function k(i){let e=new Error(i);throw delete e.stack,e}function yt(i){let e=function(t){let s=0;return t.ifd0.enabled&&(s+=1024),t.exif.enabled&&(s+=2048),t.makerNote&&(s+=2048),t.userComment&&(s+=1024),t.gps.enabled&&(s+=512),t.interop.enabled&&(s+=100),t.ifd1.enabled&&(s+=1024),s+2048}(i);return i.jfif.enabled&&(e+=50),i.xmp.enabled&&(e+=2e4),i.iptc.enabled&&(e+=14e3),i.icc.enabled&&(e+=6e3),e}const Me=i=>String.fromCharCode.apply(null,i),bt=typeof TextDecoder<"u"?new TextDecoder("utf-8"):void 0;class L{static from(e,t){return e instanceof this&&e.le===t?e:new L(e,void 0,void 0,t)}constructor(e,t=0,s,n){if(typeof n=="boolean"&&(this.le=n),Array.isArray(e)&&(e=new Uint8Array(e)),e===0)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){s===void 0&&(s=e.byteLength-t);let a=new DataView(e,t,s);this._swapDataView(a)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof L){s===void 0&&(s=e.byteLength-t),(t+=e.byteOffset)+s>e.byteOffset+e.byteLength&&k("Creating view outside of available memory in ArrayBuffer");let a=new DataView(e.buffer,t,s);this._swapDataView(a)}else if(typeof e=="number"){let a=new DataView(new ArrayBuffer(e));this._swapDataView(a)}else k("Invalid input argument for BufferView: "+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,s=L){return e instanceof DataView||e instanceof L?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||k("BufferView.set(): Invalid data argument."),this.toUint8().set(e,t),new s(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new L(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return n=this.getUint8Array(e,t),bt?bt.decode(n):Xt?Buffer.from(n).toString("utf8"):decodeURIComponent(escape(Me(n)));var n}getLatin1String(e=0,t=this.byteLength){let s=this.getUint8Array(e,t);return Me(s)}getUnicodeString(e=0,t=this.byteLength){const s=[];for(let n=0;n<t&&e+n<this.byteLength;n+=2)s.push(this.getUint16(e+n));return Me(s)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,s){switch(t){case 1:return this.getUint8(e,s);case 2:return this.getUint16(e,s);case 4:return this.getUint32(e,s);case 8:return this.getUint64&&this.getUint64(e,s)}}getUint(e,t,s){switch(t){case 8:return this.getUint8(e,s);case 16:return this.getUint16(e,s);case 32:return this.getUint32(e,s);case 64:return this.getUint64&&this.getUint64(e,s)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function Qe(i,e){k(`${i} '${e}' was not loaded, try using full build of exifr.`)}class st extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||Qe(this.kind,e),t&&(e in t||function(s,n){k(`Unknown ${s} '${n}'.`)}(this.kind,e),t[e].enabled||Qe(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var Qt=new st("file parser"),M=new st("segment parser"),nt=new st("file reader");let os=Gt.fetch;function vt(i,e){return(t=i).startsWith("data:")||t.length>1e4?Ze(i,e,"base64"):gt&&i.includes("://")?Je(i,e,"url",wt):gt?Ze(i,e,"fs"):ce?Je(i,e,"url",wt):void k("Invalid input argument");var t}async function Je(i,e,t,s){return nt.has(t)?Ze(i,e,t):s?async function(n,a){let d=await a(n);return new L(d)}(i,s):void k(`Parser ${t} is not loaded`)}async function Ze(i,e,t){let s=new(nt.get(t))(i,e);return await s.read(),s}const wt=i=>os(i).then(e=>e.arrayBuffer()),et=i=>new Promise((e,t)=>{let s=new FileReader;s.onloadend=()=>e(s.result||new ArrayBuffer),s.onerror=t,s.readAsArrayBuffer(i)}),at=new Map,ls=new Map,ds=new Map,ve=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],Jt=["jfif","xmp","icc","iptc","ihdr"],tt=["tiff",...Jt],D=["ifd0","ifd1","exif","gps","interop"],we=[...tt,...D],Fe=["makerNote","userComment"],Zt=["translateKeys","translateValues","reviveValues","multiSegment"],Pe=[...Zt,"sanitize","mergeOutput","silentErrors"];class ei{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class oe extends ei{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,s,n){if(super(),v(this,"enabled",!1),v(this,"skip",new Set),v(this,"pick",new Set),v(this,"deps",new Set),v(this,"translateKeys",!1),v(this,"translateValues",!1),v(this,"reviveValues",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=D.includes(e),this.canBeFiltered&&(this.dict=at.get(e)),s!==void 0)if(Array.isArray(s))this.parse=this.enabled=!0,this.canBeFiltered&&s.length>0&&this.translateTagSet(s,this.pick);else if(typeof s=="object"){if(this.enabled=!0,this.parse=s.parse!==!1,this.canBeFiltered){let{pick:a,skip:d}=s;a&&a.length>0&&this.translateTagSet(a,this.pick),d&&d.length>0&&this.translateTagSet(d,this.skip)}this.applyInheritables(s)}else s===!0||s===!1?this.parse=this.enabled=s:k(`Invalid options argument: ${s}`)}applyInheritables(e){let t,s;for(t of Zt)s=e[t],s!==void 0&&(this[t]=s)}translateTagSet(e,t){if(this.dict){let s,n,{tagKeys:a,tagValues:d}=this.dict;for(s of e)typeof s=="string"?(n=d.indexOf(s),n===-1&&(n=a.indexOf(Number(s))),n!==-1&&t.add(Number(a[n]))):t.add(s)}else for(let s of e)t.add(s)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,Ee(this.pick,this.deps)):this.enabled&&this.pick.size>0&&Ee(this.pick,this.deps)}}var O={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},Ft=new Map;class rt extends ei{static useCached(e){let t=Ft.get(e);return t!==void 0||(t=new this(e),Ft.set(e,t)),t}constructor(e){super(),e===!0?this.setupFromTrue():e===void 0?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):typeof e=="object"?this.setupFromObject(e):k(`Invalid options argument ${e}`),this.firstChunkSize===void 0&&(this.firstChunkSize=ce?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of ve)this[e]=O[e];for(e of Pe)this[e]=O[e];for(e of Fe)this[e]=O[e];for(e of we)this[e]=new oe(e,O[e],void 0,this)}setupFromTrue(){let e;for(e of ve)this[e]=O[e];for(e of Pe)this[e]=O[e];for(e of Fe)this[e]=!0;for(e of we)this[e]=new oe(e,!0,void 0,this)}setupFromArray(e){let t;for(t of ve)this[t]=O[t];for(t of Pe)this[t]=O[t];for(t of Fe)this[t]=O[t];for(t of we)this[t]=new oe(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,D)}setupFromObject(e){let t;for(t of(D.ifd0=D.ifd0||D.image,D.ifd1=D.ifd1||D.thumbnail,Object.assign(this,e),ve))this[t]=Le(e[t],O[t]);for(t of Pe)this[t]=Le(e[t],O[t]);for(t of Fe)this[t]=Le(e[t],O[t]);for(t of tt)this[t]=new oe(t,O[t],e[t],this);for(t of D)this[t]=new oe(t,O[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,D,we),e.tiff===!0?this.batchEnableWithBool(D,!0):e.tiff===!1?this.batchEnableWithUserValue(D,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,D):typeof e.tiff=="object"&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,D)}batchEnableWithBool(e,t){for(let s of e)this[s].enabled=t}batchEnableWithUserValue(e,t){for(let s of e){let n=t[s];this[s].enabled=n!==!1&&n!==void 0}}setupGlobalFilters(e,t,s,n=s){if(e&&e.length){for(let d of n)this[d].enabled=!1;let a=Pt(e,s);for(let[d,h]of a)Ee(this[d].pick,h),this[d].enabled=!0}else if(t&&t.length){let a=Pt(t,s);for(let[d,h]of a)Ee(this[d].skip,h)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:s,iptc:n,icc:a}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),s.enabled||e.skip.add(700),n.enabled||e.skip.add(33723),a.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:s,interop:n}=this;n.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),s.needed&&e.deps.add(34853),this.tiff.enabled=D.some(a=>this[a].enabled===!0)||this.makerNote||this.userComment;for(let a of D)this[a].finalizeFilters()}get onlyTiff(){return!Jt.map(e=>this[e].enabled).some(e=>e===!0)&&this.tiff.enabled}checkLoadedPlugins(){for(let e of tt)this[e].enabled&&!M.has(e)&&Qe("segment parser",e)}}function Pt(i,e){let t,s,n,a,d=[];for(n of e){for(a of(t=at.get(n),s=[],t))(i.includes(a[0])||i.includes(a[1]))&&s.push(a[0]);s.length&&d.push([n,s])}return d}function Le(i,e){return i!==void 0?i:e!==void 0?e:void 0}function Ee(i,e){for(let t of e)i.add(t)}v(rt,"default",O);class us{constructor(e){v(this,"parsers",{}),v(this,"output",{}),v(this,"errors",[]),v(this,"pushToErrors",t=>this.errors.push(t)),this.options=rt.useCached(e)}async read(e){this.file=await function(t,s){return typeof t=="string"?vt(t,s):ce&&!as&&t instanceof HTMLImageElement?vt(t.src,s):t instanceof Uint8Array||t instanceof ArrayBuffer||t instanceof DataView?new L(t):ce&&t instanceof Blob?Je(t,s,"blob",et):void k("Invalid input argument")}(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[s,n]of Qt)if(n.canHandle(e,t))return this.fileParser=new n(this.options,this.file,this.parsers),e[s]=!0;this.file.close&&this.file.close(),k("Unknown file format")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),Yt(s=e)?void 0:s;var s}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map(async s=>{let n=await s.parse();s.assignToOutput(e,n)});this.options.silentErrors&&(t=t.map(s=>s.catch(this.pushToErrors))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,s=M.get("tiff",e);var n;if(t.tiff?n={start:0,type:"tiff"}:t.jpeg&&(n=await this.fileParser.getOrFindSegment("tiff")),n===void 0)return;let a=await this.fileParser.ensureSegmentChunk(n),d=this.parsers.tiff=new s(a,e,t),h=await d.extractThumbnail();return t.close&&t.close(),h}}class ie{static findPosition(e,t){let s=e.getUint16(t+2)+2,n=typeof this.headerLength=="function"?this.headerLength(e,t,s):this.headerLength,a=t+n,d=s-n;return{offset:t,length:s,headerLength:n,start:a,size:d,end:a+d}}static parse(e,t={}){return new this(e,new rt({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof L?e:new L(e)}constructor(e,t={},s){v(this,"errors",[]),v(this,"raw",new Map),v(this,"handleError",n=>{if(!this.options.silentErrors)throw n;this.errors.push(n.message)}),this.chunk=this.normalizeInput(e),this.file=s,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let s=ds.get(t),n=ls.get(t),a=at.get(t),d=this.options[t],h=d.reviveValues&&!!s,o=d.translateValues&&!!n,l=d.translateKeys&&!!a,u={};for(let[c,p]of e)h&&s.has(c)?p=s.get(c)(p):o&&n.has(c)&&(p=this.translateValue(p,n.get(c))),l&&a.has(c)&&(c=a.get(c)||c),u[c]=p;return u}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,s){if(this.globalOptions.mergeOutput)return Object.assign(e,s);e[t]?Object.assign(e[t],s):e[t]=s}}v(ie,"headerLength",4),v(ie,"type",void 0),v(ie,"multiSegment",!1),v(ie,"canHandle",()=>!1);function hs(i){return i===192||i===194||i===196||i===219||i===221||i===218||i===254}function cs(i){return i>=224&&i<=239}function ps(i,e,t){for(let[s,n]of M)if(n.canHandle(i,e,t))return s}class Ct extends class{constructor(e,t,s){v(this,"errors",[]),v(this,"ensureSegmentChunk",async n=>{let a=n.start,d=n.size||65536;if(this.file.chunked)if(this.file.available(a,d))n.chunk=this.file.subarray(a,d);else try{n.chunk=await this.file.readChunk(a,d)}catch(h){k(`Couldn't read segment: ${JSON.stringify(n)}. ${h.message}`)}else this.file.byteLength>a+d?n.chunk=this.file.subarray(a,d):n.size===void 0?n.chunk=this.file.subarray(a):k("Segment unreachable: "+JSON.stringify(n));return n.chunk}),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=s}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let s=new(M.get(e))(t,this.options,this.file);return this.parsers[e]=s}createParsers(e){for(let t of e){let{type:s,chunk:n}=t,a=this.options[s];if(a&&a.enabled){let d=this.parsers[s];d&&d.append||d||this.createParser(s,n)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}{constructor(...e){super(...e),v(this,"appSegments",[]),v(this,"jpegSegments",[]),v(this,"unknownSegments",[])}static canHandle(e,t){return t===65496}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){e===!0?(this.findAll=!0,this.wanted=new Set(M.keyList())):(e=e===void 0?M.keyList().filter(t=>this.options[t].enabled):e.filter(t=>this.options[t].enabled&&M.has(t)),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:s,findAll:n,wanted:a,remaining:d}=this;if(!n&&this.file.chunked&&(n=Array.from(a).some(h=>{let o=M.get(h),l=this.options[h];return o.multiSegment&&l.multiSegment}),n&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,s.byteLength),!this.options.onlyTiff&&s.chunked){let h=!1;for(;d.size>0&&!h&&(s.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:o}=s,l=this.appSegments.some(u=>!this.file.available(u.offset||u.start,u.length||u.size));if(h=e>o&&!l?!await s.readNextChunk(e):!await s.readNextChunk(o),(e=this.findAppSegmentsInRange(e,s.byteLength))===void 0)return}}}findAppSegmentsInRange(e,t){t-=2;let s,n,a,d,h,o,{file:l,findAll:u,wanted:c,remaining:p,options:g}=this;for(;e<t;e++)if(l.getUint8(e)===255){if(s=l.getUint8(e+1),cs(s)){if(n=l.getUint16(e+2),a=ps(l,e,n),a&&c.has(a)&&(d=M.get(a),h=d.findPosition(l,e),o=g[a],h.type=a,this.appSegments.push(h),!u&&(d.multiSegment&&o.multiSegment?(this.unfinishedMultiSegment=h.chunkNumber<h.chunkCount,this.unfinishedMultiSegment||p.delete(a)):p.delete(a),p.size===0)))break;g.recordUnknownSegments&&(h=ie.findPosition(l,e),h.marker=s,this.unknownSegments.push(h)),e+=n+1}else if(hs(s)){if(n=l.getUint16(e+2),s===218&&g.stopAfterSos!==!1)return;g.recordJpegSegments&&this.jpegSegments.push({offset:e,length:n,marker:s}),e+=n+1}}return e}mergeMultiSegments(){if(!this.appSegments.some(t=>t.multiSegment))return;let e=function(t,s){let n,a,d,h=new Map;for(let o=0;o<t.length;o++)n=t[o],a=n[s],h.has(a)?d=h.get(a):h.set(a,d=[]),d.push(n);return Array.from(h)}(this.appSegments,"type");this.mergedAppSegments=e.map(([t,s])=>{let n=M.get(t,this.options);return n.handleMultiSegments?{type:t,chunk:n.handleMultiSegments(s)}:s[0]})}getSegment(e){return this.appSegments.find(t=>t.type===e)}async getOrFindSegment(e){let t=this.getSegment(e);return t===void 0&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}v(Ct,"type","jpeg"),Qt.set("jpeg",Ct);const fs=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class ms extends ie{parseHeader(){var e=this.chunk.getUint16();e===18761?this.le=!0:e===19789&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,s=new Map){let{pick:n,skip:a}=this.options[t];n=new Set(n);let d=n.size>0,h=a.size===0,o=this.chunk.getUint16(e);e+=2;for(let l=0;l<o;l++){let u=this.chunk.getUint16(e);if(d){if(n.has(u)&&(s.set(u,this.parseTag(e,u,t)),n.delete(u),n.size===0))break}else!h&&a.has(u)||s.set(u,this.parseTag(e,u,t));e+=12}return s}parseTag(e,t,s){let{chunk:n}=this,a=n.getUint16(e+2),d=n.getUint32(e+4),h=fs[a];if(h*d<=4?e+=8:e=n.getUint32(e+8),(a<1||a>13)&&k(`Invalid TIFF value type. block: ${s.toUpperCase()}, tag: ${t.toString(16)}, type: ${a}, offset ${e}`),e>n.byteLength&&k(`Invalid TIFF value offset. block: ${s.toUpperCase()}, tag: ${t.toString(16)}, type: ${a}, offset ${e} is outside of chunk size ${n.byteLength}`),a===1)return n.getUint8Array(e,d);if(a===2)return(o=function(l){for(;l.endsWith("\0");)l=l.slice(0,-1);return l}(o=n.getString(e,d)).trim())===""?void 0:o;var o;if(a===7)return n.getUint8Array(e,d);if(d===1)return this.parseTagValue(a,e);{let l=new(function(c){switch(c){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(a))(d),u=h;for(let c=0;c<d;c++)l[c]=this.parseTagValue(a,e),e+=u;return l}}parseTagValue(e,t){let{chunk:s}=this;switch(e){case 1:return s.getUint8(t);case 3:return s.getUint16(t);case 4:return s.getUint32(t);case 5:return s.getUint32(t)/s.getUint32(t+4);case 6:return s.getInt8(t);case 8:return s.getInt16(t);case 9:return s.getInt32(t);case 10:return s.getInt32(t)/s.getInt32(t+4);case 11:return s.getFloat(t);case 12:return s.getDouble(t);case 13:return s.getUint32(t);default:k(`Invalid tiff type ${e}`)}}}class xe extends ms{static canHandle(e,t){return e.getUint8(t+1)===225&&e.getUint32(t+4)===1165519206&&e.getUint16(t+8)===0}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse("parseExifBlock"),e.gps.enabled&&await this.safeParse("parseGpsBlock"),e.interop.enabled&&await this.safeParse("parseInteropBlock"),e.ifd1.enabled&&await this.safeParse("parseThumbnailBlock"),this.createOutput()}safeParse(e){let t=this[e]();return t.catch!==void 0&&(t=t.catch(this.handleError)),t}findIfd0Offset(){this.ifd0Offset===void 0&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(this.ifd1Offset===void 0){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let s=new Map;return this[t]=s,this.parseTags(e,t,s),s}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&k("Malformed EXIF data"),!e.chunked&&this.ifd0Offset>e.byteLength&&k(`IFD0 offset points to outside of file.
this.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,yt(this.options));let t=this.parseBlock(this.ifd0Offset,"ifd0");return t.size!==0?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif||(this.ifd0||await this.parseIfd0Block(),this.exifOffset===void 0))return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,yt(this.options));let e=this.parseBlock(this.exifOffset,"exif");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let s=e.get(t);s&&s.length===1&&e.set(t,s[0])}async parseGpsBlock(){if(this.gps||(this.ifd0||await this.parseIfd0Block(),this.gpsOffset===void 0))return;let e=this.parseBlock(this.gpsOffset,"gps");return e&&e.has(2)&&e.has(4)&&(e.set("latitude",St(...e.get(2),e.get(1))),e.set("longitude",St(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),this.interopOffset!==void 0||this.exif||await this.parseExifBlock(),this.interopOffset!==void 0))return this.parseBlock(this.interopOffset,"interop")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,"ifd1"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),this.ifd1===void 0)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,s,n={};for(t of D)if(e=this[t],!Yt(e))if(s=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if(t==="ifd1")continue;Object.assign(n,s)}else n[t]=s;return this.makerNote&&(n.makerNote=this.makerNote),this.userComment&&(n.userComment=this.userComment),n}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[s,n]of Object.entries(t))this.assignObjectToOutput(e,s,n)}}function St(i,e,t,s){var n=i+e/60+t/3600;return s!=="S"&&s!=="W"||(n*=-1),n}v(xe,"type","tiff"),v(xe,"headerLength",10),M.set("tiff",xe);const ot={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1};Object.assign({},ot,{firstChunkSize:4e4,gps:[1,2,3,4]});Object.assign({},ot,{tiff:!1,ifd1:!0,mergeOutput:!1});const gs=Object.assign({},ot,{firstChunkSize:4e4,ifd0:[274]});async function ys(i){let e=new us(gs);await e.read(i);let t=await e.parse();if(t&&t.ifd0)return t.ifd0[274]}const bs=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let de=!0,ue=!0;if(typeof navigator=="object"){let i=navigator.userAgent;if(i.includes("iPad")||i.includes("iPhone")){let e=i.match(/OS (\d+)_(\d+)/);if(e){let[,t,s]=e;de=Number(t)+.1*Number(s)<13.4,ue=!1}}else if(i.includes("OS X 10")){let[,e]=i.match(/OS X 10[_.](\d+)/);de=ue=Number(e)<15}if(i.includes("Chrome/")){let[,e]=i.match(/Chrome\/(\d+)/);de=ue=Number(e)<81}else if(i.includes("Firefox/")){let[,e]=i.match(/Firefox\/(\d+)/);de=ue=Number(e)<77}}async function vs(i){let e=await ys(i);return Object.assign({canvas:de,css:ue},bs[e])}class ws extends L{constructor(...e){super(...e),v(this,"ranges",new Fs),this.byteLength!==0&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,s){if(e===0&&this.byteLength===0&&s){let n=new DataView(s.buffer||s,s.byteOffset,s.byteLength);this._swapDataView(n)}else{let n=e+t;if(n>this.byteLength){let{dataView:a}=this._extend(n);this._swapDataView(a)}}}_extend(e){let t;t=Xt?Kt.allocUnsafe(e):new Uint8Array(e);let s=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:s}}subarray(e,t,s=!1){return t=t||this._lengthToEnd(e),s&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,s=!1){s&&this._tryExtend(t,e.byteLength,e);let n=super.set(e,t);return this.ranges.add(t,n.byteLength),n}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class Fs{constructor(){v(this,"list",[])}get length(){return this.list.length}add(e,t,s=0){let n=e+t,a=this.list.filter(d=>Tt(e,d.offset,n)||Tt(e,d.end,n));if(a.length>0){e=Math.min(e,...a.map(h=>h.offset)),n=Math.max(n,...a.map(h=>h.end)),t=n-e;let d=a.shift();d.offset=e,d.length=t,d.end=n,this.list=this.list.filter(h=>!a.includes(h))}else this.list.push({offset:e,length:t,end:n})}available(e,t){let s=e+t;return this.list.some(n=>n.offset<=e&&s<=n.end)}}function Tt(i,e,t){return i<=e&&e<=t}class Ps extends ws{constructor(e,t){super(0),v(this,"chunksRead",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,s=await this.readChunk(e,t);return!!s&&s.byteLength===t}async readChunk(e,t){if(this.chunksRead++,(t=this.safeWrapAddress(e,t))!==0)return this._readChunk(e,t)}safeWrapAddress(e,t){return this.size!==void 0&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(this.ranges.list.length!==0)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return this.size!==void 0&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}nt.set("blob",class extends Ps{async readWhole(){this.chunked=!1;let i=await et(this.input);this._swapArrayBuffer(i)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(i,e){let t=e?i+e:void 0,s=this.input.slice(i,t),n=await et(s);return this.set(n,i,!0)}});const Cs={strings:{generatingThumbnails:"Generating thumbnails..."}},Ss={version:"3.1.0"};function Ts(i,e,t){try{i.getContext("2d").getImageData(0,0,1,1)}catch(s){if(s.code===18)return Promise.reject(new Error("cannot read image, probably an svg with external resources"))}return i.toBlob?new Promise(s=>{i.toBlob(s,e,t)}).then(s=>{if(s===null)throw new Error("cannot read image, probably an svg with external resources");return s}):Promise.resolve().then(()=>ns(i.toDataURL(e,t),{})).then(s=>{if(s===null)throw new Error("could not extract blob, probably an old browser");return s})}function As(i,e){let t=i.width,s=i.height;(e.deg===90||e.deg===270)&&(t=i.height,s=i.width);const n=document.createElement("canvas");n.width=t,n.height=s;const a=n.getContext("2d");return a.translate(t/2,s/2),e.canvas&&(a.rotate(e.rad),a.scale(e.scaleX,e.scaleY)),a.drawImage(i,-i.width/2,-i.height/2,i.width,i.height),n}function Es(i){const e=i.width/i.height,t=5e6,s=4096;let n=Math.floor(Math.sqrt(t*e)),a=Math.floor(t/Math.sqrt(t*e));if(n>s&&(n=s,a=Math.round(n/e)),a>s&&(a=s,n=Math.round(e*a)),i.width>n){const d=document.createElement("canvas");return d.width=n,d.height=a,d.getContext("2d").drawImage(i,0,0,n,a),d}return i}const Ds={thumbnailWidth:null,thumbnailHeight:null,thumbnailType:"image/jpeg",waitForThumbnailsBeforeUpload:!1,lazy:!1};class ti extends De{constructor(e,t){if(super(e,{...Ds,...t}),this.onFileAdded=s=>{!s.preview&&s.data&&mt(s.type)&&!s.isRemote&&this.addToQueue(s.id)},this.onCancelRequest=s=>{const n=this.queue.indexOf(s.id);n!==-1&&this.queue.splice(n,1)},this.onFileRemoved=s=>{const n=this.queue.indexOf(s.id);n!==-1&&this.queue.splice(n,1),s.preview&&ft(s.preview)&&URL.revokeObjectURL(s.preview)},this.onRestored=()=>{this.uppy.getFiles().filter(n=>n.isRestored).forEach(n=>{(!n.preview||ft(n.preview))&&this.addToQueue(n.id)})},this.onAllFilesRemoved=()=>{this.queue=[]},this.waitUntilAllProcessed=s=>{s.forEach(a=>{const d=this.uppy.getFile(a);this.uppy.emit("preprocess-progress",d,{mode:"indeterminate",message:this.i18n("generatingThumbnails")})});const n=()=>{s.forEach(a=>{const d=this.uppy.getFile(a);this.uppy.emit("preprocess-complete",d)})};return new Promise(a=>{this.queueProcessing?this.uppy.once("thumbnail:all-generated",()=>{n(),a()}):(n(),a())})},this.type="modifier",this.id=this.opts.id||"ThumbnailGenerator",this.title="Thumbnail Generator",this.queue=[],this.queueProcessing=!1,this.defaultThumbnailDimension=200,this.thumbnailType=this.opts.thumbnailType,this.defaultLocale=Cs,this.i18nInit(),this.opts.lazy&&this.opts.waitForThumbnailsBeforeUpload)throw new Error("ThumbnailGenerator: The `lazy` and `waitForThumbnailsBeforeUpload` options are mutually exclusive. Please ensure at most one of them is set to `true`.")}createThumbnail(e,t,s){const n=URL.createObjectURL(e.data),a=new Promise((h,o)=>{const l=new Image;l.src=n,l.addEventListener("load",()=>{URL.revokeObjectURL(n),h(l)}),l.addEventListener("error",u=>{URL.revokeObjectURL(n),o(u.error||new Error("Could not create thumbnail"))})}),d=vs(e.data).catch(()=>1);return Promise.all([a,d]).then(h=>{let[o,l]=h;const u=this.getProportionalDimensions(o,t,s,l.deg),c=As(o,l),p=this.resizeImage(c,u.width,u.height);return Ts(p,this.thumbnailType,80)}).then(h=>URL.createObjectURL(h))}getProportionalDimensions(e,t,s,n){let a=e.width/e.height;return(n===90||n===270)&&(a=e.height/e.width),t!=null?{width:t,height:Math.round(t/a)}:s!=null?{width:Math.round(s*a),height:s}:{width:this.defaultThumbnailDimension,height:Math.round(this.defaultThumbnailDimension/a)}}resizeImage(e,t,s){let n=Es(e),a=Math.ceil(Math.log2(n.width/t));a<1&&(a=1);let d=t*2**(a-1),h=s*2**(a-1);const o=2;for(;a--;){const l=document.createElement("canvas");l.width=d,l.height=h,l.getContext("2d").drawImage(n,0,0,d,h),n=l,d=Math.round(d/o),h=Math.round(h/o)}return n}setPreviewURL(e,t){this.uppy.setFileState(e,{preview:t})}addToQueue(e){this.queue.push(e),this.queueProcessing===!1&&this.processQueue()}processQueue(){if(this.queueProcessing=!0,this.queue.length>0){const e=this.uppy.getFile(this.queue.shift());return e?this.requestThumbnail(e).catch(()=>{}).then(()=>this.processQueue()):(this.uppy.log("[ThumbnailGenerator] file was removed before a thumbnail could be generated, but not removed from the queue. This is probably a bug","error"),Promise.resolve())}return this.queueProcessing=!1,this.uppy.log("[ThumbnailGenerator] Emptied thumbnail queue"),this.uppy.emit("thumbnail:all-generated"),Promise.resolve()}requestThumbnail(e){return mt(e.type)&&!e.isRemote?this.createThumbnail(e,this.opts.thumbnailWidth,this.opts.thumbnailHeight).then(t=>{this.setPreviewURL(e.id,t),this.uppy.log(`[ThumbnailGenerator] Generated thumbnail for ${e.id}`),this.uppy.emit("thumbnail:generated",this.uppy.getFile(e.id),t)}).catch(t=>{this.uppy.log(`[ThumbnailGenerator] Failed thumbnail for ${e.id}:`,"warning"),this.uppy.log(t,"warning"),this.uppy.emit("thumbnail:error",this.uppy.getFile(e.id),t)}):Promise.resolve()}install(){this.uppy.on("file-removed",this.onFileRemoved),this.uppy.on("cancel-all",this.onAllFilesRemoved),this.opts.lazy?(this.uppy.on("thumbnail:request",this.onFileAdded),this.uppy.on("thumbnail:cancel",this.onCancelRequest)):(this.uppy.on("thumbnail:request",this.onFileAdded),this.uppy.on("file-added",this.onFileAdded),this.uppy.on("restored",this.onRestored)),this.opts.waitForThumbnailsBeforeUpload&&this.uppy.addPreProcessor(this.waitUntilAllProcessed)}uninstall(){this.uppy.off("file-removed",this.onFileRemoved),this.uppy.off("cancel-all",this.onAllFilesRemoved),this.opts.lazy?(this.uppy.off("thumbnail:request",this.onFileAdded),this.uppy.off("thumbnail:cancel",this.onCancelRequest)):(this.uppy.off("thumbnail:request",this.onFileAdded),this.uppy.off("file-added",this.onFileAdded),this.uppy.off("restored",this.onRestored)),this.opts.waitForThumbnailsBeforeUpload&&this.uppy.removePreProcessor(this.waitUntilAllProcessed)}}ti.VERSION=Ss.version;function At(i){if(typeof i=="string"){const e=document.querySelectorAll(i);return e.length===0?null:Array.from(e)}return typeof i=="object"&&Ti(i)?[i]:null}const he=Array.from;function ii(i,e,t,s){let{onSuccess:n}=s;i.readEntries(a=>{const d=[...e,...a];a.length?queueMicrotask(()=>{ii(i,d,t,{onSuccess:n})}):n(d)},a=>{t(a),n(e)})}function si(i,e){return i==null?i:{kind:i.isFile?"file":i.isDirectory?"directory":void 0,name:i.name,getFile(){return new Promise((t,s)=>i.file(t,s))},async*values(){const t=i.createReader();yield*await new Promise(n=>{ii(t,[],e,{onSuccess:a=>n(a.map(d=>si(d,e)))})})},isSameEntry:void 0}}function ni(i,e,t){try{return t===void 0&&(t=void 0),async function*(){const s=()=>`${e}/${i.name}`;if(i.kind==="file"){const n=await i.getFile();n!=null?(n.relativePath=e?s():null,yield n):t!=null&&(yield t)}else if(i.kind==="directory")for await(const n of i.values())yield*ni(n,e?s():i.name);else t!=null&&(yield t)}()}catch(s){return Promise.reject(s)}}async function*ks(i,e){const t=await Promise.all(Array.from(i.items,async s=>{var n;let a;const d=()=>typeof s.getAsEntry=="function"?s.getAsEntry():s.webkitGetAsEntry();return(n=a)!=null||(a=si(d(),e)),{fileSystemHandle:a,lastResortFile:s.getAsFile()}}));for(const{lastResortFile:s,fileSystemHandle:n}of t)if(n!=null)try{yield*ni(n,"",s)}catch(a){s!=null?yield s:e(a)}else s!=null&&(yield s)}function Os(i){const e=he(i.files);return Promise.resolve(e)}async function Bs(i,e){var t;const s=(t=e==null?void 0:e.logDropError)!=null?t:Function.prototype;try{const n=[];for await(const a of ks(i,s))n.push(a);return n}catch{return Os(i)}}var Is={exports:{}};(function(i){var e=Object.prototype.hasOwnProperty,t="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(t=!1));function n(o,l,u){this.fn=o,this.context=l,this.once=u||!1}function a(o,l,u,c,p){if(typeof u!="function")throw new TypeError("The listener must be a function");var g=new n(u,c||o,p),f=t?t+l:l;return o._events[f]?o._events[f].fn?o._events[f]=[o._events[f],g]:o._events[f].push(g):(o._events[f]=g,o._eventsCount++),o}function d(o,l){--o._eventsCount===0?o._events=new s:delete o._events[l]}function h(){this._events=new s,this._eventsCount=0}h.prototype.eventNames=function(){var l=[],u,c;if(this._eventsCount===0)return l;for(c in u=this._events)e.call(u,c)&&l.push(t?c.slice(1):c);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(u)):l},h.prototype.listeners=function(l){var u=t?t+l:l,c=this._events[u];if(!c)return[];if(c.fn)return[c.fn];for(var p=0,g=c.length,f=new Array(g);p<g;p++)f[p]=c[p].fn;return f},h.prototype.listenerCount=function(l){var u=t?t+l:l,c=this._events[u];return c?c.fn?1:c.length:0},h.prototype.emit=function(l,u,c,p,g,f){var y=t?t+l:l;if(!this._events[y])return!1;var m=this._events[y],w=arguments.length,C,b;if(m.fn){switch(m.once&&this.removeListener(l,m.fn,void 0,!0),w){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,u),!0;case 3:return m.fn.call(m.context,u,c),!0;case 4:return m.fn.call(m.context,u,c,p),!0;case 5:return m.fn.call(m.context,u,c,p,g),!0;case 6:return m.fn.call(m.context,u,c,p,g,f),!0}for(b=1,C=new Array(w-1);b<w;b++)C[b-1]=arguments[b];m.fn.apply(m.context,C)}else{var F=m.length,T;for(b=0;b<F;b++)switch(m[b].once&&this.removeListener(l,m[b].fn,void 0,!0),w){case 1:m[b].fn.call(m[b].context);break;case 2:m[b].fn.call(m[b].context,u);break;case 3:m[b].fn.call(m[b].context,u,c);break;case 4:m[b].fn.call(m[b].context,u,c,p);break;default:if(!C)for(T=1,C=new Array(w-1);T<w;T++)C[T-1]=arguments[T];m[b].fn.apply(m[b].context,C)}}return!0},h.prototype.on=function(l,u,c){return a(this,l,u,c,!1)},h.prototype.once=function(l,u,c){return a(this,l,u,c,!0)},h.prototype.removeListener=function(l,u,c,p){var g=t?t+l:l;if(!this._events[g])return this;if(!u)return d(this,g),this;var f=this._events[g];if(f.fn)f.fn===u&&(!p||f.once)&&(!c||f.context===c)&&d(this,g);else{for(var y=0,m=[],w=f.length;y<w;y++)(f[y].fn!==u||p&&!f[y].once||c&&f[y].context!==c)&&m.push(f[y]);m.length?this._events[g]=m.length===1?m[0]:m:d(this,g)}return this},h.prototype.removeAllListeners=function(l){var u;return l?(u=t?t+l:l,this._events[u]&&d(this,u)):(this._events=new s,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=t,h.EventEmitter=h,i.exports=h})(Is);globalThis&&globalThis.__classPrivateFieldGet;globalThis&&globalThis.__classPrivateFieldSet;globalThis&&globalThis.__classPrivateFieldGet;function it(){return it=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},it.apply(this,arguments)}const Us={position:"relative",width:"100%",minHeight:"100%"},Ns={position:"absolute",top:0,left:0,width:"100%",overflow:"visible"};class Rs extends pe{constructor(e){super(e),this.handleScroll=()=>{this.setState({offset:this.base.scrollTop})},this.handleResize=()=>{this.resize()},this.focusElement=null,this.state={offset:0,height:0}}componentDidMount(){this.resize(),window.addEventListener("resize",this.handleResize)}componentWillUpdate(){this.base.contains(document.activeElement)&&(this.focusElement=document.activeElement)}componentDidUpdate(){this.focusElement&&this.focusElement.parentNode&&document.activeElement!==this.focusElement&&this.focusElement.focus(),this.focusElement=null,this.resize()}componentWillUnmount(){window.removeEventListener("resize",this.handleResize)}resize(){const{height:e}=this.state;e!==this.base.offsetHeight&&this.setState({height:this.base.offsetHeight})}render(e){let{data:t,rowHeight:s,renderRow:n,overscanCount:a=10,...d}=e;const{offset:h,height:o}=this.state;let l=Math.floor(h/s),u=Math.floor(o/s);a&&(l=Math.max(0,l-l%a),u+=a);const c=l+u+4,p=t.slice(l,c),g={...Us,height:t.length*s},f={...Ns,top:l*s};return r("div",it({onScroll:this.handleScroll},d),r("div",{role:"presentation",style:g},r("div",{role:"presentation",style:f},p.map(n))))}}function Ms(){return r("svg",{"aria-hidden":"true",focusable:"false",width:"30",height:"30",viewBox:"0 0 30 30"},r("path",{d:"M15 30c8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15C6.716 0 0 6.716 0 15c0 8.284 6.716 15 15 15zm4.258-12.676v6.846h-8.426v-6.846H5.204l9.82-12.364 9.82 12.364H19.26z"}))}var Et=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function Ls(i,e){return!!(i===e||Et(i)&&Et(e))}function xs(i,e){if(i.length!==e.length)return!1;for(var t=0;t<i.length;t++)if(!Ls(i[t],e[t]))return!1;return!0}function Dt(i,e){e===void 0&&(e=xs);var t=null;function s(){for(var n=[],a=0;a<arguments.length;a++)n[a]=arguments[a];if(t&&t.lastThis===this&&e(n,t.lastArgs))return t.lastResult;var d=i.apply(this,n);return t={lastResult:d,lastArgs:n,lastThis:this},d}return s.clear=function(){t=null},s}const ai=['a[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','area[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])',"input:not([disabled]):not([inert]):not([aria-hidden])","select:not([disabled]):not([inert]):not([aria-hidden])","textarea:not([disabled]):not([inert]):not([aria-hidden])","button:not([disabled]):not([inert]):not([aria-hidden])",'iframe:not([tabindex^="-"]):not([inert]):not([aria-hidden])','object:not([tabindex^="-"]):not([inert]):not([aria-hidden])','embed:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[contenteditable]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[tabindex]:not([tabindex^="-"]):not([inert]):not([aria-hidden])'];function ri(i,e){if(e){const t=i.querySelector(`[data-uppy-paneltype="${e}"]`);if(t)return t}return i}function kt(i,e){const t=e[0];t&&(t.focus(),i.preventDefault())}function _s(i,e){const t=e[e.length-1];t&&(t.focus(),i.preventDefault())}function zs(i){return i.contains(document.activeElement)}function oi(i,e,t){const s=ri(t,e),n=he(s.querySelectorAll(ai)),a=n.indexOf(document.activeElement);zs(s)?i.shiftKey&&a===0?_s(i,n):!i.shiftKey&&a===n.length-1&&kt(i,n):kt(i,n)}function $s(i,e,t){e===null||oi(i,e,t)}function Hs(){let i=!1;return Ai((t,s)=>{const n=ri(t,s),a=n.contains(document.activeElement);if(a&&i)return;const d=n.querySelector("[data-uppy-super-focusable]");if(!(a&&!d))if(d)d.focus({preventScroll:!0}),i=!0;else{const h=n.querySelector(ai);h==null||h.focus({preventScroll:!0}),i=!1}},260)}function Vs(){const i=document.body;return!(!("draggable"in i)||!("ondragstart"in i&&"ondrop"in i)||!("FormData"in window)||!("FileReader"in window))}var qs=function(e,t){if(e===t)return!0;for(var s in e)if(!(s in t))return!1;for(var s in t)if(e[s]!==t[s])return!1;return!0};const js=xt(qs);function Ws(){return r("svg",{"aria-hidden":"true",focusable:"false",width:"25",height:"25",viewBox:"0 0 25 25"},r("g",{fill:"#686DE0",fillRule:"evenodd"},r("path",{d:"M5 7v10h15V7H5zm0-1h15a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1z",fillRule:"nonzero"}),r("path",{d:"M6.35 17.172l4.994-5.026a.5.5 0 0 1 .707 0l2.16 2.16 3.505-3.505a.5.5 0 0 1 .707 0l2.336 2.31-.707.72-1.983-1.97-3.505 3.505a.5.5 0 0 1-.707 0l-2.16-2.159-3.938 3.939-1.409.026z",fillRule:"nonzero"}),r("circle",{cx:"7.5",cy:"9.5",r:"1.5"})))}function Gs(){return r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},r("path",{d:"M9.5 18.64c0 1.14-1.145 2-2.5 2s-2.5-.86-2.5-2c0-1.14 1.145-2 2.5-2 .557 0 1.079.145 1.5.396V7.25a.5.5 0 0 1 .379-.485l9-2.25A.5.5 0 0 1 18.5 5v11.64c0 1.14-1.145 2-2.5 2s-2.5-.86-2.5-2c0-1.14 1.145-2 2.5-2 .557 0 1.079.145 1.5.396V8.67l-8 2v7.97zm8-11v-2l-8 2v2l8-2zM7 19.64c.855 0 1.5-.484 1.5-1s-.645-1-1.5-1-1.5.484-1.5 1 .645 1 1.5 1zm9-2c.855 0 1.5-.484 1.5-1s-.645-1-1.5-1-1.5.484-1.5 1 .645 1 1.5 1z",fill:"#049BCF",fillRule:"nonzero"}))}function Ks(){return r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},r("path",{d:"M16 11.834l4.486-2.691A1 1 0 0 1 22 10v6a1 1 0 0 1-1.514.857L16 14.167V17a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v2.834zM15 9H5v8h10V9zm1 4l5 3v-6l-5 3z",fill:"#19AF67",fillRule:"nonzero"}))}function Xs(){return r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},r("path",{d:"M9.766 8.295c-.691-1.843-.539-3.401.747-3.726 1.643-.414 2.505.938 2.39 3.299-.039.79-.194 1.662-.537 3.148.324.49.66.967 1.055 1.51.17.231.382.488.629.757 1.866-.128 3.653.114 4.918.655 1.487.635 2.192 1.685 1.614 2.84-.566 1.133-1.839 1.084-3.416.249-1.141-.604-2.457-1.634-3.51-2.707a13.467 13.467 0 0 0-2.238.426c-1.392 4.051-4.534 6.453-5.707 4.572-.986-1.58 1.38-4.206 4.914-5.375.097-.322.185-.656.264-1.001.08-.353.306-1.31.407-1.737-.678-1.059-1.2-2.031-1.53-2.91zm2.098 4.87c-.033.144-.068.287-.104.427l.033-.01-.012.038a14.065 14.065 0 0 1 1.02-.197l-.032-.033.052-.004a7.902 7.902 0 0 1-.208-.271c-.197-.27-.38-.526-.555-.775l-.006.028-.002-.003c-.076.323-.148.632-.186.8zm5.77 2.978c1.143.605 1.832.632 2.054.187.26-.519-.087-1.034-1.113-1.473-.911-.39-2.175-.608-3.55-.608.845.766 1.787 1.459 2.609 1.894zM6.559 18.789c.14.223.693.16 1.425-.413.827-.648 1.61-1.747 2.208-3.206-2.563 1.064-4.102 2.867-3.633 3.62zm5.345-10.97c.088-1.793-.351-2.48-1.146-2.28-.473.119-.564 1.05-.056 2.405.213.566.52 1.188.908 1.859.18-.858.268-1.453.294-1.984z",fill:"#E2514A",fillRule:"nonzero"}))}function Ys(){return r("svg",{"aria-hidden":"true",focusable:"false",width:"25",height:"25",viewBox:"0 0 25 25"},r("path",{d:"M10.45 2.05h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5V2.55a.5.5 0 0 1 .5-.5zm2.05 1.024h1.05a.5.5 0 0 1 .5.5V3.6a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5v-.001zM10.45 0h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5V.5a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-2.05 3.074h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-2.05 1.024h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm-2.05 1.025h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-1.656 3.074l-.82 5.946c.52.302 1.174.458 1.976.458.803 0 1.455-.156 1.975-.458l-.82-5.946h-2.311zm0-1.025h2.312c.512 0 .946.378 1.015.885l.82 5.946c.056.412-.142.817-.501 1.026-.686.398-1.515.597-2.49.597-.974 0-1.804-.199-2.49-.597a1.025 1.025 0 0 1-.5-1.026l.819-5.946c.07-.507.503-.885 1.015-.885zm.545 6.6a.5.5 0 0 1-.397-.561l.143-.999a.5.5 0 0 1 .495-.429h.74a.5.5 0 0 1 .495.43l.143.998a.5.5 0 0 1-.397.561c-.404.08-.819.08-1.222 0z",fill:"#00C469",fillRule:"nonzero"}))}function Qs(){return r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},r("g",{fill:"#A7AFB7",fillRule:"nonzero"},r("path",{d:"M5.5 22a.5.5 0 0 1-.5-.5v-18a.5.5 0 0 1 .5-.5h10.719a.5.5 0 0 1 .367.16l3.281 3.556a.5.5 0 0 1 .133.339V21.5a.5.5 0 0 1-.5.5h-14zm.5-1h13V7.25L16 4H6v17z"}),r("path",{d:"M15 4v3a1 1 0 0 0 1 1h3V7h-3V4h-1z"})))}function Js(){return r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},r("path",{d:"M4.5 7h13a.5.5 0 1 1 0 1h-13a.5.5 0 0 1 0-1zm0 3h15a.5.5 0 1 1 0 1h-15a.5.5 0 1 1 0-1zm0 3h15a.5.5 0 1 1 0 1h-15a.5.5 0 1 1 0-1zm0 3h10a.5.5 0 1 1 0 1h-10a.5.5 0 1 1 0-1z",fill:"#5A5E69",fillRule:"nonzero"}))}function lt(i){const e={color:"#838999",icon:Qs()};if(!i)return e;const t=i.split("/")[0],s=i.split("/")[1];return t==="text"?{color:"#5a5e69",icon:Js()}:t==="image"?{color:"#686de0",icon:Ws()}:t==="audio"?{color:"#068dbb",icon:Gs()}:t==="video"?{color:"#19af67",icon:Ks()}:t==="application"&&s==="pdf"?{color:"#e25149",icon:Xs()}:t==="application"&&["zip","x-7z-compressed","x-zip-compressed","x-rar-compressed","x-tar","x-gzip","x-apple-diskimage"].indexOf(s)!==-1?{color:"#00C469",icon:Ys()}:e}function li(i){const{file:e}=i;if(e.preview)return r("img",{className:"uppy-Dashboard-Item-previewImg",alt:e.name,src:e.preview});const{color:t,icon:s}=lt(e.type);return r("div",{className:"uppy-Dashboard-Item-previewIconWrap"},r("span",{className:"uppy-Dashboard-Item-previewIcon",style:{color:t}},s),r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-Dashboard-Item-previewIconBg",width:"58",height:"76",viewBox:"0 0 58 76"},r("rect",{fill:"#FFF",width:"58",height:"76",rx:"3",fillRule:"evenodd"})))}const Zs=(i,e)=>(typeof e=="function"?e():e).filter(n=>n.id===i)[0].name;function di(i){const{file:e,toggleFileCard:t,i18n:s,metaFields:n}=i,{missingRequiredMetaFields:a}=e;if(!(a!=null&&a.length))return null;const d=a.map(h=>Zs(h,n)).join(", ");return r("div",{className:"uppy-Dashboard-Item-errorMessage"},s("missingRequiredMetaFields",{smart_count:a.length,fields:d})," ",r("button",{type:"button",class:"uppy-u-reset uppy-Dashboard-Item-errorMessageBtn",onClick:()=>t(!0,e.id)},s("editFile")))}function en(i){const{file:e,i18n:t,toggleFileCard:s,metaFields:n,showLinkToFileUploadResult:a}=i,d="rgba(255, 255, 255, 0.5)",h=e.preview?d:lt(e.type).color;return r("div",{className:"uppy-Dashboard-Item-previewInnerWrap",style:{backgroundColor:h}},a&&e.uploadURL&&r("a",{className:"uppy-Dashboard-Item-previewLink",href:e.uploadURL,rel:"noreferrer noopener",target:"_blank","aria-label":e.meta.name},r("span",{hidden:!0},e.meta.name)),r(li,{file:e}),r(di,{file:e,i18n:t,toggleFileCard:s,metaFields:n}))}function tn(i){if(!i.isUploaded){if(i.error&&!i.hideRetryButton){i.uppy.retryUpload(i.file.id);return}i.resumableUploads&&!i.hidePauseResumeButton?i.uppy.pauseResume(i.file.id):i.individualCancellation&&!i.hideCancelButton&&i.uppy.removeFile(i.file.id)}}function Ot(i){return i.isUploaded?i.i18n("uploadComplete"):i.error?i.i18n("retryUpload"):i.resumableUploads?i.file.isPaused?i.i18n("resumeUpload"):i.i18n("pauseUpload"):i.individualCancellation?i.i18n("cancelUpload"):""}function _e(i){return r("div",{className:"uppy-Dashboard-Item-progress"},r("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-progressIndicator",type:"button","aria-label":Ot(i),title:Ot(i),onClick:()=>tn(i)},i.children))}function Ce(i){let{children:e}=i;return r("svg",{"aria-hidden":"true",focusable:"false",width:"70",height:"70",viewBox:"0 0 36 36",className:"uppy-c-icon uppy-Dashboard-Item-progressIcon--circle"},e)}function ze(i){let{progress:e}=i;const t=2*Math.PI*15;return r("g",null,r("circle",{className:"uppy-Dashboard-Item-progressIcon--bg",r:"15",cx:"18",cy:"18","stroke-width":"2",fill:"none"}),r("circle",{className:"uppy-Dashboard-Item-progressIcon--progress",r:"15",cx:"18",cy:"18",transform:"rotate(-90, 18, 18)",fill:"none","stroke-width":"2","stroke-dasharray":t,"stroke-dashoffset":t-t/100*e}))}function sn(i){if(!i.file.progress.uploadStarted)return null;if(i.isUploaded)return r("div",{className:"uppy-Dashboard-Item-progress"},r("div",{className:"uppy-Dashboard-Item-progressIndicator"},r(Ce,null,r("circle",{r:"15",cx:"18",cy:"18",fill:"#1bb240"}),r("polygon",{className:"uppy-Dashboard-Item-progressIcon--check",transform:"translate(2, 3)",points:"14 22.5 7 15.2457065 8.99985857 13.1732815 14 18.3547104 22.9729883 9 25 11.1005634"}))));if(!i.recoveredState)return i.error&&!i.hideRetryButton?r(_e,i,r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-Dashboard-Item-progressIcon--retry",width:"28",height:"31",viewBox:"0 0 16 19"},r("path",{d:"M16 11a8 8 0 1 1-8-8v2a6 6 0 1 0 6 6h2z"}),r("path",{d:"M7.9 3H10v2H7.9z"}),r("path",{d:"M8.536.5l3.535 3.536-1.414 1.414L7.12 1.914z"}),r("path",{d:"M10.657 2.621l1.414 1.415L8.536 7.57 7.12 6.157z"}))):i.resumableUploads&&!i.hidePauseResumeButton?r(_e,i,r(Ce,null,r(ze,{progress:i.file.progress.percentage}),i.file.isPaused?r("polygon",{className:"uppy-Dashboard-Item-progressIcon--play",transform:"translate(3, 3)",points:"12 20 12 10 20 15"}):r("g",{className:"uppy-Dashboard-Item-progressIcon--pause",transform:"translate(14.5, 13)"},r("rect",{x:"0",y:"0",width:"2",height:"10",rx:"0"}),r("rect",{x:"5",y:"0",width:"2",height:"10",rx:"0"})))):!i.resumableUploads&&i.individualCancellation&&!i.hideCancelButton?r(_e,i,r(Ce,null,r(ze,{progress:i.file.progress.percentage}),r("polygon",{className:"cancel",transform:"translate(2, 2)",points:"19.8856516 11.0625 16 14.9481516 12.1019737 11.0625 11.0625 12.1143484 14.9481516 16 11.0625 19.8980263 12.1019737 20.9375 16 17.0518484 19.8856516 20.9375 20.9375 19.8980263 17.0518484 16 20.9375 12"}))):r("div",{className:"uppy-Dashboard-Item-progress"},r("div",{className:"uppy-Dashboard-Item-progressIndicator"},r(Ce,null,r(ze,{progress:i.file.progress.percentage}))))}const $e="...";function ui(i,e){if(e===0)return"";if(i.length<=e)return i;if(e<=$e.length+1)return`${i.slice(0,e-1)}…`;const t=e-$e.length,s=Math.ceil(t/2),n=Math.floor(t/2);return i.slice(0,s)+$e+i.slice(-n)}const nn=i=>{const{author:e,name:t}=i.file.meta;function s(){return i.isSingleFile&&i.containerHeight>=350?90:i.containerWidth<=352?35:i.containerWidth<=576?60:e?20:30}return r("div",{className:"uppy-Dashboard-Item-name",title:t},ui(t,s()))},an=i=>{var e;const{author:t}=i.file.meta,s=(e=i.file.remote)==null?void 0:e.providerName,n="·";return t?r("div",{className:"uppy-Dashboard-Item-author"},r("a",{href:`${t.url}?utm_source=Companion&utm_medium=referral`,target:"_blank",rel:"noopener noreferrer"},ui(t.name,13)),s?r(Te,null,` ${n} `,s,` ${n} `):null):null},rn=i=>i.file.size&&r("div",{className:"uppy-Dashboard-Item-statusSize"},Ye(i.file.size)),on=i=>i.file.isGhost&&r("span",null," • ",r("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-reSelect",type:"button",onClick:i.toggleAddFilesPanel},i.i18n("reSelect"))),ln=i=>{let{file:e,onClick:t}=i;return e.error?r("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-errorDetails","aria-label":e.error,"data-microtip-position":"bottom","data-microtip-size":"medium",onClick:t,type:"button"},"?"):null};function dn(i){const{file:e}=i;return r("div",{className:"uppy-Dashboard-Item-fileInfo","data-uppy-file-source":e.source},r("div",{className:"uppy-Dashboard-Item-fileName"},nn(i),r(ln,{file:i.file,onClick:()=>alert(i.file.error)})),r("div",{className:"uppy-Dashboard-Item-status"},an(i),rn(i),on(i)),r(di,{file:i.file,i18n:i.i18n,toggleFileCard:i.toggleFileCard,metaFields:i.metaFields}))}function un(i,e){return e===void 0&&(e="Copy the URL below"),new Promise(t=>{const s=document.createElement("textarea");s.setAttribute("style",{position:"fixed",top:0,left:0,width:"2em",height:"2em",padding:0,border:"none",outline:"none",boxShadow:"none",background:"transparent"}),s.value=i,document.body.appendChild(s),s.select();const n=a=>{document.body.removeChild(s),window.prompt(e,i),t()};try{return document.execCommand("copy")?(document.body.removeChild(s),t()):n("copy command unavailable")}catch{return document.body.removeChild(s),n()}})}function hn(i){let{file:e,uploadInProgressOrComplete:t,metaFields:s,canEditFile:n,i18n:a,onClick:d}=i;return!t&&s&&s.length>0||!t&&n(e)?r("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-action uppy-Dashboard-Item-action--edit",type:"button","aria-label":a("editFileWithFilename",{file:e.meta.name}),title:a("editFileWithFilename",{file:e.meta.name}),onClick:()=>d()},r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"14",height:"14",viewBox:"0 0 14 14"},r("g",{fillRule:"evenodd"},r("path",{d:"M1.5 10.793h2.793A1 1 0 0 0 5 10.5L11.5 4a1 1 0 0 0 0-1.414L9.707.793a1 1 0 0 0-1.414 0l-6.5 6.5A1 1 0 0 0 1.5 8v2.793zm1-1V8L9 1.5l1.793 1.793-6.5 6.5H2.5z",fillRule:"nonzero"}),r("rect",{x:"1",y:"12.293",width:"11",height:"1",rx:".5"}),r("path",{fillRule:"nonzero",d:"M6.793 2.5L9.5 5.207l.707-.707L7.5 1.793z"})))):null}function cn(i){let{i18n:e,onClick:t,file:s}=i;return r("button",{className:"uppy-u-reset uppy-Dashboard-Item-action uppy-Dashboard-Item-action--remove",type:"button","aria-label":e("removeFile",{file:s.meta.name}),title:e("removeFile",{file:s.meta.name}),onClick:()=>t()},r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"18",height:"18",viewBox:"0 0 18 18"},r("path",{d:"M9 0C4.034 0 0 4.034 0 9s4.034 9 9 9 9-4.034 9-9-4.034-9-9-9z"}),r("path",{fill:"#FFF",d:"M13 12.222l-.778.778L9 9.778 5.778 13 5 12.222 8.222 9 5 5.778 5.778 5 9 8.222 12.222 5l.778.778L9.778 9z"})))}const pn=(i,e)=>{un(e.file.uploadURL,e.i18n("copyLinkToClipboardFallback")).then(()=>{e.uppy.log("Link copied to clipboard."),e.uppy.info(e.i18n("copyLinkToClipboardSuccess"),"info",3e3)}).catch(e.uppy.log).then(()=>i.target.focus({preventScroll:!0}))};function fn(i){const{i18n:e}=i;return r("button",{className:"uppy-u-reset uppy-Dashboard-Item-action uppy-Dashboard-Item-action--copyLink",type:"button","aria-label":e("copyLink"),title:e("copyLink"),onClick:t=>pn(t,i)},r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"14",height:"14",viewBox:"0 0 14 12"},r("path",{d:"M7.94 7.703a2.613 2.613 0 0 1-.626 2.681l-.852.851a2.597 2.597 0 0 1-1.849.766A2.616 2.616 0 0 1 2.764 7.54l.852-.852a2.596 2.596 0 0 1 2.69-.625L5.267 7.099a1.44 1.44 0 0 0-.833.407l-.852.851a1.458 1.458 0 0 0 1.03 2.486c.39 0 .755-.152 1.03-.426l.852-.852c.231-.231.363-.522.406-.824l1.04-1.038zm4.295-5.937A2.596 2.596 0 0 0 10.387 1c-.698 0-1.355.272-1.849.766l-.852.851a2.614 2.614 0 0 0-.624 2.688l1.036-1.036c.041-.304.173-.6.407-.833l.852-.852c.275-.275.64-.426 1.03-.426a1.458 1.458 0 0 1 1.03 2.486l-.852.851a1.442 1.442 0 0 1-.824.406l-1.04 1.04a2.596 2.596 0 0 0 2.683-.628l.851-.85a2.616 2.616 0 0 0 0-3.697zm-6.88 6.883a.577.577 0 0 0 .82 0l3.474-3.474a.579.579 0 1 0-.819-.82L5.355 7.83a.579.579 0 0 0 0 .819z"})))}function mn(i){const{uppy:e,file:t,uploadInProgressOrComplete:s,canEditFile:n,metaFields:a,showLinkToFileUploadResult:d,showRemoveButton:h,i18n:o,toggleFileCard:l,openFileEditor:u}=i;return r("div",{className:"uppy-Dashboard-Item-actionWrapper"},r(hn,{i18n:o,file:t,uploadInProgressOrComplete:s,canEditFile:n,metaFields:a,onClick:()=>{a&&a.length>0?l(!0,t.id):u(t)}}),d&&t.uploadURL?r(fn,{file:t,uppy:e,i18n:o}):null,h?r(cn,{i18n:o,file:t,uppy:e,onClick:()=>e.removeFile(t.id,"removed-by-user")}):null)}class gn extends pe{componentDidMount(){const{file:e}=this.props;e.preview||this.props.handleRequestThumbnail(e)}shouldComponentUpdate(e){return!js(this.props,e)}componentDidUpdate(){const{file:e}=this.props;e.preview||this.props.handleRequestThumbnail(e)}componentWillUnmount(){const{file:e}=this.props;e.preview||this.props.handleCancelThumbnail(e)}render(){const{file:e}=this.props,t=e.progress.preprocess||e.progress.postprocess,s=e.progress.uploadComplete&&!t&&!e.error,n=e.progress.uploadStarted||t,a=e.progress.uploadStarted&&!e.progress.uploadComplete||t,d=e.error||!1,{isGhost:h}=e;let o=(this.props.individualCancellation||!a)&&!s;s&&this.props.showRemoveButtonAfterComplete&&(o=!0);const l=x({"uppy-Dashboard-Item":!0,"is-inprogress":a&&!this.props.recoveredState,"is-processing":t,"is-complete":s,"is-error":!!d,"is-resumable":this.props.resumableUploads,"is-noIndividualCancellation":!this.props.individualCancellation,"is-ghost":h});return r("div",{className:l,id:`uppy_${e.id}`,role:this.props.role},r("div",{className:"uppy-Dashboard-Item-preview"},r(en,{file:e,showLinkToFileUploadResult:this.props.showLinkToFileUploadResult,i18n:this.props.i18n,toggleFileCard:this.props.toggleFileCard,metaFields:this.props.metaFields}),r(sn,{uppy:this.props.uppy,file:e,error:d,isUploaded:s,hideRetryButton:this.props.hideRetryButton,hideCancelButton:this.props.hideCancelButton,hidePauseResumeButton:this.props.hidePauseResumeButton,recoveredState:this.props.recoveredState,showRemoveButtonAfterComplete:this.props.showRemoveButtonAfterComplete,resumableUploads:this.props.resumableUploads,individualCancellation:this.props.individualCancellation,i18n:this.props.i18n})),r("div",{className:"uppy-Dashboard-Item-fileInfoAndButtons"},r(dn,{file:e,id:this.props.id,acquirers:this.props.acquirers,containerWidth:this.props.containerWidth,containerHeight:this.props.containerHeight,i18n:this.props.i18n,toggleAddFilesPanel:this.props.toggleAddFilesPanel,toggleFileCard:this.props.toggleFileCard,metaFields:this.props.metaFields,isSingleFile:this.props.isSingleFile}),r(mn,{file:e,metaFields:this.props.metaFields,showLinkToFileUploadResult:this.props.showLinkToFileUploadResult,showRemoveButton:o,canEditFile:this.props.canEditFile,uploadInProgressOrComplete:n,toggleFileCard:this.props.toggleFileCard,openFileEditor:this.props.openFileEditor,uppy:this.props.uppy,i18n:this.props.i18n})))}}function yn(i,e){const t=[];let s=[];return i.forEach(n=>{s.length<e?s.push(n):(t.push(s),s=[n])}),s.length&&t.push(s),t}function bn(i){let{id:e,error:t,i18n:s,uppy:n,files:a,acquirers:d,resumableUploads:h,hideRetryButton:o,hidePauseResumeButton:l,hideCancelButton:u,showLinkToFileUploadResult:c,showRemoveButtonAfterComplete:p,isWide:g,metaFields:f,isSingleFile:y,toggleFileCard:m,handleRequestThumbnail:w,handleCancelThumbnail:C,recoveredState:b,individualCancellation:F,itemsPerRow:T,openFileEditor:I,canEditFile:A,toggleAddFilesPanel:E,containerWidth:N,containerHeight:Z}=i;const W=T===1?71:200,fe=Pi(()=>{const ee=(ke,ne)=>a[ne].isGhost-a[ke].isGhost,G=Object.keys(a);return b&&G.sort(ee),yn(G,T)},[a,T,b]),me=ee=>r("div",{class:"uppy-Dashboard-filesInner",role:"presentation",key:ee[0]},ee.map(G=>r(gn,{key:G,uppy:n,id:e,error:t,i18n:s,acquirers:d,resumableUploads:h,individualCancellation:F,hideRetryButton:o,hidePauseResumeButton:l,hideCancelButton:u,showLinkToFileUploadResult:c,showRemoveButtonAfterComplete:p,isWide:g,metaFields:f,recoveredState:b,isSingleFile:y,containerWidth:N,containerHeight:Z,toggleFileCard:m,handleRequestThumbnail:w,handleCancelThumbnail:C,role:"listitem",openFileEditor:I,canEditFile:A,toggleAddFilesPanel:E,file:a[G]})));return y?r("div",{class:"uppy-Dashboard-files"},me(fe[0])):r(Rs,{class:"uppy-Dashboard-files",role:"list",data:fe,renderRow:me,rowHeight:W})}let hi;hi=Symbol.for("uppy test: disable unused locale key warning");class ci extends pe{constructor(){super(...arguments),this.triggerFileInputClick=()=>{this.fileInput.click()},this.triggerFolderInputClick=()=>{this.folderInput.click()},this.triggerVideoCameraInputClick=()=>{this.mobileVideoFileInput.click()},this.triggerPhotoCameraInputClick=()=>{this.mobilePhotoFileInput.click()},this.onFileInputChange=e=>{this.props.handleInputChange(e),e.target.value=null},this.renderHiddenInput=(e,t)=>r("input",{className:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabIndex:-1,webkitdirectory:e,type:"file",name:"files[]",multiple:this.props.maxNumberOfFiles!==1,onChange:this.onFileInputChange,accept:this.props.allowedFileTypes,ref:t}),this.renderHiddenCameraInput=(e,t,s)=>{const a={photo:"image/*",video:"video/*"}[e];return r("input",{className:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabIndex:-1,type:"file",name:`camera-${e}`,onChange:this.onFileInputChange,capture:t,accept:a,ref:s})},this.renderMyDeviceAcquirer=()=>r("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MyDevice"},r("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerFileInputClick},r("div",{className:"uppy-DashboardTab-inner"},r("svg",{className:"uppy-DashboardTab-iconMyDevice","aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},r("path",{d:"M8.45 22.087l-1.305-6.674h17.678l-1.572 6.674H8.45zm4.975-12.412l1.083 1.765a.823.823 0 00.715.386h7.951V13.5H8.587V9.675h4.838zM26.043 13.5h-1.195v-2.598c0-.463-.336-.75-.798-.75h-8.356l-1.082-1.766A.823.823 0 0013.897 8H7.728c-.462 0-.815.256-.815.718V13.5h-.956a.97.97 0 00-.746.37.972.972 0 00-.19.81l1.724 8.565c.095.44.484.755.933.755H24c.44 0 .824-.3.929-.727l2.043-8.568a.972.972 0 00-.176-.825.967.967 0 00-.753-.38z",fill:"currentcolor","fill-rule":"evenodd"}))),r("div",{className:"uppy-DashboardTab-name"},this.props.i18n("myDevice")))),this.renderPhotoCamera=()=>r("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MobilePhotoCamera"},r("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerPhotoCameraInputClick},r("div",{className:"uppy-DashboardTab-inner"},r("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},r("path",{d:"M23.5 9.5c1.417 0 2.5 1.083 2.5 2.5v9.167c0 1.416-1.083 2.5-2.5 2.5h-15c-1.417 0-2.5-1.084-2.5-2.5V12c0-1.417 1.083-2.5 2.5-2.5h2.917l1.416-2.167C13 7.167 13.25 7 13.5 7h5c.25 0 .5.167.667.333L20.583 9.5H23.5zM16 11.417a4.706 4.706 0 00-4.75 4.75 4.704 4.704 0 004.75 4.75 4.703 4.703 0 004.75-4.75c0-2.663-2.09-4.75-4.75-4.75zm0 7.825c-1.744 0-3.076-1.332-3.076-3.074 0-1.745 1.333-3.077 3.076-3.077 1.744 0 3.074 1.333 3.074 3.076s-1.33 3.075-3.074 3.075z",fill:"#02B383","fill-rule":"nonzero"}))),r("div",{className:"uppy-DashboardTab-name"},this.props.i18n("takePictureBtn")))),this.renderVideoCamera=()=>r("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MobileVideoCamera"},r("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerVideoCameraInputClick},r("div",{className:"uppy-DashboardTab-inner"},r("svg",{"aria-hidden":"true",width:"32",height:"32",viewBox:"0 0 32 32"},r("path",{fill:"#FF675E",fillRule:"nonzero",d:"m21.254 14.277 2.941-2.588c.797-.313 1.243.818 1.09 1.554-.01 2.094.02 4.189-.017 6.282-.126.915-1.145 1.08-1.58.34l-2.434-2.142c-.192.287-.504 1.305-.738.468-.104-1.293-.028-2.596-.05-3.894.047-.312.381.823.426 1.069.063-.384.206-.744.362-1.09zm-12.939-3.73c3.858.013 7.717-.025 11.574.02.912.129 1.492 1.237 1.351 2.217-.019 2.412.04 4.83-.03 7.239-.17 1.025-1.166 1.59-2.029 1.429-3.705-.012-7.41.025-11.114-.019-.913-.129-1.492-1.237-1.352-2.217.018-2.404-.036-4.813.029-7.214.136-.82.83-1.473 1.571-1.454z "}))),r("div",{className:"uppy-DashboardTab-name"},this.props.i18n("recordVideoBtn")))),this.renderBrowseButton=(e,t)=>{const s=this.props.acquirers.length;return r("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-Dashboard-browse",onClick:t,"data-uppy-super-focusable":s===0},e)},this.renderDropPasteBrowseTagline=e=>{const t=this.renderBrowseButton(this.props.i18n("browseFiles"),this.triggerFileInputClick),s=this.renderBrowseButton(this.props.i18n("browseFolders"),this.triggerFolderInputClick),n=this.props.fileManagerSelectionType,a=n.charAt(0).toUpperCase()+n.slice(1);return r("div",{class:"uppy-Dashboard-AddFiles-title"},this.props.disableLocalFiles?this.props.i18n("importFiles"):e>0?this.props.i18nArray(`dropPasteImport${a}`,{browseFiles:t,browseFolders:s,browse:t}):this.props.i18nArray(`dropPaste${a}`,{browseFiles:t,browseFolders:s,browse:t}))},this.renderAcquirer=e=>{var t;return r("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":e.id},r("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-cy":e.id,"aria-controls":`uppy-DashboardContent-panel--${e.id}`,"aria-selected":((t=this.props.activePickerPanel)==null?void 0:t.id)===e.id,"data-uppy-super-focusable":!0,onClick:()=>this.props.showPanel(e.id)},r("div",{className:"uppy-DashboardTab-inner"},e.icon()),r("div",{className:"uppy-DashboardTab-name"},e.name)))},this.renderAcquirers=e=>{const t=[...e],s=t.splice(e.length-2,e.length);return r(Te,null,t.map(n=>this.renderAcquirer(n)),r("span",{role:"presentation",style:{"white-space":"nowrap"}},s.map(n=>this.renderAcquirer(n))))},this.renderSourcesList=(e,t)=>{const{showNativePhotoCameraButton:s,showNativeVideoCameraButton:n}=this.props;let a=[];const d="myDevice";t||a.push({key:d,elements:this.renderMyDeviceAcquirer()}),s&&a.push({key:"nativePhotoCameraButton",elements:this.renderPhotoCamera()}),n&&a.push({key:"nativePhotoCameraButton",elements:this.renderVideoCamera()}),a.push(...e.map(c=>({key:c.id,elements:this.renderAcquirer(c)}))),a.length===1&&a[0].key===d&&(a=[]);const o=[...a],l=o.splice(a.length-2,a.length),u=c=>c.map(p=>{let{key:g,elements:f}=p;return r(Te,{key:g},f)});return r(Te,null,this.renderDropPasteBrowseTagline(a.length),r("div",{className:"uppy-Dashboard-AddFiles-list",role:"tablist"},u(o),r("span",{role:"presentation",style:{"white-space":"nowrap"}},u(l))))}}[hi](){this.props.i18nArray("dropPasteBoth"),this.props.i18nArray("dropPasteFiles"),this.props.i18nArray("dropPasteFolders"),this.props.i18nArray("dropPasteImportBoth"),this.props.i18nArray("dropPasteImportFiles"),this.props.i18nArray("dropPasteImportFolders")}renderPoweredByUppy(){const{i18nArray:e}=this.props,t=r("span",null,r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-Dashboard-poweredByIcon",width:"11",height:"11",viewBox:"0 0 11 11"},r("path",{d:"M7.365 10.5l-.01-4.045h2.612L5.5.806l-4.467 5.65h2.604l.01 4.044h3.718z",fillRule:"evenodd"})),r("span",{className:"uppy-Dashboard-poweredByUppy"},"Uppy")),s=e("poweredBy",{uppy:t});return r("a",{tabIndex:-1,href:"https://uppy.io",rel:"noreferrer noopener",target:"_blank",className:"uppy-Dashboard-poweredBy"},s)}render(){const{showNativePhotoCameraButton:e,showNativeVideoCameraButton:t,nativeCameraFacingMode:s}=this.props;return r("div",{className:"uppy-Dashboard-AddFiles"},this.renderHiddenInput(!1,n=>{this.fileInput=n}),this.renderHiddenInput(!0,n=>{this.folderInput=n}),e&&this.renderHiddenCameraInput("photo",s,n=>{this.mobilePhotoFileInput=n}),t&&this.renderHiddenCameraInput("video",s,n=>{this.mobileVideoFileInput=n}),this.renderSourcesList(this.props.acquirers,this.props.disableLocalFiles),r("div",{className:"uppy-Dashboard-AddFiles-info"},this.props.note&&r("div",{className:"uppy-Dashboard-note"},this.props.note),this.props.proudlyDisplayPoweredByUppy&&this.renderPoweredByUppy(this.props)))}}const vn=i=>r("div",{className:x("uppy-Dashboard-AddFilesPanel",i.className),"data-uppy-panelType":"AddFiles","aria-hidden":!i.showAddFilesPanel},r("div",{className:"uppy-DashboardContent-bar"},r("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},i.i18n("addingMoreFiles")),r("button",{className:"uppy-DashboardContent-back",type:"button",onClick:()=>i.toggleAddFilesPanel(!1)},i.i18n("back"))),r(ci,i));function j(i){const{tagName:e}=i.target;if(e==="INPUT"||e==="TEXTAREA"){i.stopPropagation();return}i.preventDefault(),i.stopPropagation()}function wn(i){let{activePickerPanel:e,className:t,hideAllPanels:s,i18n:n,state:a,uppy:d}=i;return r("div",{className:x("uppy-DashboardContent-panel",t),role:"tabpanel","data-uppy-panelType":"PickerPanel",id:`uppy-DashboardContent-panel--${e.id}`,onDragOver:j,onDragLeave:j,onDrop:j,onPaste:j},r("div",{className:"uppy-DashboardContent-bar"},r("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},n("importFrom",{name:e.name})),r("button",{className:"uppy-DashboardContent-back",type:"button",onClick:s},n("cancel"))),r("div",{className:"uppy-DashboardContent-panelBody"},d.getPlugin(e.id).render(a)))}function Fn(i){const e=i.files[i.fileCardFor],t=()=>{i.uppy.emit("file-editor:cancel",e),i.closeFileEditor()};return r("div",{className:x("uppy-DashboardContent-panel",i.className),role:"tabpanel","data-uppy-panelType":"FileEditor",id:"uppy-DashboardContent-panel--editor"},r("div",{className:"uppy-DashboardContent-bar"},r("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},i.i18nArray("editing",{file:r("span",{className:"uppy-DashboardContent-titleFile"},e.meta?e.meta.name:e.name)})),r("button",{className:"uppy-DashboardContent-back",type:"button",onClick:t},i.i18n("cancel")),r("button",{className:"uppy-DashboardContent-save",type:"button",onClick:i.saveFileEditor},i.i18n("save"))),r("div",{className:"uppy-DashboardContent-panelBody"},i.editors.map(s=>i.uppy.getPlugin(s.id).render(i.state))))}const z={STATE_ERROR:"error",STATE_WAITING:"waiting",STATE_PREPROCESSING:"preprocessing",STATE_UPLOADING:"uploading",STATE_POSTPROCESSING:"postprocessing",STATE_COMPLETE:"complete",STATE_PAUSED:"paused"};function Pn(i,e,t,s){if(s===void 0&&(s={}),i)return z.STATE_ERROR;if(e)return z.STATE_COMPLETE;if(t)return z.STATE_PAUSED;let n=z.STATE_WAITING;const a=Object.keys(s);for(let d=0;d<a.length;d++){const{progress:h}=s[a[d]];if(h.uploadStarted&&!h.uploadComplete)return z.STATE_UPLOADING;h.preprocess&&n!==z.STATE_UPLOADING&&(n=z.STATE_PREPROCESSING),h.postprocess&&n!==z.STATE_UPLOADING&&n!==z.STATE_PREPROCESSING&&(n=z.STATE_POSTPROCESSING)}return n}function Cn(i){let{files:e,i18n:t,isAllComplete:s,isAllErrored:n,isAllPaused:a,inProgressNotPausedFiles:d,newFiles:h,processingFiles:o}=i;switch(Pn(n,s,a,e)){case"uploading":return t("uploadingXFiles",{smart_count:d.length});case"preprocessing":case"postprocessing":return t("processingXFiles",{smart_count:o.length});case"paused":return t("uploadPaused");case"waiting":return t("xFilesSelected",{smart_count:h.length});case"complete":return t("uploadComplete");case"error":return t("error")}}function Sn(i){const{i18n:e,isAllComplete:t,hideCancelButton:s,maxNumberOfFiles:n,toggleAddFilesPanel:a,uppy:d}=i;let{allowNewUpload:h}=i;return h&&n&&(h=i.totalFileCount<i.maxNumberOfFiles),r("div",{className:"uppy-DashboardContent-bar"},!t&&!s?r("button",{className:"uppy-DashboardContent-back",type:"button",onClick:()=>d.cancelAll()},e("cancel")):r("div",null),r("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},r(Cn,i)),h?r("button",{className:"uppy-DashboardContent-addMore",type:"button","aria-label":e("addMoreFiles"),title:e("addMoreFiles"),onClick:()=>a(!0)},r("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"15",height:"15",viewBox:"0 0 15 15"},r("path",{d:"M8 6.5h6a.5.5 0 0 1 .5.5v.5a.5.5 0 0 1-.5.5H8v6a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V8h-6a.5.5 0 0 1-.5-.5V7a.5.5 0 0 1 .5-.5h6v-6A.5.5 0 0 1 7 0h.5a.5.5 0 0 1 .5.5v6z"})),r("span",{className:"uppy-DashboardContent-addMoreCaption"},e("addMore"))):r("div",null))}function Tn(i){const{computedMetaFields:e,requiredMetaFields:t,updateMeta:s,form:n,formState:a}=i,d={text:"uppy-u-reset uppy-c-textInput uppy-Dashboard-FileCard-input"};return e.map(h=>{const o=`uppy-Dashboard-FileCard-input-${h.id}`,l=t.includes(h.id);return r("fieldset",{key:h.id,className:"uppy-Dashboard-FileCard-fieldset"},r("label",{className:"uppy-Dashboard-FileCard-label",htmlFor:o},h.name),h.render!==void 0?h.render({value:a[h.id],onChange:u=>s(u,h.id),fieldCSSClasses:d,required:l,form:n.id},r):r("input",{className:d.text,id:o,form:n.id,type:h.type||"text",required:l,value:a[h.id],placeholder:h.placeholder,onInput:u=>s(u.target.value,h.id),"data-uppy-super-focusable":!0}))})}function An(i){var e;const{files:t,fileCardFor:s,toggleFileCard:n,saveFileCard:a,metaFields:d,requiredMetaFields:h,openFileEditor:o,i18n:l,i18nArray:u,className:c,canEditFile:p}=i,g=()=>typeof d=="function"?d(t[s]):d,f=t[s],y=(e=g())!=null?e:[],m=p(f),w={};y.forEach(E=>{var N;w[E.id]=(N=f.meta[E.id])!=null?N:""});const[C,b]=Ae(w),F=Ci(E=>{E.preventDefault(),a(C,s)},[a,C,s]),T=(E,N)=>{b({...C,[N]:E})},I=()=>{n(!1)},[A]=Ae(()=>{const E=document.createElement("form");return E.setAttribute("tabindex","-1"),E.id=_t(),E});return Xe(()=>(document.body.appendChild(A),A.addEventListener("submit",F),()=>{A.removeEventListener("submit",F),document.body.removeChild(A)}),[A,F]),r("div",{className:x("uppy-Dashboard-FileCard",c),"data-uppy-panelType":"FileCard",onDragOver:j,onDragLeave:j,onDrop:j,onPaste:j},r("div",{className:"uppy-DashboardContent-bar"},r("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},u("editing",{file:r("span",{className:"uppy-DashboardContent-titleFile"},f.meta?f.meta.name:f.name)})),r("button",{className:"uppy-DashboardContent-back",type:"button",form:A.id,title:l("finishEditingFile"),onClick:I},l("cancel"))),r("div",{className:"uppy-Dashboard-FileCard-inner"},r("div",{className:"uppy-Dashboard-FileCard-preview",style:{backgroundColor:lt(f.type).color}},r(li,{file:f}),m&&r("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-Dashboard-FileCard-edit",onClick:E=>{F(E),o(f)}},l("editImage"))),r("div",{className:"uppy-Dashboard-FileCard-info"},r(Tn,{computedMetaFields:y,requiredMetaFields:h,updateMeta:T,form:A,formState:C})),r("div",{className:"uppy-Dashboard-FileCard-actions"},r("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Dashboard-FileCard-actionsBtn",type:"submit",form:A.id},l("saveChanges")),r("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-link uppy-Dashboard-FileCard-actionsBtn",type:"button",onClick:I,form:A.id},l("cancel")))))}const te="uppy-transition-slideDownUp",Bt=250;function Se(i){let{children:e}=i;const[t,s]=Ae(null),[n,a]=Ae(""),d=Be(),h=Be(),o=Be(),l=()=>{a(`${te}-enter`),cancelAnimationFrame(o.current),clearTimeout(h.current),h.current=void 0,o.current=requestAnimationFrame(()=>{a(`${te}-enter ${te}-enter-active`),d.current=setTimeout(()=>{a("")},Bt)})},u=()=>{a(`${te}-leave`),cancelAnimationFrame(o.current),clearTimeout(d.current),d.current=void 0,o.current=requestAnimationFrame(()=>{a(`${te}-leave ${te}-leave-active`),h.current=setTimeout(()=>{s(null),a("")},Bt)})};return Xe(()=>{const c=Q(e)[0];t!==c&&(c&&!t?l():t&&!c&&!h.current&&u(),s(c))},[e,t]),Xe(()=>()=>{clearTimeout(d.current),clearTimeout(h.current),cancelAnimationFrame(o.current)},[]),t?Lt(t,{className:x(n,t.props.className)}):null}function J(){return J=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},J.apply(this,arguments)}const It=900,Ut=700,He=576,Nt=330;function En(i){const e=i.totalFileCount===0,t=i.totalFileCount===1,s=i.containerWidth>He,n=i.containerHeight>Nt,a=x({"uppy-Dashboard":!0,"uppy-Dashboard--isDisabled":i.disabled,"uppy-Dashboard--animateOpenClose":i.animateOpenClose,"uppy-Dashboard--isClosing":i.isClosing,"uppy-Dashboard--isDraggingOver":i.isDraggingOver,"uppy-Dashboard--modal":!i.inline,"uppy-size--md":i.containerWidth>He,"uppy-size--lg":i.containerWidth>Ut,"uppy-size--xl":i.containerWidth>It,"uppy-size--height-md":i.containerHeight>Nt,"uppy-Dashboard--isAddFilesPanelVisible":i.showAddFilesPanel,"uppy-Dashboard--isInnerWrapVisible":i.areInsidesReadyToBeVisible,"uppy-Dashboard--singleFile":i.singleFileFullScreen&&t&&n});let d=1;i.containerWidth>It?d=5:i.containerWidth>Ut?d=4:i.containerWidth>He&&(d=3);const h=i.showSelectedFiles&&!e,o=i.recoveredState?Object.keys(i.recoveredState.files).length:null,l=i.files?Object.keys(i.files).filter(p=>i.files[p].isGhost).length:null,u=()=>l>0?i.i18n("recoveredXFiles",{smart_count:l}):i.i18n("recoveredAllFiles");return r("div",{className:a,"data-uppy-theme":i.theme,"data-uppy-num-acquirers":i.acquirers.length,"data-uppy-drag-drop-supported":!i.disableLocalFiles&&Vs(),"aria-hidden":i.inline?"false":i.isHidden,"aria-disabled":i.disabled,"aria-label":i.inline?i.i18n("dashboardTitle"):i.i18n("dashboardWindowTitle"),onPaste:i.handlePaste,onDragOver:i.handleDragOver,onDragLeave:i.handleDragLeave,onDrop:i.handleDrop},r("div",{"aria-hidden":"true",className:"uppy-Dashboard-overlay",tabIndex:-1,onClick:i.handleClickOutside}),r("div",{className:"uppy-Dashboard-inner","aria-modal":!i.inline&&"true",role:i.inline?void 0:"dialog",style:{width:i.inline&&i.width?i.width:"",height:i.inline&&i.height?i.height:""}},i.inline?null:r("button",{className:"uppy-u-reset uppy-Dashboard-close",type:"button","aria-label":i.i18n("closeModal"),title:i.i18n("closeModal"),onClick:i.closeModal},r("span",{"aria-hidden":"true"},"×")),r("div",{className:"uppy-Dashboard-innerWrap"},r("div",{className:"uppy-Dashboard-dropFilesHereHint"},i.i18n("dropHint")),h&&r(Sn,i),o&&r("div",{className:"uppy-Dashboard-serviceMsg"},r("svg",{className:"uppy-Dashboard-serviceMsg-icon","aria-hidden":"true",focusable:"false",width:"21",height:"16",viewBox:"0 0 24 19"},r("g",{transform:"translate(0 -1)",fill:"none",fillRule:"evenodd"},r("path",{d:"M12.857 1.43l10.234 17.056A1 1 0 0122.234 20H1.766a1 1 0 01-.857-1.514L11.143 1.429a1 1 0 011.714 0z",fill:"#FFD300"}),r("path",{fill:"#000",d:"M11 6h2l-.3 8h-1.4z"}),r("circle",{fill:"#000",cx:"12",cy:"17",r:"1"}))),r("strong",{className:"uppy-Dashboard-serviceMsg-title"},i.i18n("sessionRestored")),r("div",{className:"uppy-Dashboard-serviceMsg-text"},u())),h?r(bn,{id:i.id,error:i.error,i18n:i.i18n,uppy:i.uppy,files:i.files,acquirers:i.acquirers,resumableUploads:i.resumableUploads,hideRetryButton:i.hideRetryButton,hidePauseResumeButton:i.hidePauseResumeButton,hideCancelButton:i.hideCancelButton,showLinkToFileUploadResult:i.showLinkToFileUploadResult,showRemoveButtonAfterComplete:i.showRemoveButtonAfterComplete,isWide:i.isWide,metaFields:i.metaFields,toggleFileCard:i.toggleFileCard,handleRequestThumbnail:i.handleRequestThumbnail,handleCancelThumbnail:i.handleCancelThumbnail,recoveredState:i.recoveredState,individualCancellation:i.individualCancellation,openFileEditor:i.openFileEditor,canEditFile:i.canEditFile,toggleAddFilesPanel:i.toggleAddFilesPanel,isSingleFile:t,itemsPerRow:d}):r(ci,J({},i,{isSizeMD:s})),r(Se,null,i.showAddFilesPanel?r(vn,J({key:"AddFiles"},i,{isSizeMD:s})):null),r(Se,null,i.fileCardFor?r(An,J({key:"FileCard"},i)):null),r(Se,null,i.activePickerPanel?r(wn,J({key:"Picker"},i)):null),r(Se,null,i.showFileEditor?r(Fn,J({key:"Editor"},i)):null),r("div",{className:"uppy-Dashboard-progressindicators"},i.progressindicators.map(p=>i.uppy.getPlugin(p.id).render(i.state))))))}const Dn={strings:{closeModal:"Close Modal",addMoreFiles:"Add more files",addingMoreFiles:"Adding more files",importFrom:"Import from %{name}",dashboardWindowTitle:"Uppy Dashboard Window (Press escape to close)",dashboardTitle:"Uppy Dashboard",copyLinkToClipboardSuccess:"Link copied to clipboard.",copyLinkToClipboardFallback:"Copy the URL below",copyLink:"Copy link",back:"Back",removeFile:"Remove file",editFile:"Edit file",editImage:"Edit image",editing:"Editing %{file}",error:"Error",finishEditingFile:"Finish editing file",saveChanges:"Save changes",myDevice:"My Device",dropHint:"Drop your files here",uploadComplete:"Upload complete",uploadPaused:"Upload paused",resumeUpload:"Resume upload",pauseUpload:"Pause upload",retryUpload:"Retry upload",cancelUpload:"Cancel upload",xFilesSelected:{0:"%{smart_count} file selected",1:"%{smart_count} files selected"},uploadingXFiles:{0:"Uploading %{smart_count} file",1:"Uploading %{smart_count} files"},processingXFiles:{0:"Processing %{smart_count} file",1:"Processing %{smart_count} files"},poweredBy:"Powered by %{uppy}",addMore:"Add more",editFileWithFilename:"Edit file %{file}",save:"Save",cancel:"Cancel",dropPasteFiles:"Drop files here or %{browseFiles}",dropPasteFolders:"Drop files here or %{browseFolders}",dropPasteBoth:"Drop files here, %{browseFiles} or %{browseFolders}",dropPasteImportFiles:"Drop files here, %{browseFiles} or import from:",dropPasteImportFolders:"Drop files here, %{browseFolders} or import from:",dropPasteImportBoth:"Drop files here, %{browseFiles}, %{browseFolders} or import from:",importFiles:"Import files from:",browseFiles:"browse files",browseFolders:"browse folders",recoveredXFiles:{0:"We could not fully recover 1 file. Please re-select it and resume the upload.",1:"We could not fully recover %{smart_count} files. Please re-select them and resume the upload."},recoveredAllFiles:"We restored all files. You can now resume the upload.",sessionRestored:"Session restored",reSelect:"Re-select",missingRequiredMetaFields:{0:"Missing required meta field: %{fields}.",1:"Missing required meta fields: %{fields}."},takePictureBtn:"Take Picture",recordVideoBtn:"Record Video"}};function P(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var kn=0;function _(i){return"__private_"+kn+++"_"+i}const On={version:"3.8.3"},Ve=Dt.default||Dt,Rt=9,Bn=27;function Mt(){const i={};return i.promise=new Promise((e,t)=>{i.resolve=e,i.reject=t}),i}const In={target:"body",metaFields:[],inline:!1,width:750,height:550,thumbnailWidth:280,thumbnailType:"image/jpeg",waitForThumbnailsBeforeUpload:!1,defaultPickerIcon:Ms,showLinkToFileUploadResult:!1,showProgressDetails:!1,hideUploadButton:!1,hideCancelButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideProgressAfterFinish:!1,note:null,closeModalOnClickOutside:!1,closeAfterFinish:!1,singleFileFullScreen:!0,disableStatusBar:!1,disableInformer:!1,disableThumbnailGenerator:!1,disablePageScrollWhenModalOpen:!0,animateOpenClose:!0,fileManagerSelectionType:"files",proudlyDisplayPoweredByUppy:!0,showSelectedFiles:!0,showRemoveButtonAfterComplete:!1,browserBackButtonClose:!1,showNativePhotoCameraButton:!1,showNativeVideoCameraButton:!1,theme:"light",autoOpen:null,autoOpenFileEditor:!1,disabled:!1,disableLocalFiles:!1,doneButtonHandler:null,onRequestCloseModal:null};var K=_("disabledNodes"),H=_("generateLargeThumbnailIfSingleFile"),le=_("openFileEditorWhenFilesAdded"),X=_("attachRenderFunctionToTarget"),qe=_("isTargetSupported"),je=_("getAcquirers"),We=_("getProgressIndicators"),V=_("getEditors"),Ge=_("addSpecifiedPluginsFromOptions"),Ke=_("autoDiscoverPlugins"),Y=_("addSupportedPluginIfNoTarget");let Un=class extends De{constructor(e,t){var s,n,a,d;let h;t?t.autoOpen===void 0?h=t.autoOpenFileEditor?"imageEditor":null:h=t.autoOpen:h=null,super(e,{...In,...t,autoOpen:h}),Object.defineProperty(this,K,{writable:!0,value:void 0}),this.modalName=`uppy-Dashboard-${_t()}`,this.superFocus=Hs(),this.ifFocusedOnUppyRecently=!1,this.removeTarget=o=>{const u=this.getPluginState().targets.filter(c=>c.id!==o.id);this.setPluginState({targets:u})},this.addTarget=o=>{const l=o.id||o.constructor.name,u=o.title||l,c=o.type;if(c!=="acquirer"&&c!=="progressindicator"&&c!=="editor"){const y="Dashboard: can only be targeted by plugins of types: acquirer, progressindicator, editor";return this.uppy.log(y,"error"),null}const p={id:l,name:u,type:c},f=this.getPluginState().targets.slice();return f.push(p),this.setPluginState({targets:f}),this.el},this.hideAllPanels=()=>{var o;const l=this.getPluginState(),u={activePickerPanel:void 0,showAddFilesPanel:!1,activeOverlayType:null,fileCardFor:null,showFileEditor:!1};l.activePickerPanel===u.activePickerPanel&&l.showAddFilesPanel===u.showAddFilesPanel&&l.showFileEditor===u.showFileEditor&&l.activeOverlayType===u.activeOverlayType||(this.setPluginState(u),this.uppy.emit("dashboard:close-panel",(o=l.activePickerPanel)==null?void 0:o.id))},this.showPanel=o=>{const{targets:l}=this.getPluginState(),u=l.find(c=>c.type==="acquirer"&&c.id===o);this.setPluginState({activePickerPanel:u,activeOverlayType:"PickerPanel"}),this.uppy.emit("dashboard:show-panel",o)},this.canEditFile=o=>{const{targets:l}=this.getPluginState();return P(this,V)[V](l).some(c=>this.uppy.getPlugin(c.id).canEditFile(o))},this.openFileEditor=o=>{const{targets:l}=this.getPluginState(),u=P(this,V)[V](l);this.setPluginState({showFileEditor:!0,fileCardFor:o.id||null,activeOverlayType:"FileEditor"}),u.forEach(c=>{this.uppy.getPlugin(c.id).selectFile(o)})},this.closeFileEditor=()=>{const{metaFields:o}=this.getPluginState();o&&o.length>0?this.setPluginState({showFileEditor:!1,activeOverlayType:"FileCard"}):this.setPluginState({showFileEditor:!1,fileCardFor:null,activeOverlayType:"AddFiles"})},this.saveFileEditor=()=>{const{targets:o}=this.getPluginState();P(this,V)[V](o).forEach(u=>{this.uppy.getPlugin(u.id).save()}),this.closeFileEditor()},this.openModal=()=>{const{promise:o,resolve:l}=Mt();if(this.savedScrollPosition=window.pageYOffset,this.savedActiveElement=document.activeElement,this.opts.disablePageScrollWhenModalOpen&&document.body.classList.add("uppy-Dashboard-isFixed"),this.opts.animateOpenClose&&this.getPluginState().isClosing){const u=()=>{this.setPluginState({isHidden:!1}),this.el.removeEventListener("animationend",u,!1),l()};this.el.addEventListener("animationend",u,!1)}else this.setPluginState({isHidden:!1}),l();return this.opts.browserBackButtonClose&&this.updateBrowserHistory(),document.addEventListener("keydown",this.handleKeyDownInModal),this.uppy.emit("dashboard:modal-open"),o},this.closeModal=o=>{var l;const u=(l=o==null?void 0:o.manualClose)!=null?l:!0,{isHidden:c,isClosing:p}=this.getPluginState();if(c||p)return;const{promise:g,resolve:f}=Mt();if(this.opts.disablePageScrollWhenModalOpen&&document.body.classList.remove("uppy-Dashboard-isFixed"),this.opts.animateOpenClose){this.setPluginState({isClosing:!0});const m=()=>{this.setPluginState({isHidden:!0,isClosing:!1}),this.superFocus.cancel(),this.savedActiveElement.focus(),this.el.removeEventListener("animationend",m,!1),f()};this.el.addEventListener("animationend",m,!1)}else this.setPluginState({isHidden:!0}),this.superFocus.cancel(),this.savedActiveElement.focus(),f();if(document.removeEventListener("keydown",this.handleKeyDownInModal),u&&this.opts.browserBackButtonClose){var y;(y=history.state)!=null&&y[this.modalName]&&history.back()}return this.uppy.emit("dashboard:modal-closed"),g},this.isModalOpen=()=>!this.getPluginState().isHidden||!1,this.requestCloseModal=()=>this.opts.onRequestCloseModal?this.opts.onRequestCloseModal():this.closeModal(),this.setDarkModeCapability=o=>{const{capabilities:l}=this.uppy.getState();this.uppy.setState({capabilities:{...l,darkMode:o}})},this.handleSystemDarkModeChange=o=>{const l=o.matches;this.uppy.log(`[Dashboard] Dark mode is ${l?"on":"off"}`),this.setDarkModeCapability(l)},this.toggleFileCard=(o,l)=>{const u=this.uppy.getFile(l);o?this.uppy.emit("dashboard:file-edit-start",u):this.uppy.emit("dashboard:file-edit-complete",u),this.setPluginState({fileCardFor:o?l:null,activeOverlayType:o?"FileCard":null})},this.toggleAddFilesPanel=o=>{this.setPluginState({showAddFilesPanel:o,activeOverlayType:o?"AddFiles":null})},this.addFiles=o=>{const l=o.map(u=>({source:this.id,name:u.name,type:u.type,data:u,meta:{relativePath:u.relativePath||u.webkitRelativePath||null}}));try{this.uppy.addFiles(l)}catch(u){this.uppy.log(u)}},this.startListeningToResize=()=>{this.resizeObserver=new ResizeObserver(o=>{const l=o[0],{width:u,height:c}=l.contentRect;this.setPluginState({containerWidth:u,containerHeight:c,areInsidesReadyToBeVisible:!0})}),this.resizeObserver.observe(this.el.querySelector(".uppy-Dashboard-inner")),this.makeDashboardInsidesVisibleAnywayTimeout=setTimeout(()=>{const o=this.getPluginState(),l=!this.opts.inline&&o.isHidden;!o.areInsidesReadyToBeVisible&&!l&&(this.uppy.log("[Dashboard] resize event didn’t fire on time: defaulted to mobile layout","warning"),this.setPluginState({areInsidesReadyToBeVisible:!0}))},1e3)},this.stopListeningToResize=()=>{this.resizeObserver.disconnect(),clearTimeout(this.makeDashboardInsidesVisibleAnywayTimeout)},this.recordIfFocusedOnUppyRecently=o=>{this.el.contains(o.target)?this.ifFocusedOnUppyRecently=!0:(this.ifFocusedOnUppyRecently=!1,this.superFocus.cancel())},this.disableInteractiveElements=o=>{var l;const u=["a[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])",'[role="button"]:not([disabled])'],c=(l=P(this,K)[K])!=null?l:he(this.el.querySelectorAll(u)).filter(p=>!p.classList.contains("uppy-Dashboard-close"));for(const p of c)p.tagName==="A"?p.setAttribute("aria-disabled",o):p.disabled=o;o?P(this,K)[K]=c:P(this,K)[K]=null,this.dashboardIsDisabled=o},this.updateBrowserHistory=()=>{var o;(o=history.state)!=null&&o[this.modalName]||history.pushState({...history.state,[this.modalName]:!0},""),window.addEventListener("popstate",this.handlePopState,!1)},this.handlePopState=o=>{var l;this.isModalOpen()&&(!o.state||!o.state[this.modalName])&&this.closeModal({manualClose:!1}),!this.isModalOpen()&&(l=o.state)!=null&&l[this.modalName]&&history.back()},this.handleKeyDownInModal=o=>{o.keyCode===Bn&&this.requestCloseModal(),o.keyCode===Rt&&oi(o,this.getPluginState().activeOverlayType,this.el)},this.handleClickOutside=()=>{this.opts.closeModalOnClickOutside&&this.requestCloseModal()},this.handlePaste=o=>{this.uppy.iteratePlugins(u=>{u.type==="acquirer"&&(u.handleRootPaste==null||u.handleRootPaste(o))});const l=he(o.clipboardData.files);l.length>0&&(this.uppy.log("[Dashboard] Files pasted"),this.addFiles(l))},this.handleInputChange=o=>{o.preventDefault();const l=he(o.target.files);l.length>0&&(this.uppy.log("[Dashboard] Files selected through input"),this.addFiles(l))},this.handleDragOver=o=>{var l,u;o.preventDefault(),o.stopPropagation();const c=()=>{let y=!0;return this.uppy.iteratePlugins(m=>{m.canHandleRootDrop!=null&&m.canHandleRootDrop(o)&&(y=!0)}),y},p=()=>{const{types:y}=o.dataTransfer;return y.some(m=>m==="Files")},g=c(),f=p();if(!g&&!f||this.opts.disabled||this.opts.disableLocalFiles&&(f||!g)||!this.uppy.getState().allowNewUpload){o.dataTransfer.dropEffect="none",clearTimeout(this.removeDragOverClassTimeout);return}o.dataTransfer.dropEffect="copy",clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!0}),(l=(u=this.opts).onDragOver)==null||l.call(u,o)},this.handleDragLeave=o=>{var l,u;o.preventDefault(),o.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.removeDragOverClassTimeout=setTimeout(()=>{this.setPluginState({isDraggingOver:!1})},50),(l=(u=this.opts).onDragLeave)==null||l.call(u,o)},this.handleDrop=async o=>{var l,u;o.preventDefault(),o.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!1}),this.uppy.iteratePlugins(f=>{f.type==="acquirer"&&(f.handleRootDrop==null||f.handleRootDrop(o))});let c=!1;const p=f=>{this.uppy.log(f,"error"),c||(this.uppy.info(f.message,"error"),c=!0)};this.uppy.log("[Dashboard] Processing dropped files");const g=await Bs(o.dataTransfer,{logDropError:p});g.length>0&&(this.uppy.log("[Dashboard] Files dropped"),this.addFiles(g)),(l=(u=this.opts).onDrop)==null||l.call(u,o)},this.handleRequestThumbnail=o=>{this.opts.waitForThumbnailsBeforeUpload||this.uppy.emit("thumbnail:request",o)},this.handleCancelThumbnail=o=>{this.opts.waitForThumbnailsBeforeUpload||this.uppy.emit("thumbnail:cancel",o)},this.handleKeyDownInInline=o=>{o.keyCode===Rt&&$s(o,this.getPluginState().activeOverlayType,this.el)},this.handlePasteOnBody=o=>{this.el.contains(document.activeElement)&&this.handlePaste(o)},this.handleComplete=o=>{let{failed:l}=o;this.opts.closeAfterFinish&&!(l!=null&&l.length)&&this.requestCloseModal()},this.handleCancelRestore=()=>{this.uppy.emit("restore-canceled")},Object.defineProperty(this,H,{writable:!0,value:()=>{if(this.opts.disableThumbnailGenerator)return;const o=600,l=this.uppy.getFiles();if(l.length===1){const u=this.uppy.getPlugin(`${this.id}:ThumbnailGenerator`);u==null||u.setOptions({thumbnailWidth:o});const c={...l[0],preview:void 0};u==null||u.requestThumbnail(c).then(()=>{u==null||u.setOptions({thumbnailWidth:this.opts.thumbnailWidth})})}}}),Object.defineProperty(this,le,{writable:!0,value:o=>{const l=o[0],{metaFields:u}=this.getPluginState(),c=u&&u.length>0,p=this.canEditFile(l);c&&this.opts.autoOpen==="metaEditor"?this.toggleFileCard(!0,l.id):p&&this.opts.autoOpen==="imageEditor"&&this.openFileEditor(l)}}),this.initEvents=()=>{if(this.opts.trigger&&!this.opts.inline){const o=At(this.opts.trigger);o?o.forEach(l=>l.addEventListener("click",this.openModal)):this.uppy.log("Dashboard modal trigger not found. Make sure `trigger` is set in Dashboard options, unless you are planning to call `dashboard.openModal()` method yourself","warning")}this.startListeningToResize(),document.addEventListener("paste",this.handlePasteOnBody),this.uppy.on("plugin-added",P(this,Y)[Y]),this.uppy.on("plugin-remove",this.removeTarget),this.uppy.on("file-added",this.hideAllPanels),this.uppy.on("dashboard:modal-closed",this.hideAllPanels),this.uppy.on("complete",this.handleComplete),this.uppy.on("files-added",P(this,H)[H]),this.uppy.on("file-removed",P(this,H)[H]),document.addEventListener("focus",this.recordIfFocusedOnUppyRecently,!0),document.addEventListener("click",this.recordIfFocusedOnUppyRecently,!0),this.opts.inline&&this.el.addEventListener("keydown",this.handleKeyDownInInline),this.opts.autoOpen&&this.uppy.on("files-added",P(this,le)[le])},this.removeEvents=()=>{const o=At(this.opts.trigger);!this.opts.inline&&o&&o.forEach(l=>l.removeEventListener("click",this.openModal)),this.stopListeningToResize(),document.removeEventListener("paste",this.handlePasteOnBody),window.removeEventListener("popstate",this.handlePopState,!1),this.uppy.off("plugin-added",P(this,Y)[Y]),this.uppy.off("plugin-remove",this.removeTarget),this.uppy.off("file-added",this.hideAllPanels),this.uppy.off("dashboard:modal-closed",this.hideAllPanels),this.uppy.off("complete",this.handleComplete),this.uppy.off("files-added",P(this,H)[H]),this.uppy.off("file-removed",P(this,H)[H]),document.removeEventListener("focus",this.recordIfFocusedOnUppyRecently),document.removeEventListener("click",this.recordIfFocusedOnUppyRecently),this.opts.inline&&this.el.removeEventListener("keydown",this.handleKeyDownInInline),this.opts.autoOpen&&this.uppy.off("files-added",P(this,le)[le])},this.superFocusOnEachUpdate=()=>{const o=this.el.contains(document.activeElement),l=document.activeElement===document.body||document.activeElement===null,u=this.uppy.getState().info.length===0,c=!this.opts.inline;u&&(c||o||l&&this.ifFocusedOnUppyRecently)?this.superFocus(this.el,this.getPluginState().activeOverlayType):this.superFocus.cancel()},this.afterUpdate=()=>{if(this.opts.disabled&&!this.dashboardIsDisabled){this.disableInteractiveElements(!0);return}!this.opts.disabled&&this.dashboardIsDisabled&&this.disableInteractiveElements(!1),this.superFocusOnEachUpdate()},this.saveFileCard=(o,l)=>{this.uppy.setFileMeta(l,o),this.toggleFileCard(!1,l)},Object.defineProperty(this,X,{writable:!0,value:o=>{const l=this.uppy.getPlugin(o.id);return{...o,icon:l.icon||this.opts.defaultPickerIcon,render:l.render}}}),Object.defineProperty(this,qe,{writable:!0,value:o=>{const l=this.uppy.getPlugin(o.id);return typeof l.isSupported!="function"?!0:l.isSupported()}}),Object.defineProperty(this,je,{writable:!0,value:Ve(o=>o.filter(l=>l.type==="acquirer"&&P(this,qe)[qe](l)).map(P(this,X)[X]))}),Object.defineProperty(this,We,{writable:!0,value:Ve(o=>o.filter(l=>l.type==="progressindicator").map(P(this,X)[X]))}),Object.defineProperty(this,V,{writable:!0,value:Ve(o=>o.filter(l=>l.type==="editor").map(P(this,X)[X]))}),this.render=o=>{const l=this.getPluginState(),{files:u,capabilities:c,allowNewUpload:p}=o,{newFiles:g,uploadStartedFiles:f,completeFiles:y,erroredFiles:m,inProgressFiles:w,inProgressNotPausedFiles:C,processingFiles:b,isUploadStarted:F,isAllComplete:T,isAllErrored:I,isAllPaused:A}=this.uppy.getObjectOfFilesPerState(),E=P(this,je)[je](l.targets),N=P(this,We)[We](l.targets),Z=P(this,V)[V](l.targets);let W;return this.opts.theme==="auto"?W=c.darkMode?"dark":"light":W=this.opts.theme,["files","folders","both"].indexOf(this.opts.fileManagerSelectionType)<0&&(this.opts.fileManagerSelectionType="files",console.warn(`Unsupported option for "fileManagerSelectionType". Using default of "${this.opts.fileManagerSelectionType}".`)),En({state:o,isHidden:l.isHidden,files:u,newFiles:g,uploadStartedFiles:f,completeFiles:y,erroredFiles:m,inProgressFiles:w,inProgressNotPausedFiles:C,processingFiles:b,isUploadStarted:F,isAllComplete:T,isAllErrored:I,isAllPaused:A,totalFileCount:Object.keys(u).length,totalProgress:o.totalProgress,allowNewUpload:p,acquirers:E,theme:W,disabled:this.opts.disabled,disableLocalFiles:this.opts.disableLocalFiles,direction:this.opts.direction,activePickerPanel:l.activePickerPanel,showFileEditor:l.showFileEditor,saveFileEditor:this.saveFileEditor,closeFileEditor:this.closeFileEditor,disableInteractiveElements:this.disableInteractiveElements,animateOpenClose:this.opts.animateOpenClose,isClosing:l.isClosing,progressindicators:N,editors:Z,autoProceed:this.uppy.opts.autoProceed,id:this.id,closeModal:this.requestCloseModal,handleClickOutside:this.handleClickOutside,handleInputChange:this.handleInputChange,handlePaste:this.handlePaste,inline:this.opts.inline,showPanel:this.showPanel,hideAllPanels:this.hideAllPanels,i18n:this.i18n,i18nArray:this.i18nArray,uppy:this.uppy,note:this.opts.note,recoveredState:o.recoveredState,metaFields:l.metaFields,resumableUploads:c.resumableUploads||!1,individualCancellation:c.individualCancellation,isMobileDevice:c.isMobileDevice,fileCardFor:l.fileCardFor,toggleFileCard:this.toggleFileCard,toggleAddFilesPanel:this.toggleAddFilesPanel,showAddFilesPanel:l.showAddFilesPanel,saveFileCard:this.saveFileCard,openFileEditor:this.openFileEditor,canEditFile:this.canEditFile,width:this.opts.width,height:this.opts.height,showLinkToFileUploadResult:this.opts.showLinkToFileUploadResult,fileManagerSelectionType:this.opts.fileManagerSelectionType,proudlyDisplayPoweredByUppy:this.opts.proudlyDisplayPoweredByUppy,hideCancelButton:this.opts.hideCancelButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,showRemoveButtonAfterComplete:this.opts.showRemoveButtonAfterComplete,containerWidth:l.containerWidth,containerHeight:l.containerHeight,areInsidesReadyToBeVisible:l.areInsidesReadyToBeVisible,isTargetDOMEl:this.isTargetDOMEl,parentElement:this.el,allowedFileTypes:this.uppy.opts.restrictions.allowedFileTypes,maxNumberOfFiles:this.uppy.opts.restrictions.maxNumberOfFiles,requiredMetaFields:this.uppy.opts.restrictions.requiredMetaFields,showSelectedFiles:this.opts.showSelectedFiles,showNativePhotoCameraButton:this.opts.showNativePhotoCameraButton,showNativeVideoCameraButton:this.opts.showNativeVideoCameraButton,nativeCameraFacingMode:this.opts.nativeCameraFacingMode,singleFileFullScreen:this.opts.singleFileFullScreen,handleCancelRestore:this.handleCancelRestore,handleRequestThumbnail:this.handleRequestThumbnail,handleCancelThumbnail:this.handleCancelThumbnail,isDraggingOver:l.isDraggingOver,handleDragOver:this.handleDragOver,handleDragLeave:this.handleDragLeave,handleDrop:this.handleDrop})},Object.defineProperty(this,Ge,{writable:!0,value:()=>{(this.opts.plugins||[]).forEach(l=>{const u=this.uppy.getPlugin(l);u?u.mount(this,u):this.uppy.log(`[Uppy] Dashboard could not find plugin '${l}', make sure to uppy.use() the plugins you are specifying`,"warning")})}}),Object.defineProperty(this,Ke,{writable:!0,value:()=>{this.uppy.iteratePlugins(P(this,Y)[Y])}}),Object.defineProperty(this,Y,{writable:!0,value:o=>{var l;const u=["acquirer","editor"];o&&!((l=o.opts)!=null&&l.target)&&u.includes(o.type)&&(this.getPluginState().targets.some(p=>o.id===p.id)||o.mount(this,o))}}),this.install=()=>{this.setPluginState({isHidden:!0,fileCardFor:null,activeOverlayType:null,showAddFilesPanel:!1,activePickerPanel:void 0,showFileEditor:!1,metaFields:this.opts.metaFields,targets:[],areInsidesReadyToBeVisible:!1,isDraggingOver:!1});const{inline:o,closeAfterFinish:l}=this.opts;if(o&&l)throw new Error("[Dashboard] `closeAfterFinish: true` cannot be used on an inline Dashboard, because an inline Dashboard cannot be closed at all. Either set `inline: false`, or disable the `closeAfterFinish` option.");const{allowMultipleUploads:u,allowMultipleUploadBatches:c}=this.uppy.opts;(u||c)&&l&&this.uppy.log("[Dashboard] When using `closeAfterFinish`, we recommended setting the `allowMultipleUploadBatches` option to `false` in the Uppy constructor. See https://uppy.io/docs/uppy/#allowMultipleUploads-true","warning");const{target:p}=this.opts;p&&this.mount(p,this),this.opts.disableStatusBar||this.uppy.use(qt,{id:`${this.id}:StatusBar`,target:this,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,hideCancelButton:this.opts.hideCancelButton,showProgressDetails:this.opts.showProgressDetails,hideAfterFinish:this.opts.hideProgressAfterFinish,locale:this.opts.locale,doneButtonHandler:this.opts.doneButtonHandler}),this.opts.disableInformer||this.uppy.use(Wt,{id:`${this.id}:Informer`,target:this}),this.opts.disableThumbnailGenerator||this.uppy.use(ti,{id:`${this.id}:ThumbnailGenerator`,thumbnailWidth:this.opts.thumbnailWidth,thumbnailHeight:this.opts.thumbnailHeight,thumbnailType:this.opts.thumbnailType,waitForThumbnailsBeforeUpload:this.opts.waitForThumbnailsBeforeUpload,lazy:!this.opts.waitForThumbnailsBeforeUpload}),this.darkModeMediaQuery=typeof window<"u"&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null;const g=this.darkModeMediaQuery?this.darkModeMediaQuery.matches:!1;if(this.uppy.log(`[Dashboard] Dark mode is ${g?"on":"off"}`),this.setDarkModeCapability(g),this.opts.theme==="auto"){var f;(f=this.darkModeMediaQuery)==null||f.addListener(this.handleSystemDarkModeChange)}P(this,Ge)[Ge](),P(this,Ke)[Ke](),this.initEvents()},this.uninstall=()=>{if(!this.opts.disableInformer){const u=this.uppy.getPlugin(`${this.id}:Informer`);u&&this.uppy.removePlugin(u)}if(!this.opts.disableStatusBar){const u=this.uppy.getPlugin(`${this.id}:StatusBar`);u&&this.uppy.removePlugin(u)}if(!this.opts.disableThumbnailGenerator){const u=this.uppy.getPlugin(`${this.id}:ThumbnailGenerator`);u&&this.uppy.removePlugin(u)}if((this.opts.plugins||[]).forEach(u=>{const c=this.uppy.getPlugin(u);c&&c.unmount()}),this.opts.theme==="auto"){var l;(l=this.darkModeMediaQuery)==null||l.removeListener(this.handleSystemDarkModeChange)}this.opts.disablePageScrollWhenModalOpen&&document.body.classList.remove("uppy-Dashboard-isFixed"),this.unmount(),this.removeEvents()},this.id=this.opts.id||"Dashboard",this.title="Dashboard",this.type="orchestrator",this.defaultLocale=Dn,(n=(s=this.opts).doneButtonHandler)!=null||(s.doneButtonHandler=()=>{this.uppy.clearUploadedFiles(),this.requestCloseModal()}),(d=(a=this.opts).onRequestCloseModal)!=null||(a.onRequestCloseModal=()=>this.closeModal()),this.i18nInit()}};Un.VERSION=On.version;export{Un as D,qt as S,Bs as g,Vs as i,he as t};
