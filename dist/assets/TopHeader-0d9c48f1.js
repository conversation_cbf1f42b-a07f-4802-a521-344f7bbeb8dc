import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as n,L as t}from"./vendor-2ae44a2e.js";import{M as f,A as w,G as v}from"./index-b2ff2fa1.js";import{_ as C}from"./qr-scanner-cf010ec4.js";import{g as N}from"./@mantine/core-1006e8cf.js";import{t as k}from"./tailwind-merge-05141ada.js";import{d as b,e as c,f as y,h as g,j as _}from"./index.esm-42944128.js";import{b as M,c as S,d as L}from"./index.esm-de9a80b6.js";import{g as h,h as F,i as O,U as I,L as A}from"./lucide-react-1246a7ed.js";import"./react-confirm-alert-783bc3ae.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@fullcalendar/core-a789a586.js";n.lazy(()=>C(()=>import("./BackButton-5d19a352.js"),["assets/BackButton-5d19a352.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/index-f2c2b086.js","assets/qr-scanner-cf010ec4.js"]));new f;function r(...o){return k(N(o))}const le=()=>{const{state:o}=n.useContext(w);n.useContext(v);const[a,s]=n.useState(!1),[i,l]=n.useState("assistants");n.useState(!1);const[x,p]=n.useState(null);n.useRef(null);const u=n.useRef(),m=j=>{u.current&&clearTimeout(u.current),s(!0),p(j)},d=()=>{u.current=setTimeout(()=>{s(!1),p(null)},200)};return(o==null?void 0:o.role)=="user"?e.jsx("nav",{className:"border-bg-transparent fixed left-0 right-0 top-0 z-50 bg-[#0f1827] text-white",children:e.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex h-16 items-center justify-between",children:[e.jsx("div",{className:"flex flex-shrink-0 items-center",children:e.jsx("span",{className:"font-bold md:text-[13px] lg:text-xl",children:"AutomateIntel - Voice"})}),e.jsx("div",{className:"mx-8 hidden flex-1 items-center justify-center bg-[#0f1827] text-white md:flex",children:e.jsxs("div",{className:"relative flex  items-center rounded-full bg-[#1d2937] p-1",children:[e.jsxs("div",{className:"relative",onMouseEnter:()=>m("assistants"),onMouseLeave:d,children:[e.jsx(t,{to:"/user/voice",children:e.jsxs("button",{onClick:()=>{s(!a),l("assistants")},className:r("relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",i==="assistants"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:["Assistants",e.jsx(h,{className:"ml-1 h-4 w-4 2xl:h-6 2xl:w-6"})]})}),x==="assistants"&&e.jsxs("div",{className:"absolute left-0 top-full mt-1 w-56 rounded-md bg-[#0f1827] py-1 shadow-lg",children:[e.jsxs(t,{to:"/user/voice",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("assistants")},children:[e.jsx(b,{className:"h-4 w-4"}),e.jsx("span",{children:"Voice Assistant Test"})]}),e.jsxs(t,{to:"/user/sms",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("assistants")},children:[e.jsx(c,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Assistant Test"})]}),e.jsxs(t,{to:"/user/assistants",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("assistants")},children:[e.jsx(y,{className:"h-4 w-4"}),e.jsx("span",{children:"Voice Assistants"})]}),e.jsxs(t,{to:"/user/voice_list",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("assistants")},children:[e.jsx(b,{className:"h-4 w-4"}),e.jsx("span",{children:"Cloned Voices"})]})]})]}),e.jsxs("div",{className:"relative",onMouseEnter:()=>m("campaigns"),onMouseLeave:d,children:[e.jsx(t,{to:"/user/outbound_campaigns",children:e.jsxs("button",{onClick:()=>{s(!a),l("campaigns")},className:r("relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",i==="campaigns"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:["Campaigns",e.jsx(h,{className:"ml-1 h-4 w-4 2xl:w-6"})]})}),x==="campaigns"&&e.jsxs("div",{className:"absolute left-0 top-full mt-1 w-56 rounded-md bg-[#0f1827] py-1 shadow-lg",children:[e.jsxs(t,{to:"/user/outbound_campaigns",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("campaigns")},children:[e.jsx(g,{className:"h-4 w-4"}),e.jsx("span",{children:"OutBound Campaigns"})]}),e.jsxs(t,{to:"/user/inbound_campaigns",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("campaigns")},children:[e.jsx(_,{className:"h-4 w-4"}),e.jsx("span",{children:"Inbound Campaigns"})]}),e.jsxs(t,{to:"/user/sms_outbound_campaigns",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("campaigns")},children:[e.jsx(g,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Outbound Campaigns"})]}),e.jsxs(t,{to:"/user/sms_inbound_campaigns",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("campaigns")},children:[e.jsx(g,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Inbound Campaigns"})]})]})]}),e.jsxs("div",{className:"relative",onMouseEnter:()=>m("logs"),onMouseLeave:d,children:[e.jsx(t,{to:"/user/outbound_call_logs",children:e.jsxs("button",{onClick:()=>{s(!a),l("logs")},className:r("relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",i==="logs"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:["Logs",e.jsx(h,{className:"ml-1 h-4 w-4 2xl:w-6"})]})}),x==="logs"&&e.jsxs("div",{className:"absolute left-0 top-full mt-1 w-64 rounded-md bg-[#0f1827] py-1 shadow-lg",children:[e.jsxs(t,{to:"/user/outbound_call_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("logs")},children:[e.jsx(M,{className:"h-4 w-4"}),e.jsx("span",{children:"Outbound Call Logs"})]}),e.jsxs(t,{to:"/user/inbound_call_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("logs")},children:[e.jsx(S,{className:"h-4 w-4"}),e.jsx("span",{children:"Inbound Call Logs"})]}),e.jsxs(t,{to:"/user/test_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("logs")},children:[e.jsx(L,{className:"h-4 w-4"}),e.jsx("span",{children:"Sample Voice Call Log"})]}),e.jsxs(t,{to:"/user/test_sms_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("logs")},children:[e.jsx(c,{className:"h-4 w-4"}),e.jsx("span",{children:"Sample SMS Followup Logs"})]}),e.jsxs(t,{to:"/user/email_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("logs")},children:[e.jsx(c,{className:"h-4 w-4"}),e.jsx("span",{children:"Email Logs"})]}),e.jsxs(t,{to:"/user/sms_outbound_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("logs")},children:[e.jsx(c,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Outbound Logs"})]}),e.jsxs(t,{to:"/user/sms_inbound_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("logs")},children:[e.jsx(c,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Inbound Logs"})]})]})]}),e.jsx(t,{to:"/user/numbers",children:e.jsx("button",{onClick:()=>{l("phone"),s(!1),p(null)},className:r("relative z-10 rounded-full px-6 py-2 text-sm font-medium md:text-[12px] lg:text-sm",i==="phone"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:"Phone #"})}),e.jsxs("div",{className:"relative",onMouseEnter:()=>m("settings"),onMouseLeave:d,children:[e.jsxs("button",{onClick:()=>{s(!a),l("settings")},className:r("relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",i==="settings"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:["Settings",e.jsx(h,{className:"ml-1 h-4 w-4 2xl:w-6"})]}),x==="settings"&&e.jsxs("div",{className:"absolute left-0 top-full mt-1 w-48 rounded-md bg-[#0f1827] py-1 shadow-lg",children:[e.jsxs(t,{to:"/user/settings",className:"block flex items-center space-x-3 px-4 py-2 text-sm  text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("settings")},children:[e.jsx(F,{className:"h-4 w-4"}),e.jsx("span",{children:"Settings"})]}),e.jsxs(t,{to:"/user/stripe_subscription",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!a),l("settings")},children:[e.jsx(O,{className:"h-4 w-4"}),e.jsx("span",{children:"Billing"})]})]})]})]})}),e.jsxs("div",{className:"hidden items-center space-x-6 md:flex",children:[e.jsx(t,{to:"/user/profile",className:"text-white hover:text-gray-600",children:e.jsx(I,{className:"h-5 w-5 2xl:w-7"})}),e.jsx(t,{to:"/user/logout",className:"text-white hover:text-gray-600",children:e.jsx(A,{className:"h-5 w-5 2xl:w-7"})})]}),e.jsx("div",{className:"flex items-center md:hidden",children:e.jsx("button",{className:"text-white hover:text-gray-600",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]})})}):e.jsx("nav",{className:"border-b border-gray-200 bg-white",children:e.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"flex h-16 items-center justify-between",children:e.jsx("span",{className:"text-xl font-bold",children:"AutomateIntel - Voice"})})})})};export{r as cn,le as default};
