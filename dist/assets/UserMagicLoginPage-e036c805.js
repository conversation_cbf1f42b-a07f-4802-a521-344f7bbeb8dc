import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as a,h as f,f as b}from"./vendor-2ae44a2e.js";import{u as w}from"./react-hook-form-47c010f8.js";import{o as j}from"./yup-5abd4662.js";import{c as y,a as N}from"./yup-5c93ed04.js";import{A as v,G as S,M as A,s as k}from"./index-b2ff2fa1.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const V=()=>{var i,m;const l=y({email:N().email().required()}).required();a.useContext(v);const{dispatch:n}=a.useContext(S),[c,s]=a.useState(!1),o=f();b();const{register:p,handleSubmit:d,setError:u,formState:{errors:r}}=w({resolver:j(l)}),x=async g=>{let h=new A;try{s(!0);const t=await h.magicLoginAttempt(g.email,o==null?void 0:o.role);t.error||(s(!1),console.log(t),k(n,"Please check your mail to complete login attempt"))}catch(t){s(!1),console.log("Error",t),u("email",{type:"manual",message:t.message})}};return e.jsxs("div",{className:"mx-auto w-full max-w-xs",children:[e.jsxs("form",{onSubmit:d(x),className:"px-8 pt-6 pb-8 mt-8 mb-4 bg-white rounded shadow-md",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...p("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(i=r.email)!=null&&i.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(m=r.email)==null?void 0:m.message})]}),e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("button",{type:"submit",className:"focus:shadow-outline rounded bg-[#2cc9d5] px-4 py-2 font-bold text-white hover:bg-[#2cc9d5]/70 focus:outline-none",children:[c?"Attempting Log In...":"Sign In"," "]})})]}),e.jsxs("p",{className:"text-xs text-center text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})};export{V as default};
