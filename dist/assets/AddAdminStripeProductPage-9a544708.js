import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as l,r as A,f as S}from"./vendor-2ae44a2e.js";import{u as P}from"./react-hook-form-47c010f8.js";import{o as D}from"./yup-5abd4662.js";import{c as L,a as j}from"./yup-5c93ed04.js";import{G as y,M,s as N,t as $}from"./index-b2ff2fa1.js";import"./react-quill-d06fcfc9.js";import"./InteractiveButton-bff38983.js";import"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";const ne=({setSidebar:r,closeSidebar:d})=>{var u,h,f,g;const v=L({name:j().required(),description:j().nullable()}).required(),{dispatch:o}=l.useContext(y),{dispatch:k}=l.useContext(y),[m,a]=A.useState(!1);S();const{register:c,handleSubmit:p,setError:C,formState:{errors:s}}=P({resolver:D(v)}),x=async b=>{let E=new M;a(!0);try{const t=await E.addStripeProduct({name:b.name,description:b.description});if(!t.error)N(o,"Added"),a(!1),d&&d();else if(t.validation){const w=Object.keys(t.validation);for(let i=0;i<w.length;i++){const n=w[i];console.log(n),C(n,{type:"manual",message:t.validation[n]})}}}catch(t){console.log("Error",t),N(o,t.message),$(o,t.message),a(!1)}};return l.useEffect(()=>{k({type:"SETPATH",payload:{path:"products"}})},[]),e.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>r(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add Product"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]",onClick:()=>r(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(x)(),r(!1)},disabled:m,children:m?"Saving":"Save"})]})]}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:p(x),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...c("name"),className:`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${(u=s.name)!=null&&u.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(h=s.name)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"description",children:"Description"}),e.jsx("input",{type:"text",placeholder:"Description",...c("description"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(f=s.description)!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=s.description)==null?void 0:g.message})]})]})]})};export{ne as default};
