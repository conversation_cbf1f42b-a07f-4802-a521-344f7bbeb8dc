import{j as r}from"./@react-google-maps/api-c55ecefa.js";import{R as a,f as E}from"./vendor-2ae44a2e.js";import{u as T}from"./react-hook-form-47c010f8.js";import{o as k}from"./yup-5abd4662.js";import{c as I,a as h}from"./yup-5c93ed04.js";import{M as y,G as x,A as P,s as R,t as L}from"./index-b2ff2fa1.js";import"./react-quill-d06fcfc9.js";import{M as S}from"./MkdInput-a584fac2.js";import{I as M}from"./InteractiveButton-bff38983.js";import"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let w=new y;const dt=({inbound:i,setSidebar:G})=>{const{dispatch:p}=a.useContext(x),{state:o}=a.useContext(P),_=I({name:h(),script_id:h()}).required(),{dispatch:N}=a.useContext(x);a.useState({});const[l,C]=a.useState([]),[c,m]=a.useState(!1),j=E(),{register:d,handleSubmit:v,setError:u,setValue:O,formState:{errors:g}}=T({resolver:k(_)});a.useState([]);const A=async s=>{let e=new y;m(!0);try{e.setTable("campaign");const t=await e.callRestAPI({name:s.name,campaign_type:i?1:0,script_id:s.script_id,user_id:o.user},"POST");if(!t.error)R(p,"Added"),j(i?"/user/inbound_campaigns":"/user/outbound_campaigns");else if(t.validation){const b=Object.keys(t.validation);for(let n=0;n<b.length;n++){const f=b[n];u(f,{type:"manual",message:t.validation[f]})}}m(!1)}catch(t){m(!1),console.log("Error",t),u("name",{type:"manual",message:t.message}),L(N,t.message)}};return a.useEffect(()=>{p({type:"SETPATH",payload:{path:i?"inbound_campaigns":"outbound_campaigns"}}),async function(){w.setTable("scripts"),console.log("state",o.user);const e=await w.callRestAPI({user_id:o.user,filter:[`user_id,eq,${o.user}`]},"GETALL");e.error||(console.log("scripts",e),C(e.list?e.list.map(t=>({script_id:t.id,script:t.script})):[]))}()},[]),r.jsxs("div",{className:"mx-auto rounded bg-[#1d2937] p-5 shadow-md",children:[r.jsxs("h4",{className:"mb-6 text-2xl font-medium text-white",children:["Add New ",i?"Inbound":"Outbound"," Campaign"]}),r.jsxs("form",{className:"w-full max-w-lg",onSubmit:v(A),children:[r.jsx(S,{type:"text",page:"add",name:"name",errors:g,label:"Campaign Name",placeholder:"Name of the campaign",register:d,className:"border-gray-700 bg-[#1d2937] text-white",labelClassName:"text-white"}),r.jsx(S,{type:"mapping",page:"add",name:"script_id",errors:g,label:"Script",placeholder:"Select a script",options:l.map(s=>s.script_id),mapping:l.reduce((s,e)=>(s[e.script_id]=e.script,s),{}),register:d,className:"border-gray-700 bg-[#1d2937] text-white",labelClassName:"text-white"}),r.jsx(M,{type:"submit",loading:c,disabled:c,className:"focus:shadow-outline rounded bg-[#19b2f6]/80  px-4 py-2 font-bold text-white hover:bg-[#0c99db] focus:outline-none",children:"Submit"})]})]})};export{dt as default};
