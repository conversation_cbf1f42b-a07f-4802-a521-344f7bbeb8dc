import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as m,f as I}from"./vendor-2ae44a2e.js";import{u as v}from"./react-hook-form-47c010f8.js";import{o as T}from"./yup-5abd4662.js";import{c as P,a as r}from"./yup-5c93ed04.js";import{G as h,M as k,s as A,t as E}from"./index-b2ff2fa1.js";import"./react-quill-d06fcfc9.js";import{M as o}from"./MkdInput-a584fac2.js";import{I as D}from"./InteractiveButton-bff38983.js";import"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";const pe=()=>{var f,y;const{dispatch:u}=m.useContext(h),j=P({name:r(),product_id:r(),stripe_id:r(),is_usage_metered:r(),usage_limit:r(),object:r(),amount:r(),trial_days:r(),type:r(),status:r()}).required(),{dispatch:N}=m.useContext(h),[b,M]=m.useState({}),[g,n]=m.useState(!1),S=I(),{register:s,handleSubmit:_,setError:x,setValue:O,formState:{errors:t}}=v({resolver:T(j)});m.useState([]);const w=async a=>{let c=new k;n(!0);try{for(let p in b){let l=new FormData;l.append("file",b[p].file);let d=await c.uploadImage(l);a[p]=d.url}c.setTable("stripe_price");const i=await c.callRestAPI({name:a.name,product_id:a.product_id,stripe_id:a.stripe_id,is_usage_metered:a.is_usage_metered,usage_limit:a.usage_limit,object:a.object,amount:a.amount,trial_days:a.trial_days,type:a.type,status:a.status},"POST");if(!i.error)A(u,"Added"),S("/admin/stripe_price");else if(i.validation){const p=Object.keys(i.validation);for(let l=0;l<p.length;l++){const d=p[l];x(d,{type:"manual",message:i.validation[d]})}}n(!1)}catch(i){n(!1),console.log("Error",i),x("name",{type:"manual",message:i.message}),E(N,i.message)}};return m.useEffect(()=>{u({type:"SETPATH",payload:{path:"stripe_price"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Stripe Price"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:_(w),children:[e.jsx(o,{type:"text",page:"add",name:"name",errors:t,label:"Name",placeholder:"Name",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"product_id",errors:t,label:"Product Id",placeholder:"Product Id",register:s,className:""}),e.jsx(o,{type:"text",page:"add",name:"stripe_id",errors:t,label:"Stripe Id",placeholder:"Stripe Id",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"is_usage_metered",errors:t,label:"Is Usage Metered",placeholder:"Is Usage Metered",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"usage_limit",errors:t,label:"Usage Limit",placeholder:"Usage Limit",register:s,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"object",children:"Object"}),e.jsx("textarea",{placeholder:"Object",...s("object"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(f=t.object)!=null&&f.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(y=t.object)==null?void 0:y.message})]}),e.jsx(o,{type:"number",page:"add",name:"amount",errors:t,label:"Amount",placeholder:"Amount",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"trial_days",errors:t,label:"Trial Days",placeholder:"Trial Days",register:s,className:""}),e.jsx(o,{type:"text",page:"add",name:"type",errors:t,label:"Type",placeholder:"Type",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"status",errors:t,label:"Status",placeholder:"Status",register:s,className:""}),e.jsx(D,{type:"submit",loading:g,disabled:g,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{pe as default};
