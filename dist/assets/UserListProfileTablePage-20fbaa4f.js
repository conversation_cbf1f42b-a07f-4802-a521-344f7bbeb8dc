import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as s,f as u,r as h}from"./vendor-2ae44a2e.js";import{M as S,A as x,G as g,d as w}from"./index-b2ff2fa1.js";import{M as d}from"./index-9aa09a5c.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";new S;const b=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User Id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Fcm Token",accessor:"fcm_token",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Device Id",accessor:"device_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Device Type",accessor:"device_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],q=()=>{s.useContext(x),s.useContext(g),u();const[c,a]=s.useState(!1),[p,i]=s.useState(!1),[E,n]=s.useState(),m=h.useRef(null),[v,f]=s.useState([]),o=(t,r,l=[])=>{switch(t){case"add":a(r);break;case"edit":i(r),f(l),n(l[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(w,{columns:b,tableRole:"user",table:"profile",actionId:"id",actions:{view:{show:!1,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:t=>o("edit",!0,t)},delete:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!0,action:()=>o("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:"onTable",refreshRef:m})})}),e.jsx(d,{isModalActive:c,closeModalFn:()=>a(!1)}),e.jsx(d,{isModalActive:p,closeModalFn:()=>i(!1)})]})};export{q as default};
