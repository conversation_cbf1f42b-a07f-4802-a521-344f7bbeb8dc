import{j as s}from"./@react-google-maps/api-c55ecefa.js";import{R as i,h as n}from"./vendor-2ae44a2e.js";import"./yup-5c93ed04.js";import{M as j,G as c,t as h}from"./index-b2ff2fa1.js";import{S as N}from"./index-a74110af.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let r=new j;const A=()=>{i.useContext(c);const{dispatch:t}=i.useContext(c),[e,d]=i.useState({}),[x,l]=i.useState(!0),m=n();return i.useEffect(function(){(async function(){try{l(!0),r.setTable("stripe_price");const a=await r.callRestAPI({id:Number(m==null?void 0:m.id),join:""},"GET");a.error||(d(a.model),l(!1))}catch(a){l(!1),console.log("error",a),h(t,a.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:x?s.jsx(N,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Stripe Price"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Product Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.product_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Stripe Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.stripe_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Is Usage Metered"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.is_usage_metered})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Usage Limit"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.usage_limit})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Object"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.object})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Amount"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.amount})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Trial Days"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.trial_days})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Type"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.type})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})})]})})};export{A as default};
