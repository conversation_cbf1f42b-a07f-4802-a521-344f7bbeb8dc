import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as o,f as R,r as i,h as A}from"./vendor-2ae44a2e.js";import{u as B}from"./react-hook-form-47c010f8.js";import{o as C}from"./yup-5abd4662.js";import{c as D,a as d}from"./yup-5c93ed04.js";import{M as L,A as M,G,t as K,s as O}from"./index-b2ff2fa1.js";import"./react-quill-d06fcfc9.js";import{M as u}from"./MkdInput-a584fac2.js";import{I as U}from"./InteractiveButton-bff38983.js";import{S as $}from"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let l=new L;const ye=()=>{const{dispatch:w}=o.useContext(M),y=D({transcript:d(),temp974:d(),filename:d(),status:d()}).required(),{dispatch:b}=o.useContext(G),[h,q]=o.useState({}),[S,f]=o.useState(!1),[j,g]=o.useState(!1),T=R(),[H,E]=i.useState(""),[V,N]=i.useState(""),[z,v]=i.useState(""),[J,I]=i.useState(""),{register:n,handleSubmit:F,setError:k,setValue:m,formState:{errors:p}}=B({resolver:C(y)}),x=A();i.useEffect(function(){(async function(){try{g(!0),l.setTable("knowledge_bank");const e=await l.callRestAPI({id:Number(x==null?void 0:x.id)},"GET");e.error||(m("transcript",e.model.transcript),m("temp974",e.model.temp974),m("filename",e.model.filename),m("status",e.model.status),E(e.model.transcript),N(e.model.temp974),v(e.model.filename),I(e.model.status),setId(e.model.id),g(!1))}catch(e){g(!1),console.log("error",e),K(w,e.message)}})()},[]);const P=async e=>{f(!0);try{l.setTable("knowledge_bank");for(let r in h){let s=new FormData;s.append("file",h[r].file);let c=await l.uploadImage(s);e[r]=c.url}const a=await l.callRestAPI({id,transcript:e.transcript,temp974:e.temp974,filename:e.filename,status:e.status},"PUT");if(!a.error)O(b,"Updated"),T("/admin/knowledge_bank");else if(a.validation){const r=Object.keys(a.validation);for(let s=0;s<r.length;s++){const c=r[s];k(c,{type:"manual",message:a.validation[c]})}}f(!1)}catch(a){f(!1),console.log("Error",a),k("transcript",{type:"manual",message:a.message})}};return o.useEffect(()=>{b({type:"SETPATH",payload:{path:"knowledge_bank"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Knowledge Bank"}),j?t.jsx($,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:F(P),children:[t.jsx(u,{type:"text",page:"edit",name:"transcript",errors:p,label:"Transcript",placeholder:"Transcript",register:n,className:""}),t.jsx(u,{type:"text",page:"edit",name:"temp974",errors:p,label:"Temp974",placeholder:"Temp974",register:n,className:""}),t.jsx(u,{type:"text",page:"edit",name:"filename",errors:p,label:"Filename",placeholder:"Filename",register:n,className:""}),t.jsx(u,{type:"text",page:"edit",name:"status",errors:p,label:"Status",placeholder:"Status",register:n,className:""}),t.jsx(U,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:S,disable:S,children:"Submit"})]})]})};export{ye as default};
