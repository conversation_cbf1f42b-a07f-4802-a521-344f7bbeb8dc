import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as t,f as S,r as s}from"./vendor-2ae44a2e.js";import{M as w,A as g,G as v,d as b,o as j}from"./index-b2ff2fa1.js";import"./index-9aa09a5c.js";import{q as o,_ as d}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";new w;const y=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Script",accessor:"script",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],B=()=>{const{state:c}=t.useContext(g),{dispatch:p}=t.useContext(v);S();const[m,a]=t.useState(!1),[E,u]=t.useState(!1),[T,f]=t.useState(),h=s.useRef(null),[A,x]=t.useState([]);t.useEffect(()=>{p({type:"SETPATH",payload:{path:"scripts"}})},[]);const r=(i,l,n=[])=>{switch(i){case"add":a(l);break;case"edit":u(l),x(n),f(n[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto rounded bg-white pt-5 shadow md:p-5",children:e.jsx(b,{columns:y,tableRole:"user",table:"scripts",actionId:"id",defaultFilter:[`user_id,eq,${c.user}`],actions:{view:{show:!1,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>r("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!0,action:()=>r("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:"onTable",refreshRef:h})})}),e.jsx(o,{appear:!0,show:m,as:s.Fragment,children:e.jsxs(d,{as:"div",className:"relative z-[100]",onClose:()=>a(!1),children:[e.jsx(o.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(o.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 translate-x-full",enterTo:"opacity-100 translate-x-0",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-x-0",leaveTo:"opacity-0 translate-x-full",children:e.jsx(d.Panel,{className:"h-[95vh] w-full max-w-3xl transform overflow-y-auto bg-[#1d2937]  p-6 text-left align-middle shadow-xl transition-all",children:e.jsx(j,{closeSidebar:()=>a(!1)})})})})})]})})]})};export{B as default};
