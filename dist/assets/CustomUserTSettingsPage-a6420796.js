import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as x,R as J}from"./vendor-2ae44a2e.js";import{O as ae,C as le,a as he,T as re,D as ie,P as xe,R as ne,b as ue,c as oe,d as pe,e as ge,f as fe,g as ce,h as be,i as ye,j as je}from"./radix-ui-c399dc8b.js";import{g as q}from"./@mantine/core-1006e8cf.js";import{t as H}from"./tailwind-merge-05141ada.js";import{X as _e,c as j,b as ve,D as Ne,P as we,E as Q,d as ee}from"./lucide-react-1246a7ed.js";import{M as Se,G as de,A as Te,s as R}from"./index-b2ff2fa1.js";import{u as ke}from"./react-hook-form-47c010f8.js";import{c as Ce,a as M,b as G,d as W}from"./yup-5c93ed04.js";import{o as Ae}from"./yup-5abd4662.js";import{d as Ee}from"./react-slick-6ad4ab99.js";import{S as Ie}from"./index-f2c2b086.js";import"./@fullcalendar/core-a789a586.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";import"./@uppy/dashboard-af41baed.js";import"./@uppy/core-a7fbc19c.js";import"./@uppy/aws-s3-a38c5234.js";import"./@craftjs/core-9da1c17f.js";import"./@uppy/compressor-d7e7d557.js";function Z(...t){return H(q(t))}const Pe=xe,me=x.forwardRef(({className:t,...r},i)=>e.jsx(ae,{ref:i,className:Z("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0  fixed inset-0 z-50 bg-black/80",t),...r}));me.displayName=ae.displayName;const De=x.forwardRef(({className:t,children:r,...i},d)=>e.jsxs(Pe,{children:[e.jsx(me,{}),e.jsxs(le,{ref:d,className:Z("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-transparent p-6  duration-200 sm:rounded-lg",t),...i,children:[r,e.jsxs(he,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent absolute right-4 top-4 rounded-sm text-white opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:text-white",children:[e.jsx(_e,{className:"h-5 w-5 2xl:w-7 2xl:w-7"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));De.displayName=le.displayName;const Oe=x.forwardRef(({className:t,...r},i)=>e.jsx(re,{ref:i,className:Z("text-lg font-semibold leading-none tracking-tight",t),...r}));Oe.displayName=re.displayName;const Ke=x.forwardRef(({className:t,...r},i)=>e.jsx(ie,{ref:i,className:Z("text-muted-foreground text-sm",t),...r}));Ke.displayName=ie.displayName;function Re(...t){return H(q(t))}const D=x.forwardRef(({className:t,type:r,...i},d)=>e.jsx("input",{type:r,className:Re("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:d,...i}));D.displayName="Input";function se(...t){return H(q(t))}const A=x.forwardRef(({className:t,...r},i)=>e.jsx(ne,{className:se("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=unchecked]:bg-input peer inline-flex h-4 w-8 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary",t),...r,ref:i,children:e.jsx(ue,{className:se("pointer-events-none block h-5 w-5 rounded-full bg-white shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));A.displayName=ne.displayName;function Fe(...t){return H(q(t))}const F=x.forwardRef(({className:t,...r},i)=>e.jsxs(oe,{ref:i,className:Fe("relative flex w-full touch-none select-none items-center",t),...r,children:[e.jsx(pe,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:e.jsx(ge,{className:"absolute h-full bg-primary"})}),e.jsx(fe,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-white ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));F.displayName=oe.displayName;function Ve(...t){return H(q(t))}const _=be,v=ye,N=je,b=x.forwardRef(({className:t,sideOffset:r=4,...i},d)=>e.jsx(ce,{ref:d,sideOffset:r,className:Ve("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...i}));b.displayName=ce.displayName;let C=new Se;const te=[{value:"Pacific/Honolulu",label:"Hawaii-Aleutian Standard Time (HST)"},{value:"America/Anchorage",label:"Alaska Standard Time (AKST)"},{value:"America/Los_Angeles",label:"Pacific Standard Time (PST)"},{value:"America/Denver",label:"Mountain Standard Time (MST)"},{value:"America/Phoenix",label:"Mountain Standard Time (no DST)"},{value:"America/Chicago",label:"Central Standard Time (CST)"},{value:"America/Chihuahua",label:"America/Chihuahua"},{value:"America/New_York",label:"Eastern Standard Time (EST)"},{value:"America/Toronto",label:"Eastern Standard Time (Canada)"},{value:"America/Caracas",label:"Venezuela Standard Time (VET)"},{value:"America/Bogota",label:"Colombia Time (COT)"},{value:"America/Argentina/Buenos_Aires",label:"Argentina Time (ART)"},{value:"Atlantic/Bermuda",label:"Atlantic Standard Time (AST)"},{value:"Europe/London",label:"Greenwich Mean Time (GMT)"},{value:"Europe/Paris",label:"Central European Time (CET)"},{value:"Europe/Athens",label:"Eastern European Time (EET)"},{value:"Africa/Johannesburg",label:"South Africa Standard Time (SAST)"},{value:"Asia/Dubai",label:"Gulf Standard Time (GST)"},{value:"Asia/Kolkata",label:"Indian Standard Time (IST)"},{value:"Asia/Shanghai",label:"China Standard Time (CST)"},{value:"Asia/Tokyo",label:"Japan Standard Time (JST)"},{value:"Australia/Sydney",label:"Australian Eastern Standard Time (AEST)"},{value:"Pacific/Auckland",label:"New Zealand Standard Time (NZST)"}],ze=Ce().shape({username:M().when("activeTab",{is:"ghl",then:t=>t.required("Calendar ID is required"),otherwise:t=>t}),apiKey:M().when("activeTab",{is:t=>t==="ghl"||t==="cal",then:t=>t.required("API Key is required"),otherwise:t=>t}),timezone:M().when("activeTab",{is:"ghl",then:t=>t.required("Timezone is required"),otherwise:t=>t}),activeTab:M().required(),automate_emails:G(),default_assistant_id:W().when("activeTab",{is:"email",then:t=>t.required("Default assistant ID is required"),otherwise:t=>t}),reply_signature:M(),reply_delay_minutes:W().min(0).max(60),notification_on_reply:G(),cc_user_on_replies:G(),max_auto_replies_per_thread:W().min(1).max(10),working_hours_enabled:G(),working_hours_timezone:M(),out_of_office_enabled:G(),out_of_office_message:M()});function Me(){const[t,r]=x.useState(!1),[i,d]=x.useState(!1),[c,k]=x.useState("ghl"),[m,a]=x.useState(null),{dispatch:u}=J.useContext(de),{state:E}=J.useContext(Te),{register:n,handleSubmit:f,formState:{errors:p,isValid:T},reset:V,watch:y,setValue:I}=ke({mode:"onChange",resolver:Ae(ze),defaultValues:{username:"",apiKey:"",timezone:"",activeTab:"ghl",automate_emails:!0,default_assistant_id:"",reply_signature:`Best regards,
VoiceOutreach Team`,reply_delay_minutes:5,notification_on_reply:!0,cc_user_on_replies:!0,max_auto_replies_per_thread:3,working_hours_enabled:!0,working_hours_timezone:"America/New_York",out_of_office_enabled:!1,out_of_office_message:"I'm currently out of the office and will respond when I return."}});x.useEffect(()=>{(async()=>{try{await P()}catch(l){console.error("Error in useEffect:",l),u&&R(u,"Failed to load settings",4e3,"error")}})()},[]),x.useEffect(()=>{try{I("activeTab",c),P()}catch(l){console.error("Error updating form:",l)}},[c]);const P=async()=>{var l,w,K,z,$;console.log(E);try{C.setTable("user_settings");const s=await C.callRestAPI({user_id:E.user,filter:[`user_id,eq,${E.user}`]},"GETALL"),o=await C.callRestAPI({user_id:E.user,filter:["user_id,eq,23"]},"GETALL");if((l=s==null?void 0:s.list)!=null&&l[0]){a(s.list[0]),console.log(s.list[0]);let S={};try{S=s.list[0].settings?JSON.parse(s.list[0].settings):{}}catch(g){console.error("Error parsing settings JSON:",g)}if(c==="ghl")try{const g=S.calendars||s.list[0].calendars,h=g?JSON.parse(g):[];h!=null&&h[0]&&V({username:h[0].calendarId,apiKey:h[0].GOHIGHLEVEL_TOKEN,timezone:h[0].timezone,activeTab:"ghl"})}catch(g){console.error("Error parsing calendars:",g),u&&R(u,"Failed to parse calendar settings",4e3,"error")}if(c==="cal"){const g=S.cal_api_key||s.list[0].cal_api_key;g&&V({apiKey:g,activeTab:"cal"})}if(c==="email")try{const g=s.list[0].email_settings,h=g?JSON.parse(g):{};h&&Object.keys(h).length>0&&V({activeTab:"email",automate_emails:h.automate_emails??!0,default_assistant_id:h.default_assistant_id??"",reply_signature:h.reply_signature??`Best regards,
VoiceOutreach Team`,reply_delay_minutes:h.reply_delay_minutes??5,notification_on_reply:h.notification_on_reply??!0,cc_user_on_replies:h.cc_user_on_replies??!0,max_auto_replies_per_thread:h.max_auto_replies_per_thread??3,working_hours_enabled:((w=h.working_hours)==null?void 0:w.enabled)??!0,working_hours_timezone:((K=h.working_hours)==null?void 0:K.timeZone)??"America/New_York",out_of_office_enabled:((z=h.out_of_office)==null?void 0:z.enabled)??!1,out_of_office_message:(($=h.out_of_office)==null?void 0:$.message)??"I'm currently out of the office and will respond when I return."})}catch(g){console.error("Error parsing email settings:",g),u&&R(u,"Failed to parse email settings",4e3,"error")}}}catch(s){console.error("Error fetching settings:",s),u&&R(u,"Failed to fetch settings",4e3,"error")}},L=async l=>{try{if(r(!0),C.setTable("user_settings"),c==="ghl"){const w={calendarId:l.username,GOHIGHLEVEL_TOKEN:l.apiKey,timezone:l.timezone,campaign_id:"1"},z={...m!=null&&m.settings?JSON.parse(m.settings):{},calendars:JSON.stringify([w])};await C.callRestAPI({id:m==null?void 0:m.id,calendars:JSON.stringify([w]),settings:JSON.stringify(z)},"PUT")}else if(c==="cal"){const K={...m!=null&&m.settings?JSON.parse(m.settings):{},cal_api_key:l.apiKey};await C.callRestAPI({id:m==null?void 0:m.id,cal_api_key:l.apiKey,settings:JSON.stringify(K)},"PUT")}else if(c==="email"){const w={automate_emails:l.automate_emails,default_assistant_id:parseInt(l.default_assistant_id),reply_signature:l.reply_signature,reply_delay_minutes:parseInt(l.reply_delay_minutes),notification_on_reply:l.notification_on_reply,cc_user_on_replies:l.cc_user_on_replies,max_auto_replies_per_thread:parseInt(l.max_auto_replies_per_thread),working_hours:{enabled:l.working_hours_enabled,timeZone:l.working_hours_timezone,monday:{start:"09:00",end:"17:00"},tuesday:{start:"09:00",end:"17:00"},wednesday:{start:"09:00",end:"17:00"},thursday:{start:"09:00",end:"17:00"},friday:{start:"09:00",end:"17:00"},saturday:{enabled:!1},sunday:{enabled:!1}},out_of_office:{enabled:l.out_of_office_enabled,start_date:null,end_date:null,message:l.out_of_office_message}};await C.callRestAPI({id:m==null?void 0:m.id,email_settings:JSON.stringify(w)},"PUT")}u&&R(u,"Settings updated successfully!",4e3),await P()}catch(w){console.error("Error saving settings:",w),u&&R(u,"Failed to update settings",4e3,"error")}finally{r(!1)}},B=async()=>{try{r(!0);const l=await C.callRawAPI("/v3/api/custom/voiceoutreach/user/calendar/auth",{},"GET");if(!l.error&&l.link)window.open(l.link);else throw new Error("Invalid response from calendar auth")}catch(l){console.error("Error connecting calendar:",l),u&&R(u,"Failed to connect to Google Calendar",4e3,"error")}finally{r(!1)}},O=y(),U=c==="ghl"?O.username&&O.apiKey&&O.timezone:c==="cal"?O.apiKey:c==="email"?O.default_assistant_id:!1;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",className:`flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 ${c==="ghl"?"bg-[#19b2f6]/20":"hover:bg-gray-100/10"}`,onClick:()=>k("ghl"),children:e.jsx("span",{className:"font-semibold text-white",children:"GHL"})}),e.jsx("button",{type:"button",className:`flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 ${c==="cal"?"bg-[#19b2f6]/20":"hover:bg-gray-100/10"}`,onClick:()=>k("cal"),children:e.jsx("p",{className:"font-semibold text-white",children:"Cal.com"})}),e.jsx("button",{type:"button",className:`flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 ${c==="email"?"bg-[#19b2f6]/20":"hover:bg-gray-100/10"}`,onClick:()=>k("email"),children:e.jsx("span",{className:"font-semibold text-white",children:"Email"})}),e.jsx("button",{type:"button",className:"flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 hover:bg-gray-100/10",onClick:B,children:e.jsx("span",{className:"font-semibold text-white",children:"Google"})})]}),e.jsx("form",{onSubmit:f(L),className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[c==="ghl"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx(D,{...n("username"),placeholder:"Calendar ID",className:`bg-transparent text-white ${p.username?"border-red-400":"border-gray-300"} placeholder:text-gray-300`}),p.username&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:p.username.message})]}),e.jsxs("div",{className:"relative mt-2",children:[e.jsx(D,{...n("apiKey"),type:i?"text":"password",placeholder:"API Key",className:`bg-transparent text-white ${p.apiKey?"border-red-400":"border-gray-300"} pr-10 placeholder:text-gray-300`}),e.jsx("button",{type:"button",onClick:()=>d(!i),className:"absolute right-2 top-[50%] z-10 -translate-y-1/2 text-white hover:text-gray-300",children:i?e.jsx(Q,{className:"h-5 w-5"}):e.jsx(ee,{className:"h-5 w-5"})}),p.apiKey&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:p.apiKey.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"timezone",className:"mt-1 block text-sm font-medium text-white",children:"Timezone"}),e.jsxs("select",{...n("timezone"),className:`mt-1 block w-full rounded-md ${p.timezone?"border-red-400":"border-gray-300"} bg-[#1d2937] text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm`,children:[e.jsx("option",{value:"",children:"Select your timezone"}),te.map(l=>e.jsx("option",{value:l.value,children:l.label},l.value))]}),p.timezone&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:p.timezone.message})]})]}),c==="cal"&&e.jsxs("div",{className:"relative mt-2",children:[e.jsx(D,{...n("apiKey"),type:i?"text":"password",placeholder:"Cal.com API Key",className:`bg-transparent text-white ${p.apiKey?"border-red-400":"border-gray-300"} pr-10 placeholder:text-gray-300`}),e.jsx("button",{type:"button",onClick:()=>d(!i),className:"absolute right-2 top-[50%] z-10 -translate-y-1/2 text-white hover:text-gray-300",children:i?e.jsx(Q,{className:"h-5 w-5"}):e.jsx(ee,{className:"h-5 w-5"})}),p.apiKey&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:p.apiKey.message})]}),c==="email"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-white",children:"Automate Emails"}),e.jsx(A,{checked:y("automate_emails"),onCheckedChange:l=>I("automate_emails",l),className:"bg-gray-600 data-[state=checked]:bg-blue-500"})]}),e.jsxs("div",{children:[e.jsx(D,{...n("default_assistant_id"),type:"number",placeholder:"Default Assistant ID",className:`bg-transparent text-white ${p.default_assistant_id?"border-red-400":"border-gray-300"} placeholder:text-gray-300`}),p.default_assistant_id&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:p.default_assistant_id.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-white",children:"Reply Signature"}),e.jsx("textarea",{...n("reply_signature"),placeholder:"Best regards,\\nVoiceOutreach Team",rows:3,className:"w-full rounded-md border border-gray-300 bg-transparent p-2 text-white placeholder:text-gray-300"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-sm font-medium text-white",children:["Reply Delay (minutes): ",y("reply_delay_minutes")]}),e.jsx(F,{value:[y("reply_delay_minutes")||5],max:60,min:0,step:1,className:"w-full",onValueChange:([l])=>I("reply_delay_minutes",l)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-white",children:"Notify on Reply"}),e.jsx(A,{checked:y("notification_on_reply"),onCheckedChange:l=>I("notification_on_reply",l),className:"bg-gray-600 data-[state=checked]:bg-blue-500"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-white",children:"CC User on Replies"}),e.jsx(A,{checked:y("cc_user_on_replies"),onCheckedChange:l=>I("cc_user_on_replies",l),className:"bg-gray-600 data-[state=checked]:bg-blue-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-sm font-medium text-white",children:["Max Auto Replies per Thread:"," ",y("max_auto_replies_per_thread")]}),e.jsx(F,{value:[y("max_auto_replies_per_thread")||3],max:10,min:1,step:1,className:"w-full",onValueChange:([l])=>I("max_auto_replies_per_thread",l)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-white",children:"Enable Working Hours"}),e.jsx(A,{...n("working_hours_enabled"),className:"bg-gray-600 data-[state=checked]:bg-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-white",children:"Working Hours Timezone"}),e.jsx("select",{...n("working_hours_timezone"),className:"w-full rounded-md border border-gray-300 bg-[#1d2937] text-white",children:te.map(l=>e.jsx("option",{value:l.value,children:l.label},l.value))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-white",children:"Enable Out of Office"}),e.jsx(A,{...n("out_of_office_enabled"),className:"bg-gray-600 data-[state=checked]:bg-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-white",children:"Out of Office Message"}),e.jsx("textarea",{...n("out_of_office_message"),placeholder:"I'm currently out of the office and will respond when I return.",rows:3,className:"w-full rounded-md border border-gray-300 bg-transparent p-2 text-white placeholder:text-gray-300"})]})]}),e.jsx("button",{disabled:!U||t,type:"submit",className:"mt-3 inline-flex h-[41px] justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-3 py-2 text-sm font-medium text-white shadow-sm hover:border-[#19b2f6] hover:bg-[#19b2f6]/60 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:t?"Saving...":c==="ghl"?"Connect GHL":c==="cal"?"Save API Key":c==="email"?"Save Email Settings":"Save"})]})})]})}function _s({title:t,leftLabel:r,rightLabel:i,value:d,onChange:c,tooltipContent:k}){return e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("span",{className:"text-lg text-white",children:t}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-white"})}),e.jsx(b,{children:e.jsx("p",{children:k})})]})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-white",children:r}),e.jsx(A,{checked:d,onCheckedChange:c,className:"bg-gray-200 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-white",children:i})]})]})}function vs({title:t,min:r,max:i,step:d,value:c,onChange:k,tooltipContent:m,leftLabel:a,rightLabel:u}){return e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("span",{className:"text-lg text-white",children:t}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-white"})}),e.jsx(b,{children:e.jsx("p",{children:m})})]})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-blue-500",children:a}),e.jsx(F,{value:[c],max:i,min:r,step:d,className:"mx-4 w-[60%]",onValueChange:([E])=>k(E)}),e.jsx("span",{className:"text-blue-500",children:u})]})]})}function Ns({value:t,onChange:r}){const[i,d]=x.useState(!1),[c,k]=x.useState(!1),[m,a]=x.useState(!1),[u,E]=x.useState(!1),n=({label:f,placeholder:p,showKey:T,setShowKey:V,value:y,onChange:I,tooltipText:P})=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-2 mt-2 flex items-center gap-2",children:[e.jsx("span",{className:"text-lg text-white",children:f}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-white"})}),e.jsx(b,{children:e.jsx("p",{children:P})})]})})]}),e.jsxs("div",{className:"relative",children:[e.jsx(D,{type:T?"text":"password",value:y,onChange:I,placeholder:p,className:"w-full bg-transparent pr-10 text-white"}),e.jsx("button",{type:"button",onClick:()=>V(!T),className:"absolute right-2 top-1/2 -translate-y-1/2 text-white hover:text-gray-300",children:T?e.jsx(Q,{className:"h-5 w-5"}):e.jsx(ee,{className:"h-5 w-5"})})]})]});return e.jsxs("div",{className:"mb-6",children:[e.jsx(n,{label:"Elevenlabs Key",placeholder:"Enter your Elevenlabs API key",showKey:i,setShowKey:d,value:t,onChange:f=>r(f.target.value),tooltipText:"API key for Elevenlabs text-to-speech service"}),e.jsx(n,{label:"Deepgram Key",placeholder:"Enter your Deepgram API key",showKey:c,setShowKey:k,value:t,onChange:f=>r(f.target.value),tooltipText:"API key for Deepgram speech recognition service"}),e.jsx(n,{label:"Anthropic Key",placeholder:"Enter your Anthropic API key",showKey:m,setShowKey:a,value:t,onChange:f=>r(f.target.value),tooltipText:"API key for Anthropic AI service"}),e.jsx(n,{label:"Telnyx Key",placeholder:"Enter your Telnyx API key",showKey:u,setShowKey:E,value:t,onChange:f=>r(f.target.value),tooltipText:"API key for Telnyx telephony service"}),e.jsxs("div",{className:"mb-2 mt-2 flex items-center gap-2",children:[e.jsx("span",{className:"text-lg text-white",children:"Transfer Number"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-white"})}),e.jsx(b,{children:e.jsx("p",{children:"Phone number to transfer calls to"})})]})})]}),e.jsx(D,{type:"tel",value:t,onChange:f=>r(f.target.value),placeholder:"Enter phone number to transfer call to",className:"w-full bg-transparent text-white"})]})}function Le(){return e.jsx("div",{className:"h-[600px] w-full",children:e.jsx("iframe",{src:"https://callagentds.manaknightdigital.com/voice-dialer",className:"h-full w-full border-0",title:"Voice Dialer"})})}function ws({icon:t,title:r,isActive:i,onClick:d}){return e.jsxs("div",{onClick:d,className:`flex cursor-pointer items-center justify-center space-x-3 px-4 py-2 ${i?"text-center font-medium text-white":"text-center text-white hover:text-white"}`,children:[e.jsxs("div",{className:"flex-shrink-0 text-white",children:[J.cloneElement(t,{className:"w-6 h-6"})," "]}),e.jsx("span",{className:"text-center text-sm",children:r})]})}function X({title:t,isActive:r,onClick:i,icon:d=null}){return e.jsxs("button",{onClick:i,className:`flex items-center gap-2 rounded-t-md px-6 py-3 text-sm font-medium transition-colors ${r?"border-l border-r border-t border-gray-600 bg-[#2d3748] text-white":"bg-transparent text-gray-400 hover:bg-[#2d3748]/50 hover:text-gray-300"}`,children:[d||null,t]})}function Ss({open:t,onOpenChange:r}){var B,O,U,l,w,K,z,$;const{dispatch:i}=J.useContext(de),[d,c]=x.useState("calendar"),[k,m]=x.useState(!1),[a,u]=x.useState({interruptSensitivity:!1,responseSpeed:!1,doubleCall:!1,vmDetection:!1,initialDelay:0,aiCreativity:.5,transferNumber:"",callerId:"",llm_settings:{provider:"groq",model_name:"llama-3.3-70b-versatile",temperature:"0.01",max_tokens:"250",top_p:"0.9",system_prompt:""},tts_settings:{provider:"elevenlabs",model_name:"eleven_multilingual_v2"}});J.useEffect(()=>{async function s(){try{m(!0);const o=await C.callRawAPI("/v3/api/custom/voiceoutreach/user/settings",{},"GET");if(console.log(o),o!=null&&o.model){const S=o.model.settings?JSON.parse(o.model.settings):{};let g={provider:"groq",model_name:"llama-3.3-70b-versatile",temperature:"0.01",max_tokens:"250",top_p:"0.9",system_prompt:""};try{o.model.llm_settings&&(g=JSON.parse(o.model.llm_settings))}catch(Y){console.error("Error parsing llm_settings:",Y)}let h={provider:"elevenlabs",model_name:"eleven_multilingual_v2"};try{o.model.tts_settings&&(h=JSON.parse(o.model.tts_settings))}catch(Y){console.error("Error parsing tts_settings:",Y)}console.log(S),S&&u({...S,llm_settings:g,tts_settings:h})}}catch(o){console.log("Error",o)}m(!1)}s()},[]);const E=x.useCallback(Ee(async s=>{const o=JSON.stringify(s);await C.callRawAPI("/v3/api/custom/voiceoutreach/user/settings",{settings:o},"POST")},500),[]),n=s=>{u(s),E(s)},f=async()=>{const s=JSON.stringify(a);await C.callRawAPI("/v3/api/custom/voiceoutreach/user/settings",{settings:s},"POST"),R(i,"Settings saved successfully!",4e3)},p=s=>{c(s)},T={interruptSensitivity:"Controls how easily the AI can be interrupted during conversations",responseSpeed:"Adjusts how quickly the AI responds to user input",initialDelay:"Sets the delay before the first AI response",doubleCall:"Enables double-call verification feature",vmDetection:"Activates voicemail detection capabilities",aiCreativity:"Controls the creativity level of AI responses",callerId:"Set the caller ID name for outbound calls",transferNumber:"Phone number to transfer calls to"},V={groq:{"llama-3.3-70b-versatile":"Fast","meta-llama/Llama-4-Scout-17B-16E-Instruct":"Medium","meta-llama/llama-4-maverick-17b-128e-instruct":"Medium-Fast"},anthropic:{"claude-3-haiku-20240307":"Very Fast","claude-3-5-haiku-20241022":"Fast","claude-3-opus-20240229":"Slow","claude-3-5-sonnet-20240620":"Medium"},"direct-openai":{"gpt-3.5-turbo":"Very Fast","gpt-4o-mini":"Medium"},lambda_ai:{"llama-3.3-70b-instruct":"Very Fast"}},y={groq:{label:"Groq",models:["llama-3.3-70b-versatile","meta-llama/Llama-4-Scout-17B-16E-Instruct","meta-llama/llama-4-maverick-17b-128e-instruct"]},anthropic:{label:"Anthropic",models:["claude-3-haiku-20240307","claude-3-5-haiku-20241022","claude-3-opus-20240229","claude-3-5-sonnet-20240620"]},"direct-openai":{label:"OpenAI",models:["gpt-3.5-turbo","gpt-4o-mini"]},lambda_ai:{label:"Lambda AI",models:["llama-3.3-70B-instruct"]}},I={deepgram:{"nova-3":"Very Fast","nova-3-general":"Fast","nova-3-medical":"Fast","nova-2-phonecall":"Medium"},assemblyai:{"slam-1":"Very Fast","universal-1":"Fast","universal-2":"Medium"}},P={deepgram:{label:"Deepgram",models:["nova-3","nova-3-general","nova-3-medical","nova-2-phonecall"]},assemblyai:{label:"AssemblyAI",models:["slam-1","universal-1","universal-2"]}},L={elevenlabs:{label:"ElevenLabs",models:["eleven_multilingual_v2","eleven_flash_v2"]},orpheus_tts:{label:"Orpheus TTS",models:["canopylabs/orpheus-tts-0.1-finetune-prod"]},kokoro_tts:{label:"Kokoro TTS",models:["base"]}};return e.jsx(e.Fragment,{children:k?e.jsx("div",{className:"flex max-h-fit min-h-fit w-full min-w-full max-w-full items-center justify-center py-5",children:e.jsx(Ie,{size:100,color:"#0EA5E9"})}):e.jsx("div",{className:"mx-auto max-w-3xl bg-[#1d2937] text-white",children:e.jsxs("div",{className:"rounded-lg bg-[#1d2937] bg-opacity-100 p-12 shadow-lg",children:[e.jsx("div",{children:e.jsxs("div",{className:"flex justify-center",children:[e.jsx(X,{icon:e.jsx(ve,{className:"h-4 w-4"}),title:"Calendar & Email Booking",isActive:d==="calendar",onClick:()=>p("calendar")}),e.jsx(X,{icon:e.jsx(Ne,{className:"h-4 w-4"}),title:"Advanced Options",isActive:d==="advanced",onClick:()=>p("advanced")}),e.jsx(X,{icon:e.jsx(we,{className:"h-4 w-4"}),title:"Voice Dialer",isActive:d==="voicedialer",onClick:()=>p("voicedialer")})]})}),e.jsxs("div",{className:"rounded-b-md rounded-tr-md border border-gray-600 bg-[#2d3748] p-6",children:[d==="calendar"&&e.jsx(Me,{}),d==="voicedialer"&&e.jsx(Le,{}),d==="advanced"&&e.jsxs("div",{className:"mx-auto max-w-2xl space-y-6",children:[e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Interrupt Sensitivity"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:T.interruptSensitivity})})]})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Low"}),e.jsx(A,{checked:a.interruptSensitivity,onCheckedChange:s=>n({...a,interruptSensitivity:s}),className:"bg-gray-600 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-sm text-gray-400",children:"High"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Double Call"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:T.doubleCall})})]})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"False"}),e.jsx(A,{checked:a.doubleCall,onCheckedChange:s=>n({...a,doubleCall:s}),className:"bg-gray-600 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-sm text-gray-400",children:"True"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Response Speed"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:T.responseSpeed})})]})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Auto"}),e.jsx(A,{checked:a.responseSpeed,onCheckedChange:s=>n({...a,responseSpeed:s}),className:"bg-gray-600 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Sensitive"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"VM Detection (Beta)"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:T.vmDetection})})]})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Off"}),e.jsx(A,{checked:a.vmDetection,onCheckedChange:s=>n({...a,vmDetection:s}),className:"bg-gray-600 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-sm text-gray-400",children:"On"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Initial Message Delay"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:T.initialDelay})})]})})]}),e.jsxs("span",{className:"text-sm text-white",children:[a.initialDelay,"s"]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"whitespace-nowrap text-sm text-gray-400",children:"0 (sec)"}),e.jsx(F,{value:[a.initialDelay],max:5,min:0,step:.1,className:"w-full",onValueChange:([s])=>n({...a,initialDelay:s})}),e.jsx("span",{className:"whitespace-nowrap text-sm text-gray-400",children:"5 (sec)"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"AI Creativity"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:T.aiCreativity})})]})})]}),e.jsx("span",{className:"text-sm text-white",children:a.aiCreativity})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("span",{className:"pr-[28px] text-sm text-gray-400",children:["0"," "]}),e.jsx(F,{value:[a.aiCreativity],max:1,min:0,step:.01,className:"w-full",onValueChange:([s])=>n({...a,aiCreativity:s})}),e.jsx("span",{className:"pl-[28px] text-sm text-gray-400",children:"1"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Caller ID Name"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:T.callerId})})]})})]}),e.jsx(D,{placeholder:"Caller ID Name",value:a.callerId,onChange:s=>n({...a,callerId:s.target.value}),className:"w-full border-gray-200 bg-transparent text-white placeholder:text-gray-300"})]})]}),e.jsxs("div",{className:"mt-8 space-y-4 border-t border-gray-700 pt-8",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-lg font-medium text-white",children:"LLM Settings"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:"Configure Language Model settings"})})]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Provider"}),e.jsx("select",{value:a.llm_settings.provider,onChange:s=>{const o=s.target.value,S=y[o].models[0];n({...a,llm_settings:{...a.llm_settings,provider:o,model_name:S}})},className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:Object.entries(y).map(([s,{label:o}])=>e.jsx("option",{value:s,className:"bg-[#1d2937]",children:o},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Model"}),e.jsx("select",{value:a.llm_settings.model_name,onChange:s=>n({...a,llm_settings:{...a.llm_settings,model_name:s.target.value}}),className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:y[a.llm_settings.provider].models.map(s=>e.jsxs("option",{value:s,className:"bg-[#1d2937]",children:[s," (",V[a.llm_settings.provider][s],")"]},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-white",children:"Temperature"}),e.jsx("span",{className:"text-sm text-white",children:a.llm_settings.temperature})]}),e.jsx(F,{value:[parseFloat(a.llm_settings.temperature)],max:1,min:0,step:.01,className:"w-full",onValueChange:([s])=>n({...a,llm_settings:{...a.llm_settings,temperature:s.toString()}})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Max Tokens"}),e.jsx(D,{type:"number",value:a.llm_settings.max_tokens,onChange:s=>n({...a,llm_settings:{...a.llm_settings,max_tokens:s.target.value}}),className:"w-full border-gray-600 bg-transparent text-white"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-white",children:"Top P"}),e.jsx("span",{className:"text-sm text-white",children:a.llm_settings.top_p})]}),e.jsx(F,{value:[parseFloat(a.llm_settings.top_p)],max:1,min:0,step:.01,className:"w-full",onValueChange:([s])=>n({...a,llm_settings:{...a.llm_settings,top_p:s.toString()}})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"System Prompt"}),e.jsx("textarea",{value:a.llm_settings.system_prompt,onChange:s=>n({...a,llm_settings:{...a.llm_settings,system_prompt:s.target.value}}),className:"h-24 w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",placeholder:"Enter system prompt..."})]})]}),e.jsxs("div",{className:"mt-8 space-y-4 border-t border-gray-700 pt-8",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-lg font-medium text-white",children:"Speech Recognition Settings"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:"Configure Speech Recognition settings"})})]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Speech Recognition Provider"}),e.jsx("select",{value:(B=a.speech_recognition_settings)==null?void 0:B.provider,onChange:s=>{const o=s.target.value,S=P[o].models[0];n({...a,speech_recognition_settings:{...a.speech_recognition_settings,provider:o,model:S}})},className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:Object.entries(P).map(([s,{label:o}])=>e.jsx("option",{value:s,className:"bg-[#1d2937]",children:o},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Speech Recognition Model"}),e.jsx("select",{value:(O=a.speech_recognition_settings)==null?void 0:O.model,onChange:s=>n({...a,speech_recognition_settings:{...a.speech_recognition_settings,model:s.target.value}}),className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:(l=P[(U=a.speech_recognition_settings)==null?void 0:U.provider])==null?void 0:l.models.map(s=>{var o;return e.jsxs("option",{value:s,className:"bg-[#1d2937]",children:[s," (",I[(o=a.speech_recognition_settings)==null?void 0:o.provider][s],")"]},s)})})]})]}),e.jsxs("div",{className:"mt-8 space-y-4 border-t border-gray-700 pt-8",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-lg font-medium text-white",children:"Text-to-Speech Settings"}),e.jsx(_,{children:e.jsxs(v,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(b,{children:e.jsx("p",{children:"Configure Text-to-Speech settings"})})]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"TTS Provider"}),e.jsx("select",{value:(w=a.tts_settings)==null?void 0:w.provider,onChange:s=>{const o=s.target.value,S=L[o].models[0];n({...a,tts_settings:{...a.tts_settings,provider:o,model_name:S}})},className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:Object.entries(L).map(([s,{label:o}])=>e.jsx("option",{value:s,className:"bg-[#1d2937]",children:o},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"TTS Model"}),e.jsx("select",{value:(K=a.tts_settings)==null?void 0:K.model_name,onChange:s=>n({...a,tts_settings:{...a.tts_settings,model_name:s.target.value}}),className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:($=L[(z=a.tts_settings)==null?void 0:z.provider])==null?void 0:$.models.map(s=>e.jsx("option",{value:s,className:"bg-[#1d2937]",children:s},s))})]})]})]})]}),d=="advanced"&&e.jsx("div",{className:"mt-8 flex w-full justify-center",children:e.jsx("button",{className:"focus:shadow-outline w-full rounded bg-[#19b2f6]/80 px-4 py-2 font-bold text-white hover:bg-[#19b2f6]/60 focus:outline-none",onClick:f,children:"Save Settings"})})]})})})}export{_s as SettingRow,ws as SettingSection,Ss as SettingsModal,vs as SliderSetting,Ns as TransferSetting,Le as VoiceDialerSettings,Ss as default};
