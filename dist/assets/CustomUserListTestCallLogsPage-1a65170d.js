import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as l,f as ne,i as oe,r as f}from"./vendor-2ae44a2e.js";import{A as ce,G as de,T as xe,t as he,s as pe,B as me,a as ue,b as ge,l as fe,R as je,c as be,m as ve,n as ye,P as Ne,X as we}from"./index-b2ff2fa1.js";import{u as Ce}from"./react-hook-form-47c010f8.js";import{C as Se}from"./react-papaparse-b60a38ab.js";import{X as ke,e as U,f as G}from"./lucide-react-1246a7ed.js";import{C as j,q as b,_ as S}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./papaparse-2d1475f9.js";const k={0:"Inactive",1:"Active",2:"Paused"},W={inactive:0,active:1,paused:2,INACTIVE:0,ACTIVE:1,PAUSED:2,Inactive:0,Active:1,Paused:2},_=[{header:"Action",accessor:""},{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Campaign ID",accessor:"campaign_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Call ID",accessor:"call_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Recording Link",accessor:"recording_link",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Duration (sec)",accessor:"duration",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:k},{header:"Credit Used",accessor:"cost",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}}],X=_.filter(A=>!["","recording_link"].includes(A.accessor)),We=()=>{const{state:A,dispatch:D}=l.useContext(ce),{dispatch:T}=l.useContext(de);ne();const[n,m]=l.useState([]),[v,d]=l.useState([]),[x,P]=l.useState(""),[F,$]=l.useState(!1),[E,I]=l.useState(!1),[u,H]=l.useState([]),[c,K]=oe(),{CSVDownloader:J}=Se(),[Q,Y]=l.useState({currentPage:0,pageSize:0,totalNumber:0,totalPages:0});Ce({defaultValues:{}});const[V,h]=l.useState({}),[y,Z]=l.useState("eq"),[R,L]=l.useState(null),[ee,q]=l.useState(!1),[te,se]=l.useState(null);l.useEffect(()=>{T({type:"SETPATH",payload:{path:"test_call_logs"}}),O()},[v,x]);const p=(t,i,s)=>{if(!s){d(o=>o.filter(g=>!g.startsWith(t+",")));return}let a=s,r=i||"eq";if(t==="status"){const o=s.toLowerCase();W.hasOwnProperty(o)&&(a=W[o])}const N=`${t},${r},${a}`;d(o=>[...o.filter(w=>!w.startsWith(t+",")),N])};async function O(){try{I(!0);const t=new xe,i=`${t.getProjectId()}_call_logs`;let s=["type,eq,3",c.get("campaign_id")?`${i}.campaign_id,eq,${c.get("campaign_id")}`:void 0].filter(Boolean);if(x&&s.push(`${i}.call_id,cs,${x}`),v.length>0){const le=v.map(re=>{const[C,M,z]=re.split(",");return C==="duration"?`${i}.${C},${M},${parseFloat(z)}`:`${i}.${C},${M},${z}`});s=[...s,...le]}const a=await t.getPaginate("call_logs",{size:c.get("limit")??50,page:c.get("page")??1,filter:s.filter(Boolean)}),{list:r,total:N,limit:o,num_pages:g,page:w}=a;H(r),Y({currentPage:w,pageSize:o,totalNumber:N,totalPages:g})}catch(t){console.log("ERROR",t),he(D,t.message),pe(T,t.message,5e3,"error")}I(!1)}const ae=()=>{m([]),d([]),h({});for(const t of c.keys())t!=="campaign_id"&&c.delete(t);K(c),O()},B=(t,i)=>{navigator.clipboard.writeText(t),L(i),setTimeout(()=>L(null),2e3),console.log(u)},ie=({isOpen:t,onClose:i,row:s})=>(console.log(s),s?e.jsx(b,{appear:!0,show:t,as:f.Fragment,children:e.jsxs(S,{as:"div",className:"relative z-[100]",onClose:i,children:[e.jsx(b.Child,{as:f.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4",children:e.jsx(b.Child,{as:f.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(S.Panel,{className:"w-full max-w-3xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(S.Title,{className:"text-xl font-medium text-white",children:"Call Details"}),e.jsx("button",{onClick:i,children:e.jsx(we,{className:"h-6 w-6 text-white/70 hover:text-white"})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Call ID"}),e.jsx("p",{className:"text-white",children:(s==null?void 0:s.call_id)??"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Campaign ID"}),e.jsx("p",{className:"text-white",children:s.campaign_id??"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Duration"}),e.jsx("p",{className:"text-white",children:s.duration??"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Status"}),e.jsx("p",{className:"text-white",children:k[s.status]??"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Cost"}),e.jsx("p",{className:"text-white",children:s.cost?`$${s.cost.toFixed(2)}`:"N/A"})]})]}),s.recording_link?e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm text-white/70",children:"Recording Link"}),e.jsx("div",{className:"text-white",children:s.recording_link&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:a=>{a.preventDefault(),B(s.recording_link,s.id)},className:"flex items-center rounded bg-[#2d3947] p-1.5 transition-colors hover:bg-[#19b2f6]/20",title:"Copy link",children:R===s.id?e.jsx(U,{className:"h-4 w-4 text-green-400"}):e.jsx(G,{className:"h-4 w-4 text-white/70 hover:text-white"})}),e.jsx("p",{className:"flex items-center gap-2",children:e.jsx("a",{className:"text-white hover:underline",href:s.recording_link,target:"_blank",rel:"noopener noreferrer",children:s.recording_link?s.recording_link.length>50?`${s.recording_link.substring(0,50)}...`:s.recording_link:"N/A"})})]})})]}):null]})]})})})})]})}):null);return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5",children:[e.jsx("h3",{className:"my-4 px-8 text-2xl font-bold text-white",children:"Test Call Logs"}),e.jsxs("div",{className:"bg-[#1d2937] px-8 py-4",children:[e.jsxs("div",{className:"flex h-fit items-center justify-between gap-3",children:[e.jsx("div",{className:"flex w-[200px] min-w-[200px] items-center justify-between",children:e.jsx("div",{className:"relative z-10 rounded bg-[#1d2937]",children:e.jsxs(j,{children:[e.jsxs("div",{className:"flex items-center gap-4 bg-[#1d2937] text-white",children:[e.jsxs(j.Button,{className:"border-white/50ss flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white  focus-visible:outline-0 focus-visible:outline-transparent",children:[e.jsx(me,{}),e.jsx("span",{children:"Filters"}),n.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:n.length})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white focus-within:border-gray-400",children:[e.jsx(ue,{className:"text-xl text-white"}),e.jsx("input",{type:"text",placeholder:"Search by call ID",className:"border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none",style:{boxShadow:"0 0 transparent"},value:x,onChange:t=>{P(t.target.value),p("call_id","cs",t.target.value)}}),x&&e.jsx(ge,{className:"cursor-pointer text-lg text-white",onClick:()=>{P(""),p("call_id","cs","")}})]})]}),e.jsx(b,{as:f.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(j.Panel,{children:e.jsxs("div",{className:"filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10",children:[e.jsx("span",{className:"absolute left-5 top-2 font-medium text-white",children:"Filters"}),e.jsx(j.Button,{onClick:()=>{console.log("clicked"),m([]),d([]),h({})},children:e.jsx(ke,{className:"absolute right-2 top-2 cursor-pointer text-white"})}),n==null?void 0:n.map((t,i)=>{var s;return e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("button",{type:"button",className:"block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none",title:t,style:{WebkitTouchCallout:"none"},children:((s=X.find(a=>a.accessor===t))==null?void 0:s.header)||t}),e.jsxs("select",{className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none",value:y,onChange:a=>{Z(a.target.value),p(t,a.target.value,V[t])},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),t==="status"?e.jsxs("select",{value:V[t]||"",onChange:a=>{h(r=>({...r,[t]:a.target.value})),p(t,y,a.target.value)},className:"h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",children:[e.jsx("option",{value:"",children:"Select Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]}):e.jsx(fe,{type:"text",labelClassName:"!mb-0",placeholder:"Enter value",setValue:()=>{},showIcon:!1,className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",onReady:a=>p(t,y,a)}),e.jsx(je,{className:"cursor-pointer text-2xl text-red-600",onClick:()=>{m(a=>a.filter(r=>r!==t)),d(a=>a.filter(r=>!r.includes(t))),h(a=>{const r={...a};return delete r[t],r})}})]},i)}),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>$(!F),children:[e.jsx(be,{}),"Add filter"]}),F&&e.jsx("div",{className:"absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:X.map(t=>e.jsx("li",{className:`${n.includes(t.accessor)?"cursor-not-allowed text-gray-100":"cursor-pointer text-gray-400 hover:text-white"}`,onClick:()=>{n.includes(t.accessor)||(m(i=>[...i,t.accessor]),h(i=>({...i,[t.accessor]:""}))),$(!1)},children:t.header},t.accessor))})}),n.length>0&&e.jsx("div",{onClick:ae,className:"inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white",children:"Clear all filter"})]})]})})})]})})}),e.jsx(J,{filename:"test_call_logs",className:"relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]",data:()=>u,children:"Download CSV"})]}),e.jsx("div",{className:"mt-4 overflow-x-auto  bg-[#1d2937]",children:e.jsx("div",{className:E?"":"overflow-x-auto border-b border-gray-200 shadow",children:E&&u.length===0?e.jsx(ve,{columns:_}):e.jsxs("table",{className:"min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]",children:[e.jsx("thead",{className:"bg-[#1d2937]",children:e.jsx(ye,{actionPosition:"onTable",onSort:()=>{},columns:_,actions:{view:{show:!0}}})}),e.jsx("tbody",{className:"divide-y divide-gray-400 bg-[#1d2937]",children:u.map(t=>e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex items-center justify-between gap-3 text-sm",children:e.jsx("button",{className:"rounded-[30px] bg-[#19b2f6]/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-[#19b2f6]/30",onClick:()=>{se(t),q(!0)},children:"View"})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.id}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.campaign_id??"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.call_id??"N/A"}),e.jsx("td",{className:"px-6 py-4 text-white",children:t.recording_link&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:i=>{i.preventDefault(),B(t.recording_link,t.id)},className:"flex items-center rounded bg-[#2d3947] p-1.5 transition-colors hover:bg-[#19b2f6]/20",title:"Copy link",children:R===t.id?e.jsx(U,{className:"h-4 w-4 text-green-400"}):e.jsx(G,{className:"h-4 w-4 text-white/70 hover:text-white"})}),e.jsx("p",{className:"flex items-center gap-2",children:e.jsx("a",{className:"text-white hover:underline",href:t.recording_link,target:"_blank",rel:"noopener noreferrer",children:t.recording_link?t.recording_link.length>50?`${t.recording_link.substring(0,50)}...`:t.recording_link:"N/A"})})]})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.duration??"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:k[t.status]??"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.cost?`$${t.cost.toFixed(2)}`:"N/A"})]},t.id))})]})})}),e.jsx(Ne,{paginationData:Q})]})]}),e.jsx(ie,{isOpen:ee,onClose:()=>q(!1),row:te})]})};export{We as default};
