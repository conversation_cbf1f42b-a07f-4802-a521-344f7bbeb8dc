import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as b,f as Y,h as V,r as h}from"./vendor-2ae44a2e.js";import{u as Q}from"./react-hook-form-47c010f8.js";import{o as X}from"./yup-5abd4662.js";import{c as Z,a as r}from"./yup-5c93ed04.js";import{M as ee,A as se,G as te,t as P,s as v}from"./index-b2ff2fa1.js";import{S as ae}from"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let d=new ee;const Te=({activeId:u,setSidebar:y,closeSidebar:w})=>{var E,T,C,O,R,I,q,F;const L=Z({firstName:r().required(),lastName:r().required(),email:r().email().required(),password:r(),role:r(),status:r().required(),telephonicService:r().required(),speechRecognition:r().required(),textToSpeech:r().required(),llm:r().required()}).required(),{dispatch:N,state:oe}=b.useContext(se),{dispatch:x}=b.useContext(te);Y(),V();const[J,B]=h.useState(""),[le,D]=h.useState(0),[j,k]=h.useState(!1),[U,G]=h.useState(null),[W,S]=h.useState(null),{register:i,handleSubmit:_,setError:g,setValue:c,formState:{errors:m}}=Q({resolver:X(L)}),$=[{key:"0",value:"Inactive"},{key:"2",value:"Suspend"},{key:"1",value:"Active"}],H=[{name:"Twilio",value:"twilio"},{name:"Telynx",value:"telynx"}],M=[{name:"Deepgram",value:"deepgram"}],z=[{name:"Eleven Labs",value:"elevenlabs"},{name:"Azure Speech SDK",value:"azure"}],K=[{name:"Claude Haiku",value:"claude-3-haiku-20240307"},{name:"GPT-3.5",value:"ai_energy-3-turbo"}],A=async s=>{k(!0);try{if(J!==s.email){const t=await d.updateEmailByAdmin(s.email,u);if(!t.error)v(x,"Email Updated",1e3);else if(t.validation){const l=Object.keys(t.validation);for(let n=0;n<l.length;n++){const p=l[n];g(p,{type:"manual",message:t.validation[p]})}}}if(s.password.length>0){const t=await d.updatePasswordByAdmin(s.password,u);if(!t.error)v(x,"Password Updated",2e3);else if(t.validation){const l=Object.keys(t.validation);for(let n=0;n<l.length;n++){const p=l[n];g(p,{type:"manual",message:t.validation[p]})}}}d.setTable("user");const o=await d.callRestAPI({id:u,email:s.email,role:s.role,status:s.status,first_name:s.firstName,last_name:s.lastName},"PUT"),f={id:U,phone_service:s.telephonicService,tts_settings:JSON.stringify({provider:s.textToSpeech,voice_id:"qzsSemAullBuJxvBTx9G",speed:"normal",output_format:"ulaw_8000"}),llm_settings:JSON.stringify({provider:s.llm.includes("claude")?"anthropic":"openai",temperature:"0.01",max_tokens:"128",top_p:"0.9",model_name:s.llm,system_prompt:"You are a customer care agent for an insurance company, making outbound calls. Follow the decision tree provided below and decide which response best suits what the lead says. Always stick to the script, picking the best response at all times to keep the conversation going. You are Alice, and the user will respond as the lead. I will respond as the lead; just provide responses and decide which is next in the tree from the responses I give. Start with the introduction. Don't say the headers like introduction or any other ones in between **. Just say what is under them for the right situation. Don't include numbers, rather spell it. If you use any acronym, also spell it out.\\n\\n---\\n\\nHi, this is Alice from ABC. Do you have a minute to discuss how you can save on your insurance?\\n\\n---\\n\\nI’d love to tell you about our latest promotion. Are you interested in saving on auto, home, or life insurance?\\n\\n---\\n\\nCould you provide some basic details like your age, and location?\\n\\n---\\n\\nBased on your information, you could save up to [specific amount] annually on [insurance type]. Would you like to hear more about these savings or perhaps get a detailed quote?\\n\\n---\\n\\nGreat! I’ll need a few more details to prepare a personalized quote for you. Could you tell me more about your current coverage?\\n\\n---\\n\\nOur plans offer great coverage options tailored to your needs, with significant savings. Would you like me to walk you through some of the benefits and coverage options?\\n\\n---\\n\\nIs there anything else I can help you with today?\\n\\n---\\n\\nThank you for your time. We appreciate your interest in our services and look forward to helping you save on your insurance.\\n\\n---\\n\\nAlright, everything is all set! We'll be in touch soon. We are looking forward to helping you save on your insurance!",first_message:"Hello, Stacy! I'm Alice from ABC Insurance company. I can help you save on the insurance, do you have a minute?"}),telephony_settings:JSON.stringify({provider:s.telephonicService}),speech_recognition_settings:JSON.stringify({provider:s.speechRecognition,model:"nova-2-phonecall",encoding:"mulaw",sample_rate:8e3}),stt_service:s.speechRecognition,llm_service:s.llm};d.setTable("user_settings");const a=await d.callRestAPI(f,"PUT");if(!o.error)v(x,"User Updated",4e3),w&&w();else if(o.validation){const t=Object.keys(o.validation);for(let l=0;l<t.length;l++){const n=t[l];g(n,{type:"manual",message:o.validation[n]})}}}catch(o){console.log("Error",o),g("email",{type:"manual",message:o.message}),P(N,o.message)}k(!1)};return b.useEffect(()=>{x({type:"SETPATH",payload:{path:"users"}}),async function(){var s,o,f;try{S(!0),d.setTable("user");const a=await d.callRawAPI("/v3/api/custom/voiceoutreach/admin/settings/"+u,{},"GET");let t=a.model;try{t={...t,tts_settings:JSON.parse(t.tts_settings),speech_recognition_settings:JSON.parse(t.speech_recognition_settings),llm_settings:JSON.parse(t.llm_settings)}}catch{}a.error||(c("firstName",a.model.first_name),c("lastName",a.model.last_name),c("email",a.model.email),c("role",a.model.role),c("status",a.model.status),c("telephonicService",t==null?void 0:t.phone_service),c("speechRecognition",(s=t==null?void 0:t.speech_recognition_settings)==null?void 0:s.provider),c("textToSpeech",(o=t==null?void 0:t.tts_settings)==null?void 0:o.provider),c("llm",(f=t==null?void 0:t.llm_settings)==null?void 0:f.model_name),B(a.model.email),D(a.model.id),G(t.id))}catch(a){console.log("Error",a),P(N,a.message)}S(!1)}()},[u]),e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>y(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Edit Customer"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]",onClick:()=>y(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm",onClick:async()=>{await _(A)(),y(!1)},disabled:j,children:j?"Saving":"Save"})]})]}),W?e.jsx(ae,{}):e.jsxs("form",{className:"form-container w-full max-w-lg p-4 text-left",onSubmit:_(A),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"firstName",children:"First Name"}),e.jsx("input",{type:"text",placeholder:"First Name",...i("firstName"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(E=m.firstName)!=null&&E.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(T=m.firstName)==null?void 0:T.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"lastName",children:"Last Name"}),e.jsx("input",{type:"text",placeholder:"Last Name",...i("lastName"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(C=m.lastName)!=null&&C.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(O=m.lastName)==null?void 0:O.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",...i("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(R=m.email)!=null&&R.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(I=m.email)==null?void 0:I.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...i("status"),children:$.map(s=>e.jsx("option",{name:"status",value:s.key,children:s.value},s.key))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"telephonicService",children:"Telephonic Service"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...i("telephonicService"),children:H.map(s=>e.jsx("option",{name:"telephonicService",value:s.value,children:s.name},s.value))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"speechRecognition",children:"Speech Recognition"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...i("speechRecognition"),children:M.map(s=>e.jsx("option",{name:"speechRecognition",value:s.value,children:s.name},s.value))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"textToSpeech",children:"Text to Speech"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...i("textToSpeech"),children:z.map(s=>e.jsx("option",{name:"textToSpeech",value:s.value,children:s.name},s.value))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"llm",children:"LLM"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...i("llm"),children:K.map(s=>e.jsx("option",{name:"llm",value:s.value,children:s.name},s.value))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"******************",...i("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(q=m.password)!=null&&q.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(F=m.password)==null?void 0:F.message})]})]})]})};export{Te as default};
