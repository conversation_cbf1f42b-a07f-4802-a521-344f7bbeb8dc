import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as o,f as k,h as I,r as P}from"./vendor-2ae44a2e.js";import{u as A}from"./react-hook-form-47c010f8.js";import{o as C}from"./yup-5abd4662.js";import{c as q,a as w}from"./yup-5c93ed04.js";import{M as R,A as D,G as M,t as G,s as L}from"./index-b2ff2fa1.js";import"./react-quill-d06fcfc9.js";import{M as y}from"./MkdInput-a584fac2.js";import{I as U}from"./InteractiveButton-bff38983.js";import{S as V}from"./index-f2c2b086.js";import{b as _}from"./index.esm-4b383179.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-1246a7ed.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let r=new R;const he=({activeId:i,closeSidebar:a})=>{const{dispatch:j}=o.useContext(D),v=q({name:w().required("Name is required"),description:w().required("Description is required")}).required(),{dispatch:p}=o.useContext(M);o.useState({});const[f,l]=o.useState(!1),[N,n]=o.useState(!1),E=k(),{register:d,handleSubmit:S,setError:u,setValue:x,formState:{errors:h}}=A({resolver:C(v)}),m=I();P.useEffect(function(){(async function(){try{n(!0),r.setTable("voice_list");const t=await r.callRestAPI({id:i||Number(m==null?void 0:m.id)},"GET");console.log(t,"<---"),t.error||(x("name",t.model.name),x("description",t.model.description)),n(!1)}catch(t){n(!1),console.log("error",t),G(j,t.message)}})()},[]);const T=async t=>{l(!0);try{r.setTable("voice_list");const s=await r.callRestAPI({id:i,name:t.name,description:t.description},"PUT");if(!s.error)L(p,"Updated"),E("/user/voice_list");else if(s.validation){const g=Object.keys(s.validation);for(let c=0;c<g.length;c++){const b=g[c];u(b,{type:"manual",message:s.validation[b]})}}a&&a(),l(!1)}catch(s){l(!1),console.log("Error",s),u("name",{type:"manual",message:s.message})}};return o.useEffect(()=>{p({type:"SETPATH",payload:{path:"voice_list"}})},[]),e.jsxs("div",{className:"flex flex-col h-fit",children:[e.jsxs("div",{className:"flex flex-row justify-between",children:[e.jsx("h4",{className:"text-3xl font-semibold text-white",children:"Edit Voice Name"}),e.jsx("button",{onClick:a,children:e.jsx(_,{className:"text-2xl text-white"})})]}),N?e.jsx("div",{className:"flex justify-center items-center py-5 w-full min-w-full max-w-full max-h-fit min-h-fit",children:e.jsx(V,{size:100,color:"#0EA5E9"})}):e.jsxs("form",{className:"flex flex-col flex-grow mt-7 w-full",onSubmit:S(T),children:[e.jsx(y,{type:"text",page:"edit",name:"name",errors:h,label:"Name",placeholder:"name",register:d,className:"text-white bg-transparent placeholder:text-gray-300"}),e.jsx(y,{type:"textarea",page:"edit",name:"description",errors:h,label:"Description",placeholder:"description",register:d,className:"text-white bg-transparent placeholder:text-gray-300",containerClassName:"flex flex-col flex-grow",rows:6}),e.jsx(U,{type:"submit",className:"focus:shadow-outline w-fit rounded bg-[#19b2f6]/80 px-4 py-2 font-semibold text-white focus:outline-none",loading:f,disable:f,children:"Submit"})]})]})};export{he as default};
