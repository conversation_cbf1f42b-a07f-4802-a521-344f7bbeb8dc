import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as i,i as ie,r as ne,L}from"./vendor-2ae44a2e.js";import{M as le,A as oe,G as ce,T as de,t as T,s as g,B as pe,a as ue,b as me,l as xe,R as he,c as ge,m as fe,n as be,w as we,P as je,x as ye}from"./index-b2ff2fa1.js";import{A as ve}from"./index-e429b426.js";import{C as Se}from"./CustomDeleteModal-536cae45.js";import{u as Ne}from"./react-hook-form-47c010f8.js";import{X as Ce}from"./lucide-react-1246a7ed.js";import{A as Ae}from"./ArrowDownTrayIcon-efed7b41.js";import{C as j,q as Pe}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let y=new le;const G={0:"Inactive",1:"Active",2:"Paused"},U={inactive:0,active:1,paused:2,INACTIVE:0,ACTIVE:1,PAUSED:2,Inactive:0,Active:1,Paused:2},E=[{header:"Action",accessor:""},{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Assistant Name",accessor:"assistant_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"# of contacts",accessor:"num_of_contacts",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"# Sent",accessor:"total_sms",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:G}],B=E.filter(v=>!["","assistant_name","num_of_contacts"].includes(v.accessor)),z={INACTIVE:0,ACTIVE:1,PAUSED:2},Qe=()=>{const{state:v,dispatch:f}=i.useContext(oe),{dispatch:c}=i.useContext(ce),[W,_]=i.useState(!1),[l,b]=i.useState([]),[S,p]=i.useState([]),[u,k]=i.useState(""),[I,V]=i.useState(!1),[N,d]=i.useState(!1),[$,K]=i.useState([]),[H,C]=i.useState(!1),[F,M]=i.useState(null),[R,De]=ie(),[J,X]=i.useState({currentPage:0,pageSize:0,totalNumber:0,totalPages:0});Ne({defaultValues:{}});const[O,m]=i.useState({}),[A,Q]=i.useState("eq");i.useEffect(()=>{c({type:"SETPATH",payload:{path:"sms_outbound_campaigns"}}),h()},[S,u]);const x=(t,a,r)=>{if(!r){p(o=>o.filter(w=>!w.startsWith(t+",")));return}let s=r,n=a||"eq";if(t==="status"){const o=r.toLowerCase();U.hasOwnProperty(o)&&(s=U[o])}else n==="eq"?s=r:s=r.toLowerCase();const P=`${t},${n},${s}`;p(o=>[...o.filter(D=>!D.startsWith(t+",")),P])};async function h(){try{d(!0);const t=new de,a=`${t.getProjectId()}_campaign`;let r=[`${a}.campaign_type,eq,3`,`${a}.user_id,eq,${v.user}`];if(u&&r.push(`${a}.name,cs,${u}`),S.length>0){const ee=S.map(te=>{const[se,ae,re]=te.split(",");return`${a}.${se},${ae},${re}`});r=[...r,...ee]}const s=await t.getPaginate("campaign",{size:R.get("limit")??50,page:R.get("page")??1,filter:r,join:"assistants|assistant_id"}),{list:n,total:P,limit:o,num_pages:w,page:D}=s;K(n),X({currentPage:D,pageSize:o,totalNumber:P,totalPages:w})}catch(t){console.log("ERROR",t),T(f,t.message),g(c,t.message,5e3,"error")}d(!1)}async function q(t,a){d(!0);try{y.setTable("campaign"),await y.callRestAPI({status:a,id:t},"PUT"),g(c,"Updated"),h()}catch(r){T(f,r.message),g(c,r.message,5e3,"error")}d(!1)}async function Y(t){d(!0);try{y.setTable("campaign"),await y.callRestAPI({id:t},"DELETE"),g(c,"Deleted"),h()}catch(a){T(f,a.message),g(c,a.message,5e3,"error")}d(!1)}const Z=()=>{b([]),p([]),m({}),h()};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5",children:[e.jsx("h3",{className:"my-4 px-8 text-2xl font-bold text-white",children:"SMS Outbound Campaigns"}),e.jsxs("div",{className:"bg-[#1d2937] px-8 py-4",children:[e.jsxs("div",{className:"flex h-fit items-center justify-between gap-3",children:[e.jsx("div",{className:"flex w-[200px] min-w-[200px] items-center justify-between",children:e.jsx("div",{className:"relative z-10 rounded bg-[#1d2937]",children:e.jsxs(j,{children:[e.jsxs("div",{className:"flex items-center gap-4 bg-[#1d2937] text-white",children:[e.jsxs(j.Button,{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white focus-visible:outline-0 focus-visible:outline-transparent",children:[e.jsx(pe,{}),e.jsx("span",{children:"Filters"}),l.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:l.length})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white focus-within:border-gray-400",children:[e.jsx(ue,{className:"text-xl text-white"}),e.jsx("input",{type:"text",placeholder:"search by name",className:"border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none",style:{boxShadow:"0 0 transparent"},value:u,onChange:t=>{k(t.target.value),x("name","cs",t.target.value)}}),u&&e.jsx(me,{className:"cursor-pointer text-lg text-white",onClick:()=>{k(""),x("name","cs","")}})]})]}),e.jsx(Pe,{as:ne.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(j.Panel,{children:e.jsxs("div",{className:"filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10",children:[e.jsx("span",{className:"absolute left-5 top-2 font-medium text-white",children:"Filters"}),e.jsx(j.Button,{onClick:()=>{console.log("clicked"),b([]),p([]),m({})},children:e.jsx(Ce,{className:"absolute right-2 top-2 cursor-pointer text-white"})}),l==null?void 0:l.map((t,a)=>{var r;return e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("button",{type:"button",className:"block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none",title:t,style:{WebkitTouchCallout:"none"},children:((r=B.find(s=>s.accessor===t))==null?void 0:r.header)||t}),e.jsxs("select",{className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none",value:A,onChange:s=>{Q(s.target.value),x(t,s.target.value,O[t])},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),t==="status"?e.jsxs("select",{value:O[t]||"",onChange:s=>{m(n=>({...n,[t]:s.target.value})),x(t,A,s.target.value)},className:"h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",children:[e.jsx("option",{value:"",children:"Select Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]}):e.jsx(xe,{type:"text",labelClassName:"!mb-0",placeholder:"Enter value",setValue:()=>{},showIcon:!1,className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",onReady:s=>x(t,A,s)}),e.jsx(he,{className:"cursor-pointer text-2xl text-red-600",onClick:()=>{b(s=>s.filter(n=>n!==t)),p(s=>s.filter(n=>!n.includes(t))),m(s=>{const n={...s};return delete n[t],n})}})]},a)}),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>V(!I),children:[e.jsx(ge,{}),"Add filter"]}),I&&e.jsx("div",{className:"absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:B.map(t=>e.jsx("li",{className:`${l.includes(t.accessor)?"cursor-not-allowed text-gray-100":"cursor-pointer text-gray-400 hover:text-white"}`,onClick:()=>{l.includes(t.accessor)||(b(a=>[...a,t.accessor]),m(a=>({...a,[t.accessor]:""}))),V(!1)},children:t.header},t.accessor))})}),l.length>0&&e.jsx("div",{onClick:Z,className:"inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white",children:"Clear all filter"})]})]})})})]})})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("a",{className:"relative flex h-9 items-center justify-center gap-2 rounded-md border border-[#19b2f6]/80 bg-transparent px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]/20",href:"/contact_template.csv",download:"template.csv",target:"_blank",children:[e.jsx(Ae,{className:"h-4 w-4",strokeWidth:2}),"Contact Template"]}),e.jsx(ve,{onClick:()=>{_(!0)},showChildren:!0,className:"relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]",children:"Add New"})]})]}),e.jsx("div",{className:"mt-4 overflow-x-auto  bg-[#1d2937]",children:e.jsx("div",{className:"overflow-x-auto border-b border-gray-400",children:N&&$.length===0?e.jsx(fe,{columns:E}):e.jsxs("table",{className:"min-w-full divide-y  divide-gray-400 rounded border border-b-0 border-gray-400 bg-[#1d2937] bg-[#1d2937]",children:[e.jsx("thead",{className:"bg-[#1d2937]",children:e.jsx(be,{actionPosition:"onTable",onSort:()=>{},columns:E,actions:{view:{show:!0}}})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-[#1d2937]",children:$.map(t=>{var a;return e.jsxs("tr",{className:"text-white",children:[e.jsx("td",{className:"pl-3",children:e.jsxs("div",{className:"flex max-w-[520px] items-center justify-between gap-3 text-sm",children:[t.status===1?e.jsx("button",{title:"Pause Campaign",className:"rounded-[30px] bg-orange-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-orange-500/30",disabled:N,onClick:()=>q(t.id,z.PAUSED),children:"Pause"}):null,t.status!==1?e.jsx("button",{title:"Start Campaign",className:"rounded-[30px] bg-green-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-green-500/30",disabled:N,onClick:()=>q(t.id,z.ACTIVE),children:"Start"}):null,e.jsx("button",{title:"Delete Campaign",className:"rounded-[30px] bg-red-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-red-500/30",onClick:()=>{C(!0),M(t.id)},children:"Delete"}),e.jsx(L,{to:`/user/sms_outbound_logs?campaign_id=${t.id}`,children:e.jsx("button",{title:"View SMS Logs",className:"whitespace-nowrap rounded-[30px] bg-[#19b2f6]/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-[#19b2f6]/30",type:"button",children:"View Logs"})}),e.jsx(L,{to:`/user/campaign-summary?campaign_id=${t.id}`,children:e.jsx("button",{title:"View Summary",className:"whitespace-nowrap rounded-[30px] bg-purple-500/20 p-1.5 px-5 font-medium text-white transition-colors",type:"button",children:"View Summary"})})]})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.id}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.name}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:((a=t.assistants)==null?void 0:a.assistant_name)??"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("p",{className:"max-w-3xl truncate",children:we(t.contacts,[]).length})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.total_sms??"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:G[t.status]??"N/A"})]},t.id)})})]})})}),e.jsx(je,{paginationData:J})]})]}),e.jsx(ye,{closeSidebar:()=>{_(!1),h()},isOpen:W}),e.jsx(Se,{isOpen:H&&!!F,closeModal:()=>C(!1),onDelete:async()=>{await Y(F),M(null),C(!1)}})]})};export{Qe as default};
