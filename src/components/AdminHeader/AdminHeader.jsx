import React, { useRef, useEffect } from "react";
import { Link, NavLink, useNavigate } from "react-router-dom";
import {
  PiUsersThreeFill,
  PiToolboxBold,
  PiBugDuotone,
  PiFolderSimpleUserDuotone,
  PiContactlessPaymentBold,
  PiListNumbersBold,
  PiReceiptBold,
  PiSignOutBold,
  PiCaretDownBold,
  PiCaretUpBold,
} from "react-icons/pi";
import MkdSDK from "Utils/MkdSDK";
import { MdDashboard } from "react-icons/md";
import { GlobalContext } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
let sdk = new MkdSDK();

const NAV_ITEMS = [
  {
    to: "/admin/customers",
    text: "Customers",
    icon: <PiFolderSimpleUserDuotone className="text-xl text-[#A8A8A8]" />,
    value: "customers",
  },
  {
    to: "/admin/admins",
    text: "Admins",
    icon: <PiFolderSimpleUserDuotone className="text-xl text-[#A8A8A8]" />,
    value: "admins",
  },
  {
    to: "/admin/forwarding-numbers",
    text: "Forwarding Numbers",
    icon: <PiListNumbersBold className="text-xl text-[#A8A8A8]" />,
    value: "forwarding",
  },
  {
    to: "/admin/reports",
    text: "Bug Reports",
    icon: <PiBugDuotone className="text-xl text-[#A8A8A8]" />,
    value: "bugs",
  },
  {
    to: "/admin/invoices",
    text: "Invoices",
    icon: <PiReceiptBold className="text-xl text-[#A8A8A8]" />,
    value: "invoices",
  },
  {
    to: "/admin/stripe_product",
    text: "Stripe Products",
    icon: <PiContactlessPaymentBold className="text-xl text-[#A8A8A8]" />,
    value: "products",
  },
  {
    to: "/admin/stripe_price",
    text: "Stripe Price",
    icon: <PiContactlessPaymentBold className="text-xl text-[#A8A8A8]" />,
    value: "prices",
  },
];

const DROPDOWN_ITEMS = [
  {
    to: "/admin/profile",
    text: "Profile",
    icon: <PiUsersThreeFill className="text-xl" />,
  },
  {
    to: "/admin/settings",
    text: "Settings",
    icon: <PiToolboxBold className="text-xl" />,
  },
];

export const AdminHeader = () => {
  const navigate = useNavigate();
  const {
    state: { path },
    dispatch: globalDispatch,
  } = React.useContext(GlobalContext);
  const { state: authState, dispatch } = React.useContext(AuthContext);
  const [openDropdown, setOpenDropdown] = React.useState(false);
  const dropdownRef = useRef(null);

  const handleLogout = async () => {
    dispatch({ type: "LOGOUT" });
    navigate("/admin/login");
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  React.useEffect(() => {
    async function fetchData() {
      try {
        const result = await sdk.getProfile();
        dispatch({
          type: "UPDATE_PROFILE",
          payload: result,
        });
      } catch (error) {
        console.log("Error", error);
        tokenExpireError(
          dispatch,
          error.response.data.message
            ? error.response.data.message
            : error.message
        );
      }
    }

    fetchData();
  }, []);

  return (
    <nav className="fixed top-0 z-50  w-full border-b border-[#E0E0E0] bg-white px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/" className="text-lg font-bold text-[#393939]">
            voiceoutreach
          </Link>
        </div>

        <div className="flex-1 px-8">
          <ul className="flex items-center justify-center space-x-3">
            {NAV_ITEMS.map((item) => (
              <li key={item.value}>
                <NavLink
                  to={item.to}
                  className={({ isActive }) =>
                    `flex items-center space-x-2 border-b-2   px-3 py-2 transition-colors duration-150 ease-out ${
                      isActive
                        ? " border-b-[#2CC9D5] text-black"
                        : "hover: border-b-transparent text-[#353535] hover:border-b-[#2CC9D5]"
                    }`
                  }
                >
                  <span className="text-sm">{item.text}</span>
                </NavLink>
              </li>
            ))}
          </ul>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setOpenDropdown(!openDropdown)}
              className="flex items-center space-x-2 rounded-lg px-3 py-2 text-gray-600 hover:bg-gray-100"
            >
              <PiUsersThreeFill className="text-xl" />
              <span>Automateintel Admin</span>
              {openDropdown ? (
                <PiCaretUpBold className="ml-1 text-lg" />
              ) : (
                <PiCaretDownBold className="ml-1 text-lg" />
              )}
            </button>
            {openDropdown && (
              <div className="absolute right-0 mt-2 w-48 rounded-lg border border-gray-200 bg-white py-2 shadow-lg">
                {DROPDOWN_ITEMS.map((item) => (
                  <Link
                    key={item.text}
                    to={item.to}
                    className="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100"
                    onClick={() => setOpenDropdown(false)}
                  >
                    {item.icon}
                    <span>{item.text}</span>
                  </Link>
                ))}
                <div className="my-2 border-t border-gray-200"></div>
                <button
                  onClick={() => {
                    setOpenDropdown(false);
                    handleLogout();
                  }}
                  className="flex w-full items-center space-x-2 px-4 py-2 text-red-600 hover:bg-gray-100"
                >
                  <PiSignOutBold className="text-xl" />
                  <span>Logout</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default AdminHeader;
