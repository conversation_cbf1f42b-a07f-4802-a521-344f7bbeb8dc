import React from "react";

const MkdListTableRow = ({
  i,
  row,
  columns,
  actions,
  actionPosition,
  actionId = "id",
  handleTableCellChange,
  selectedIds = [],
  handleSelectRow,
  setDeleteId,
}) => {
  return (
    <>
      <tr>
        {columns.map((cell, index) => {
          if (cell.accessor.indexOf("image") > -1) {
            return (
              <td key={index} className="whitespace-nowrap px-6 py-4">
                <img
                  src={row[cell.accessor]}
                  className="h-[3.rem] w-[9.375rem]"
                  alt=""
                />
              </td>
            );
          }
          if (
            cell.accessor.indexOf("pdf") > -1 ||
            cell.accessor.indexOf("doc") > -1 ||
            // cell.accessor.indexOf('file') > -1 ||
            cell.accessor.indexOf("video") > -1
          ) {
            return (
              <td key={index} className="whitespace-nowrap px-6 py-4">
                <a
                  className="text-blue-500"
                  target="_blank"
                  href={row[cell.accessor]}
                  rel="noreferrer"
                >
                  {" "}
                  View
                </a>
              </td>
            );
          }
          if (cell.accessor === "") {
            if (
              [
                actions?.select?.show,
                actions?.view?.show,
                actions?.edit?.show,
                actions?.delete?.show,
              ].includes(true)
            ) {
              return (
                <td
                  key={index}
                  className="flex !w-full gap-2 whitespace-nowrap px-6 py-4"
                >
                  {actions?.select?.show && (
                    <span>
                      <input
                        className="mr-1"
                        type="checkbox"
                        name="select_item"
                        checked={selectedIds.includes(row[actionId])}
                        onChange={() => handleSelectRow(row[actionId])}
                      />
                    </span>
                  )}

                  {actionPosition === "onTable" && (
                    <>
                      {actions?.edit?.show && (
                        <button
                          className="cursor-pointer text-xs font-medium text-black hover:underline"
                          onClick={() => {
                            if (actions?.edit?.action) {
                              actions.edit.action([row[actionId]]);
                            }
                            // navigate(
                            //  `/${ tableRole }/edit-${table}/` +
                            //     row[actionId],
                            //   {
                            //     state: row,
                            //   }
                            // );
                          }}
                        >
                          {/* <PencilIcon
                                      className={`h-[1rem] w-[1rem] cursor-pointer group-hover: text-white`}
                                      stroke={"#29282990"}
                                    /> */}
                          <span>Edit</span>
                        </button>
                      )}
                      {actions?.view?.show && (
                        <button
                          className="cursor-pointer px-1 text-xs font-medium text-blue-500 hover:underline"
                          onClick={() => {
                            if (actions?.view?.action) {
                              actions.view.action([row[actionId]]);
                            }
                            // navigate(
                            //   `/${tableRole}/view-${table}/` +
                            //     row[actionId],
                            //   {
                            //     state: row,
                            //   }
                            // );
                          }}
                        >
                          {/* <EyeIcon
                                      className={`h-[1rem] w-[1rem] cursor-pointer group-hover: text-white`}
                                    /> */}
                          <span>View</span>
                        </button>
                      )}
                      {actions?.delete?.show && (
                        <button
                          className="cursor-pointer px-1 text-xs font-medium text-red-500 hover:underline"
                          onClick={() => {
                            // if (actions?.delete?.action) {
                            // actions.delete.action([row[actionId]]);
                            setDeleteId(row[actionId]);
                            // }
                          }}
                        >
                          {/* <TrashIcon
                                      className={`h-[1rem] w-[1rem] cursor-pointer group-hover: text-white`}
                                    /> */}
                          <span>Delete</span>
                        </button>
                      )}
                    </>
                  )}
                </td>
              );
            } else {
              return null;
            }
          }
          if (cell.mappingExist) {
            return (
              <td key={index} className="whitespace-nowrap px-6 py-4">
                {/* <select
                  onChange={(e) =>
                    handleTableCellChange(
                      row[actionId],
                      e.target.value,
                      i,
                      cell.accessor,
                    )
                  }
                >
                  {Object.keys(cell.mappings).map((cellDataKey, index) => (
                    <option
                      key={index}
                      value={cellDataKey}
                      selected={cellDataKey === row[cell.accessor]}
                    >
                      {cell.mappings[cellDataKey]}
                    </option>
                  ))}
                </select> */}
                {cell.mappings[row[cell.accessor]]}
              </td>
            );
          }
          if (
            !cell.mappingExist &&
            cell.accessor !== "id" &&
            cell.accessor !== "create_at" &&
            cell.accessor !== "update_at" &&
            cell.accessor !== "user_id"
          ) {
            return (
              <td key={index} className="whitespace-nowrap px-6 py-4">
                <input
                  className="text-ellipsis border-0"
                  type="text"
                  value={row[cell.accessor]}
                  onChange={(e) =>
                    handleTableCellChange(
                      row[actionId],
                      e.target.value,
                      i,
                      cell.accessor
                    )
                  }
                />
              </td>
            );
          }
          return (
            <td key={index} className="whitespace-nowrap px-6 py-4">
              {row[cell.accessor]}
            </td>
          );
        })}
      </tr>
    </>
  );
};

export default MkdListTableRow;
