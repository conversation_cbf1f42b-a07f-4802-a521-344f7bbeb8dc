import React, { Fragment, useEffect, useMemo } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { MkdListTable, TableActions } from "Components/MkdListTable";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import { AddButton } from "Components/AddButton";
import { ExportButton } from "Components/ExportButton";
import TreeSDK from "Utils/TreeSDK";
import { Popover, Transition } from "@headlessui/react";
import { useSearchParams } from "react-router-dom";
import PaginationBarV2 from "Components/PaginationBar/PaginationBarV2";
import { XIcon } from "lucide-react";

let sdk = new MkdSDK();

function parseSearchParams(queryString) {
  const searchParams = new URLSearchParams(queryString);
  const paramsArray = [];

  searchParams.forEach((value, key) => {
    const match = key.match(/(.+)\[(.+)\]/);
    if (match) {
      const [, field, operation] = match;
      paramsArray.push({ field, operation, value });
    }
  });

  return paramsArray;
}

const MkdListTableV3 = ({
  columns = [],
  actions = {
    view: { show: true, multiple: true, action: null },
    edit: { show: true, multiple: true, action: null },
    delete: { show: true, multiple: true, action: null },
    select: { show: true, multiple: true, action: null },
    add: {
      show: true,
      multiple: true,
      action: null,
      showChildren: true,
      children: "Add New",
    },
    export: { show: true, multiple: true, action: null },
  },
  actionPosition = "onTable",
  actionId = "id",
  tableRole = "admin",
  table = "user",
  tableTitle = "",
  tableSchema = [],
  hasFilter = true,
  schemaFields = [],
  showPagination = true,
  defaultFilter = [],
  refreshRef = null,
  filterConfig = {},
}) => {
  const tdk = new TreeSDK();

  const [searchParams, setSearchParams] = useSearchParams();
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [currentTableData, setCurrentTableData] = React.useState([]);

  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [deleteLoading, setDeleteLoading] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [selectedItems, setSelectedItems] = React.useState([]);

  const [loading, setLoading] = React.useState(true);
  const [paginationData, setPaginationData] = React.useState({
    currentPage: 0,
    pageSize: 0,
    totalNumber: 0,
    totalPages: 0,
  });

  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [filterValues, setFilterValues] = React.useState({});
  const [optionValue, setOptionValue] = React.useState("eq");

  const [searchValue, setSearchValue] = React.useState("");
  const [isSearching, setIsSearching] = React.useState(false);
  const searchTimeoutRef = React.useRef(null);
  const previousSearchRef = React.useRef("");

  const activeFilters = useMemo(() => {
    return filterConditions.length + (searchValue ? 1 : 0);
  }, [filterConditions, searchValue]);

  const filterableColumns = useMemo(() => {
    return columns
      .filter(
        (col) =>
          (!col.hasOwnProperty("isFilter") || col.isFilter) &&
          col.accessor !== "actions" &&
          !col.isAction &&
          col.accessor !== "" // Exclude empty accessors
      )
      .map((col) => ({
        ...col,
        type: col.mappingExist ? "select" : col.type,
        options: col.mappingExist
          ? Object.entries(col.mappings).map(([value, label]) => ({
              value: value,
              label: label,
            }))
          : col.options,
      }));
  }, [columns]);

  function onSort(columnName) {
    searchParams.set("sortId", columnName);
    searchParams.set(
      "direction",
      searchParams.get("direction") == "asc" ? "desc" : "asc"
    );
    getData();
  }

  useEffect(() => {
    getData();
  }, [searchParams]);

  const addFilterCondition = (option, selectedValue, inputValue) => {
    if (!inputValue) {
      setFilterConditions((prevConditions) => {
        return prevConditions.filter((cond) => !cond.startsWith(option + ","));
      });
      return;
    }

    const column = columns.find((col) => col.accessor === option);
    const operator = selectedValue || "eq";

    // Handle mapped values if mappings exist
    let value = inputValue;
    if (column?.mappingExist && column.mappings) {
      // Convert display value to actual value
      const mappingEntry = Object.entries(column.mappings).find(
        ([key, val]) => val.toLowerCase() === inputValue.toLowerCase()
      );
      if (mappingEntry) {
        value = mappingEntry[0];
      }
    }

    const condition = `${option},${operator},${value}`;

    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (cond) => !cond.startsWith(option + ",")
      );
      return [...newConditions, condition];
    });
  };

  async function getData() {
    setLoading(true);
    try {
      const tablePrefix = `${tdk.getProjectId()}_${table}`;
      let filterArray = [...defaultFilter];

      // Handle search value
      if (searchValue.trim()) {
        const searchableColumn =
          columns.find((col) => col.isSearchable)?.accessor || "id";
        filterArray.push(
          `${tablePrefix}.${searchableColumn},cs,${searchValue.trim()}`
        );
      }

      // Update filter conditions to use table prefix
      if (filterConditions.length > 0) {
        const updatedFilters = filterConditions.map((condition) => {
          const [field, operator, value] = condition.split(",");

          // Handle different value types
          let finalValue = value;
          if (
            filterConfig[field]?.type === "string" ||
            columns.find((col) => col.accessor === field)?.type === "string"
          ) {
            finalValue = `'${value}'`;
          }

          // Add table prefix to field
          return `${tablePrefix}.${field},${operator},${finalValue}`;
        });
        filterArray = [...filterArray, ...updatedFilters];
      }

      console.log("Filter Array:", filterArray); // Debug log

      const result = await tdk.getPaginate(table, {
        size: searchParams.get("limit") ?? 50,
        page: searchParams.get("page") ?? 1,
        filter: filterArray.filter(Boolean), // Remove any undefined filters
        sort: searchParams.get("sortId")
          ? `${searchParams.get("sortId")},${
              searchParams.get("direction") || "asc"
            }`
          : undefined,
      });

      const { list, total, limit, num_pages, page } = result;
      setPaginationData({
        currentPage: page,
        pageSize: limit,
        totalNumber: total,
        totalPages: num_pages,
      });

      setCurrentTableData(list);
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
      showToast(globalDispatch, error.message);
    }
    setLoading(false);
  }

  const deleteItem = async (id) => {
    async function deleteId(id) {
      try {
        setDeleteLoading(true);
        sdk.setTable(table);
        const result = await sdk.callRestAPI({ id }, "DELETE");
        if (!result?.error) {
          setCurrentTableData((list) =>
            list.filter((x) => Number(x.id) !== Number(id))
          );
          setDeleteLoading(false);
          setShowDeleteModal(false);
        }
      } catch (err) {
        setDeleteLoading(false);
        setShowDeleteModal(false);
        tokenExpireError(dispatch, err?.message);
        throw new Error(err);
      }
    }

    if (typeof id === "object") {
      id.forEach(async (idToDelete) => {
        await deleteId(idToDelete);
      });
    } else if (typeof id === "number") {
      await deleteId(id);
    }
  };

  const exportTable = async (id) => {
    try {
      sdk.setTable(table);
      const result = await sdk.exportCSV();
    } catch (err) {
      throw new Error(err);
    }
  };

  async function updateTableData(id, key, updatedData) {
    try {
      sdk.setTable(table);
      const result = await sdk.callRestAPI(
        {
          id,
          [key]: updatedData,
        },
        "PUT"
      );
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  async function handleTableCellChange(id, newValue, index, newValueKey) {
    let runApiCall;
    newValue = isNaN(Number.parseInt(newValue))
      ? newValue
      : Number.parseInt(newValue);
    try {
      clearTimeout(runApiCall);
      runApiCall = setTimeout(async () => {
        await updateTableData(id, newValueKey, newValue);
      }, 200);
      setCurrentTableData((prevData) => {
        const updatedData = prevData.map((item, i) => {
          if (i === index) {
            return {
              ...item,
              [newValueKey]: newValue,
            };
          }
          return item;
        });
        return updatedData;
      });
    } catch (error) {
      console.error(error);
    }
  }

  React.useEffect(() => {
    if (actions?.select?.action) {
      actions.select.action();
    }
  }, [selectedItems.length]);

  useEffect(() => {
    getData();
  }, []);

  const handleClearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    setFilterValues({});
    setSearchValue("");
    getData();
  };

  // First, add this new useEffect near your other useEffects
  React.useEffect(() => {
    if (searchValue === "") {
      getData();
      return;
    }

    if (/^\d+$/.test(searchValue)) {
      // For numbers, search immediately
      getData();
    } else {
      // For text, debounce
      const timeoutId = setTimeout(() => {
        getData();
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [searchValue]); // Watch for changes to searchValue

  // Then simplify the debouncedSearch function to just update the search value
  const debouncedSearch = (value) => {
    setIsSearching(true);
    setSearchValue(value);
    if (!value) {
      setIsSearching(false);
    }
  };

  // Update the clear search handler
  <AiOutlineClose
    className="text-lg text-white cursor-pointer"
    onClick={() => {
      setSearchValue("");
      setIsSearching(false);
    }}
  />;

  return (
    <div className="px-8">
      {refreshRef && (
        <button
          ref={refreshRef}
          onClick={() => getData()}
          className="hidden"
        ></button>
      )}
      <div
        className={`flex gap-3 ${
          tableTitle ? "flex-col items-center" : "items-center h-fit"
        }`}
      >
        {hasFilter ? (
          <div className="flex w-[200px] min-w-[200px] items-center justify-between">
            <div className="relative z-10 rounded bg-[#1d2937]">
              <Popover>
                <div className="flex items-center gap-4 bg-[#1d2937] text-white">
                  <Popover.Button className="flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white ">
                    <BiFilterAlt />
                    <span>Filters</span>
                    {activeFilters > 0 && (
                      <span className="flex justify-center items-center w-6 h-6 text-white bg-gray-800 rounded-full text-start">
                        {activeFilters}
                      </span>
                    )}
                  </Popover.Button>
                  <div className="flex gap-3 justify-between items-center px-2 py-1 text-white bg-transparent rounded-md border cursor-pointer focus-within:border-gray-40 border-white/50">
                    <BiSearch
                      className={`text-xl ${
                        isSearching ? "text-gray-400" : "text-white"
                      }`}
                    />
                    <input
                      type="text"
                      placeholder={`Search ${
                        columns.find((col) => col.isSearchable)?.header ||
                        " by Id"
                      }`}
                      className="p-0 text-white bg-transparent border-none placeholder:text-left placeholder:text-gray-300 focus:outline-none"
                      style={{ boxShadow: "0 0 transparent" }}
                      value={searchValue}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        debouncedSearch(newValue);
                      }}
                    />
                    {searchValue && (
                      <AiOutlineClose
                        className="text-lg text-white cursor-pointer"
                        onClick={() => {
                          setSearchValue("");
                          setIsSearching(false);
                        }}
                      />
                    )}
                  </div>
                </div>

                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
                  afterLeave={() => getData()}
                >
                  <Popover.Panel>
                    <div className="filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10">
                      <span className="absolute top-2 left-5 font-medium text-white">
                        Filters
                      </span>
                      <Popover.Button
                        onClick={() => {
                          console.log("clicked");
                          setSelectedOptions([]);
                          setFilterConditions([]);
                          setFilterValues({});
                        }}
                      >
                        <XIcon className="absolute top-2 right-2 text-white cursor-pointer" />
                      </Popover.Button>
                      {selectedOptions?.map((option, index) => {
                        const column = filterableColumns.find(
                          (col) => col.accessor === option
                        );
                        return (
                          <div
                            key={index}
                            className="flex gap-3 justify-between items-center mb-2 w-full text-gray-600"
                          >
                            <button
                              type="button"
                              className="block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none"
                              title={column?.header || option}
                            >
                              {column?.header || option}
                            </button>

                            <select
                              className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none"
                              value={optionValue}
                              onChange={(e) => {
                                setOptionValue(e.target.value);
                                addFilterCondition(
                                  option,
                                  e.target.value,
                                  filterValues[option]
                                );
                              }}
                            >
                              <option value="eq">equals</option>
                              <option value="cs">contains</option>
                              <option value="sw">start with</option>
                              <option value="ew">ends with</option>
                              <option value="lt">lower than</option>
                              <option value="le">lower or equal</option>
                              <option value="ge">greater or equal</option>
                              <option value="gt">greater than</option>
                              <option value="bt">between</option>
                              <option value="in">in</option>
                              <option value="is">is null</option>
                            </select>

                            {column?.type === "select" ? (
                              <select
                                value={filterValues[option] || ""}
                                onChange={(e) => {
                                  setFilterValues((prev) => ({
                                    ...prev,
                                    [option]: e.target.value,
                                  }));
                                  addFilterCondition(
                                    option,
                                    optionValue,
                                    e.target.value
                                  );
                                }}
                                className="h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                              >
                                <option value="">Select...</option>
                                {column.options?.map((opt) => (
                                  <option key={opt.value} value={opt.value}>
                                    {opt.label}
                                  </option>
                                ))}
                              </select>
                            ) : (
                              <input
                                type={column?.type || "text"}
                                placeholder="Enter value"
                                className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                                value={filterValues[option] || ""}
                                onChange={(e) => {
                                  setFilterValues((prev) => ({
                                    ...prev,
                                    [option]: e.target.value,
                                  }));
                                  addFilterCondition(
                                    option,
                                    optionValue,
                                    e.target.value
                                  );
                                }}
                              />
                            )}

                            <RiDeleteBin5Line
                              className="text-2xl text-red-600 cursor-pointer"
                              onClick={() => {
                                setSelectedOptions((prevOptions) =>
                                  prevOptions.filter((op) => op !== option)
                                );
                                setFilterConditions((prevConditions) =>
                                  prevConditions.filter(
                                    (condition) =>
                                      !condition.startsWith(option + ",")
                                  )
                                );
                                setFilterValues((prev) => {
                                  const newValues = { ...prev };
                                  delete newValues[option];
                                  return newValues;
                                });
                              }}
                            />
                          </div>
                        );
                      })}

                      <div className="flex relative justify-between items-center font-semibold search-buttons">
                        <div
                          className="mr-2 flex h-[40px] w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out"
                          onClick={() => {
                            setShowFilterOptions(!showFilterOptions);
                          }}
                        >
                          <AiOutlinePlus />
                          Add filter
                        </div>

                        {showFilterOptions && (
                          <div className="absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3 text-gray-600">
                            <ul className="flex flex-col gap-2 text-gray-500">
                              {filterableColumns.map((column) => (
                                <li
                                  key={column.accessor}
                                  className={`${
                                    selectedOptions.includes(column.accessor)
                                      ? "cursor-not-allowed text-gray-100"
                                      : "cursor-pointer text-gray-400"
                                  }`}
                                  onClick={() => {
                                    if (
                                      !selectedOptions.includes(column.accessor)
                                    ) {
                                      setSelectedOptions((prev) => [
                                        ...prev,
                                        column.accessor,
                                      ]);
                                      setFilterValues((prev) => ({
                                        ...prev,
                                        [column.accessor]: "",
                                      }));
                                      setShowFilterOptions(false);
                                    }
                                  }}
                                >
                                  {column.header}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {activeFilters > 0 && (
                          <div
                            onClick={handleClearAllFilters}
                            className="inline-block cursor-pointer  rounded py-2.5  pl-6 font-normal leading-tight text-white  transition duration-150 ease-in-out"
                          >
                            Clear all filter
                          </div>
                        )}
                      </div>
                    </div>
                  </Popover.Panel>
                </Transition>
              </Popover>
            </div>
          </div>
        ) : null}

        <div className="flex justify-end w-full text-center h-fit">
          <div className="flex justify-between w-full max-w-7xl">
            <h4 className="text-2xl font-medium capitalize">
              {tableTitle ? tableTitle : ""}
            </h4>
            <div className="flex gap-2 h-full">
              {selectedItems?.length && actionPosition === "aboveTable" ? (
                <TableActions actions={actions} selectedItems={selectedItems} />
              ) : null}

              {actions?.export?.show && (
                <ExportButton
                  showText={false}
                  onClick={exportTable}
                  className="mx-1"
                />
              )}

              {actions?.add?.show && (
                <AddButton
                  onClick={() => {
                    if (actions?.add?.action) {
                      actions?.add?.action();
                    }
                  }}
                  showChildren={actions?.add?.showChildren}
                >
                  {actions?.add?.children}
                </AddButton>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="overflow-x-auto rounded bg-[#1d2937] p-5 px-0">
        <MkdListTable
          onSort={onSort}
          columns={columns}
          tableRole={tableRole}
          actionId={actionId}
          table={table}
          tableTitle={tableTitle}
          deleteItem={deleteItem}
          loading={loading}
          deleteLoading={deleteLoading}
          showDeleteModal={showDeleteModal}
          currentTableData={currentTableData}
          setShowDeleteModal={setShowDeleteModal}
          actions={actions}
          actionPosition={actionPosition}
          setSelectedItems={setSelectedItems}
          handleTableCellChange={handleTableCellChange}
        />
      </div>
      {showPagination && <PaginationBarV2 paginationData={paginationData} />}
    </div>
  );
};

export default MkdListTableV3;
