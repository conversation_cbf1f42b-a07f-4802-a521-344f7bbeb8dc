import React from "react";

const MkdListTableHead = ({
  onSort,
  columns,
  actions,
  actionPosition,
  areAllRowsSelected,
  handleSelectAll,
}) => {
  return (
    <>
      <tr>
        {columns.map((column, i) => {
          if (column?.accessor === "") {
            if (
              [
                actions?.select?.show,
                actions?.view?.show,
                actions?.edit?.show,
                actions?.delete?.show,
              ].includes(true)
            ) {
              return (
                <th
                  key={i}
                  scope="col"
                  className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white ${
                    column.isSorted ? "cursor-pointer" : ""
                  } `}
                  onClick={column.isSorted ? () => onSort(i) : undefined}
                >
                  {column.header === "Action" && actions?.select?.show ? (
                    <input
                      type="checkbox"
                      disabled={!actions?.select?.multiple}
                      id="select_all_rows"
                      className="mr-3"
                      checked={areAllRowsSelected}
                      onChange={handleSelectAll}
                    />
                  ) : null}
                  {actionPosition === "onTable" && column.header}
                  <span>
                    {column.isSorted ? (column.isSortedDesc ? " ▼" : " ▲") : ""}
                  </span>
                </th>
              );
            }
          } else {
            return (
              <th
                key={i}
                scope="col"
                className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white ${
                  column.isSorted ? "cursor-pointer" : ""
                } `}
                onClick={column.isSorted ? () => onSort(i) : undefined}
              >
                {column.header === "Action" && actions?.select?.show ? (
                  <input
                    type="checkbox"
                    id="select_all_rows"
                    className="mr-3"
                    checked={areAllRowsSelected}
                    onChange={handleSelectAll}
                  />
                ) : null}
                {column.header}
                <span>
                  {column.isSorted ? (column.isSortedDesc ? " ▼" : " ▲") : ""}
                </span>
              </th>
            );
          }
          return null;
        })}
      </tr>
    </>
  );
};

export default MkdListTableHead;
