import React, { Fragment, useState, useContext, useEffect } from "react";
import { Link, NavLink, useNavigate } from "react-router-dom";
import { PiUsersThreeFill } from "react-icons/pi";
import MkdSDK from "Utils/MkdSDK";
import {
  MdAssistant,
  MdCampaign,
  MdFormatListNumbered,
  MdHandshake,
  MdOutlineAttachMoney,
  MdOutlineTextsms,
  MdRecordVoiceOver,
} from "react-icons/md";
import { GlobalContext } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import {
  BsTelephoneInbound,
  BsTelephoneOutboundFill,
  BsTelephone,
} from "react-icons/bs";
import { Menu, Transition, Dialog } from "@headlessui/react";
import { UserCircleIcon } from "@heroicons/react/24/solid";
import { useProfile } from "Hooks/useProfile";
import { showToast } from "Context/Global";
let sdk = new MkdSDK();

const NAV_ITEMS = [
  // {
  //   to: "/user/onboarding",
  //   text: "Onboarding",
  //   icon: <MdHandshake className="text-xl text-[#A8A8A8]" />,
  //   value: "onboarding",
  // },
  {
    to: "/user/voice",
    text: "Voice Assistant Test",
    icon: <MdRecordVoiceOver className="text-xl text-[#A8A8A8]" />,
    value: "voice",
  },
  {
    to: "/user/sms",
    text: "SMS Assistant Test",
    icon: <MdOutlineTextsms className="text-xl text-[#A8A8A8]" />,
    value: "sms",
  },
  {
    to: "/user/voice_list",
    text: "Voices",
    icon: <MdRecordVoiceOver className="text-xl text-[#A8A8A8]" />,
    value: "voice_list",
  },
  {
    to: "/user/assistants",
    text: "Voice Assistants",
    icon: <MdAssistant className="text-xl text-[#A8A8A8]" />,
    value: "assistants",
  },
  {
    to: "/user/numbers",
    text: "Phone #",
    icon: <MdFormatListNumbered className="text-xl text-[#A8A8A8]" />,
    value: "numbers",
  },
  {
    to: "/user/outbound_campaigns",
    text: "OutBound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "outbound_campaigns",
  },
  // {
  //   to: "/user/campaign-summary",
  //   text: "Campaign Summary",
  //   icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
  //   value: "/user/campaign-summary",
  // },
  {
    to: "/user/inbound_campaigns",
    text: "Inbound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "inbound_campaigns",
  },
  {
    to: "/user/sms_outbound_campaigns",
    text: "SMS OutBound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "sms_outbound_campaigns",
  },
  {
    to: "/user/sms_inbound_campaigns",
    text: "SMS Inbound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "sms_inbound_campaigns",
  },
  {
    to: "/user/outbound_call_logs",
    text: "Outbound Call Logs",
    icon: <BsTelephoneOutboundFill className="text-xl text-[#A8A8A8]" />,
    value: "outbound_call_logs",
  },
  {
    to: "/user/inbound_call_logs",
    text: "Inbound Call Logs",
    icon: <BsTelephoneInbound className="text-xl text-[#A8A8A8]" />,
    value: "inbound_call_logs",
  },
  {
    to: "/user/test_logs",
    text: "Sample Voice Call Log",
    icon: <BsTelephone className="text-xl text-[#A8A8A8]" />,
    value: "test_call_logs",
  },
  {
    to: "/user/test_sms_logs",
    text: "Sample SMS Followup Logs",
    icon: <MdOutlineTextsms className="text-xl text-[#A8A8A8]" />,
    value: "test_sms_logs",
  },
  {
    to: "/user/stripe_subscription",
    text: "Billing",
    icon: <MdOutlineAttachMoney className="text-xl text-[#A8A8A8]" />,
    value: "subscription",
  },
  {
    to: "/user/profile",
    text: "Profile",
    icon: <PiUsersThreeFill className="text-xl text-[#A8A8A8]" />,
    value: "profile",
  },
];

const BugReportModal = ({ isOpen, onClose, onSubmit }) => {
  const [query, setQuery] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = () => {
    if (query.trim() === "") {
      setError("Query cannot be empty");
      return;
    }
    onSubmit(query);
    setQuery("");
    setError("");
    onClose();
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-transparent bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-gray-900"
                >
                  Report a Bug
                </Dialog.Title>
                <div className="mt-2">
                  <input
                    type="text"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    className="w-full rounded-md border border-gray-300 p-2"
                    placeholder="Enter your query"
                  />
                  {error && (
                    <p className="mt-1 text-sm text-red-600">{error}</p>
                  )}
                </div>

                <div className="mt-4 flex justify-end space-x-2">
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700"
                    onClick={onClose}
                  >
                    Close
                  </button>
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-[#2cc9d5]/70"
                    onClick={handleSubmit}
                  >
                    Submit
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
export const UserHeader = () => {
  const {
    state: { isOpen, path },
    dispatch: globalDispatch,
  } = useContext(GlobalContext);
  const { state } = React.useContext(AuthContext);
  const { state: authState, dispatch } = useContext(AuthContext);
  const [reportBug, setReportBug] = useState(false);
  const navigate = useNavigate();

  const [profile] = useProfile();

  let toggleOpen = (open) => {
    globalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });
  };

  useEffect(() => {
    async function fetchData() {
      try {
        const result = await sdk.getProfile();
        dispatch({
          type: "UPDATE_PROFILE",
          payload: result,
        });
      } catch (error) {
        console.log("Error", error);
        tokenExpireError(
          dispatch,
          error.response.data.message
            ? error.response.data.message
            : error.message
        );
      }
    }

    fetchData();
  }, []);

  const handleBugReportSubmit = async (query) => {
    // Handle the bug report submission logic here
    console.log("Bug report submitted:", query);
    try {
      sdk.setTable("reports");
      await sdk.callRestAPI(
        {
          query,
          user_id: state.user,
        },
        "POST"
      );
      setReportBug(false);
      showToast(globalDispatch, "Report submitted");
    } catch (e) {
      showToast(globalDispatch, "Unable to submit bug report");
    }
  };

  return (
    <>
      <div
        className={`z-50 flex max-h-screen flex-1 flex-col bg-white py-4 text-[#A8A8A8] transition-all ${
          isOpen
            ? "fixed h-screen w-[18rem] min-w-[18rem] max-w-[18rem] md:relative"
            : "relative min-h-screen w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem] bg-transparent text-white"
        } `}
      >
        <div
          className={`text-[#393939] ${
            isOpen ? "flex w-full" : "flex items-center justify-center"
          } `}
        >
          <div></div>
          {isOpen && (
            <div className="text-2xl font-bold">
              <Link to="/">
                <h4 className="flex cursor-pointer items-center px-4 pb-4 font-sans font-bold">
                  AutomateIntel - Voice
                </h4>
              </Link>
            </div>
          )}
        </div>

        <div className="h-fit w-auto flex-1">
          <div className="sidebar-list w-auto">
            <ul className="flex flex-wrap px-2 text-sm">
              {NAV_ITEMS.map((item) => (
                <li className="block w-full list-none" key={item.value}>
                  <NavLink
                    to={item.to}
                    className={`${path == item.value ? "active-nav" : ""} `}
                  >
                    <div className="flex items-center gap-3">
                      {item.icon}
                      {isOpen && <span>{item.text}</span>}
                    </div>
                  </NavLink>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="flex justify-between pl-2">
          <Menu as="div" className="relative inline-block text-left">
            <div>
              <Menu.Button className="inline-flex w-full items-center justify-center gap-4 rounded-sm border border-gray-200 bg-gray-50 px-4 py-2 text-base font-medium text-transparent hover:bg-gray-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75">
                <UserCircleIcon className="h-5 w-5" />
                {profile?.first_name} {profile?.last_name}
              </Menu.Button>
            </div>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute bottom-0 left-full mt-2 w-56 origin-bottom-left divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-transparent/5 focus:outline-none">
                <div className="px-1 py-1">
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        className={`${
                          active ? "bg-gray-500 text-white" : "text-gray-900"
                        } group flex w-full items-center rounded-md px-3 py-3`}
                        to={`/user/profile`}
                      >
                        Account
                      </Link>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        className={`${
                          active ? "bg-gray-500 text-white" : "text-gray-900"
                        } group flex w-full items-center rounded-md px-3 py-3`}
                        onClick={() => {
                          dispatch({ type: "LOGOUT" });
                          navigate(`/user/login`);
                        }}
                      >
                        Log out
                      </button>
                    )}
                  </Menu.Item>
                </div>
              </Menu.Items>
            </Transition>
          </Menu>

          <div className="mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400">
            <span onClick={() => toggleOpen(!isOpen)}>
              <svg
                className={`transition-transform ${
                  !isOpen ? "rotate-180" : ""
                }`}
                xmlns="http:www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z"
                  fill="#A8A8A8"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>

      {/* <BugReportModal
        isOpen={reportBug}
        onClose={() => setReportBug(false)}
        onSubmit={handleBugReportSubmit}
      /> */}
    </>
  );
};

export default UserHeader;
