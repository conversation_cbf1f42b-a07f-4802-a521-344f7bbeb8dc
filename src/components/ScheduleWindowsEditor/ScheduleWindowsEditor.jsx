import React from "react";
import { MdAdd, MdDelete } from "react-icons/md";

const DAYS_OF_WEEK = [
  { value: "monday", label: "Mon" },
  { value: "tuesday", label: "<PERSON><PERSON>" },
  { value: "wednesday", label: "Wed" },
  { value: "thursday", label: "Thu" },
  { value: "friday", label: "Fri" },
  { value: "saturday", label: "Sat" },
  { value: "sunday", label: "Sun" },
];

const TIMEZONES = [
  { value: "America/New_York", label: "Eastern Time" },
  { value: "America/Chicago", label: "Central Time" },
  { value: "America/Denver", label: "Mountain Time" },
  { value: "America/Los_Angeles", label: "Pacific Time" },
  { value: "America/Phoenix", label: "Arizona Time" },
  { value: "America/Anchorage", label: "Alaska Time" },
  { value: "Pacific/Honolulu", label: "Hawaii Time" },
];

export const ScheduleWindowsEditor = ({ value = [], onChange }) => {
  const addWindow = () => {
    const newWindow = {
      days: ["monday", "tuesday", "wednesday", "thursday", "friday"],
      start_time: "09:00",
      end_time: "17:00",
      timezone: "America/New_York",
    };
    onChange([...value, newWindow]);
  };

  const removeWindow = (index) => {
    const newWindows = value.filter((_, i) => i !== index);
    onChange(newWindows);
  };

  const updateWindow = (index, field, newValue) => {
    const newWindows = value.map((window, i) => {
      if (i === index) {
        return { ...window, [field]: newValue };
      }
      return window;
    });
    onChange(newWindows);
  };

  const toggleDay = (windowIndex, day) => {
    const window = value[windowIndex];
    const newDays = window.days.includes(day)
      ? window.days.filter(d => d !== day)
      : [...window.days, day];
    updateWindow(windowIndex, "days", newDays);
  };

  return (
    <div className="space-y-4">
      {value.map((window, index) => (
        <div key={index} className="border border-gray-200 rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Schedule Window {index + 1}
            </h3>
            {value.length > 1 && (
              <button
                type="button"
                onClick={() => removeWindow(index)}
                className="text-red-600 hover:text-red-800"
              >
                <MdDelete className="text-xl" />
              </button>
            )}
          </div>

          {/* Days Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Days of Week
            </label>
            <div className="flex flex-wrap gap-2">
              {DAYS_OF_WEEK.map((day) => (
                <button
                  key={day.value}
                  type="button"
                  onClick={() => toggleDay(index, day.value)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    window.days.includes(day.value)
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  {day.label}
                </button>
              ))}
            </div>
          </div>

          {/* Time Range */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Time
              </label>
              <input
                type="time"
                value={window.start_time}
                onChange={(e) => updateWindow(index, "start_time", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Time
              </label>
              <input
                type="time"
                value={window.end_time}
                onChange={(e) => updateWindow(index, "end_time", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timezone
              </label>
              <select
                value={window.timezone}
                onChange={(e) => updateWindow(index, "timezone", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {TIMEZONES.map((tz) => (
                  <option key={tz.value} value={tz.value}>
                    {tz.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      ))}

      <button
        type="button"
        onClick={addWindow}
        className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
      >
        <MdAdd className="text-lg" />
        Add Schedule Window
      </button>
    </div>
  );
};
