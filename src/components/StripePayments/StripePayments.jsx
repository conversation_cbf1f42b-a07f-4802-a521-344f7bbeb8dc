import React, { useState } from "react";
import {
  AdminStripeInvoicesListPageV2,
  AdminStripePricesListPage,
  AdminStripeSubscriptionsListPage,
} from "Pages/Admin/List";
import { GlobalContext } from "Context/Global";
import { LazyLoad } from "Components/LazyLoad";

const StripePayments = () => {
  const [activeTab, setActiveTab] = useState("plans");
  const { state, dispatch } = React.useContext(GlobalContext);

  const TABS = {
    plans: (
      <LazyLoad>
        <AdminStripePricesListPage />
      </LazyLoad>
    ),
    subscriptions: (
      <LazyLoad>
        <AdminStripeSubscriptionsListPage />
      </LazyLoad>
    ),
    invoices: (
      <LazyLoad>
        <AdminStripeInvoicesListPageV2 />
      </LazyLoad>
    ),
  };
  React.useEffect(() => {
    dispatch({
      type: "SETPATH",
      payload: {
        path: "payments",
      },
    });
  }, []);

  return (
    <div>
      <div className="flex items-center border-b border-b-[#E0E0E0] px-6 py-3 text-[#ffffffd1]">
        <div className="flex items-center space-x-3">
          <div
            className={`cursor-pointer rounded-lg px-3 py-1 ${
              activeTab === "plans" ? "bg-[#f4f4f4] text-[#525252]" : ""
            } `}
            onClick={() => setActiveTab("plans")}
          >
            Plans
          </div>
          <div
            className={`cursor-pointer rounded-lg px-3 py-1 ${
              activeTab === "subscriptions" ? "bg-[#f4f4f4] text-[#525252]" : ""
            } `}
            onClick={() => setActiveTab("subscriptions")}
          >
            Subscriptions
          </div>
          <div
            className={`cursor-pointer rounded-lg px-3 py-1 ${
              activeTab === "invoices" ? "bg-[#f4f4f4] text-[#525252]" : ""
            }`}
            onClick={() => setActiveTab("invoices")}
          >
            Invoices
          </div>
        </div>
      </div>
      <div className="px-8">{TABS[activeTab]}</div>
    </div>
  );
};

export default StripePayments;
