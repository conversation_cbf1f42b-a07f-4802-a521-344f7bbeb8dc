import React, { Fragment, useState, useContext, useEffect } from "react";
import { Link, NavLink, useNavigate } from "react-router-dom";
import { PiUsersThreeFill } from "react-icons/pi";
import MkdSDK from "Utils/MkdSDK";
import {
  MdAssistant,
  MdCampaign,
  MdDashboard,
  MdAnalytics,
  MdKey,
  MdAttachMoney,
  MdSettings,
  MdOutlineAttachMoney,
} from "react-icons/md";
import { GlobalContext } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { Menu, Transition, Dialog } from "@headlessui/react";
import { UserCircleIcon } from "@heroicons/react/24/solid";
import { useProfile } from "Hooks/useProfile";
import { showToast } from "Context/Global";

let sdk = new MkdSDK();

const WHOLESALER_NAV_ITEMS = [
  {
    to: "/wholesaler/dashboard",
    text: "Dashboard",
    icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
    value: "dashboard",
  },
  {
    to: "/wholesaler/lead-campaigns",
    text: "Lead Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "lead-campaigns",
  },
  {
    to: "/wholesaler/analytics",
    text: "Analytics & Reports",
    icon: <MdAnalytics className="text-xl text-[#A8A8A8]" />,
    value: "analytics",
  },
  {
    to: "/wholesaler/api-keys",
    text: "API Keys",
    icon: <MdKey className="text-xl text-[#A8A8A8]" />,
    value: "api-keys",
  },
  {
    to: "/wholesaler/cost-tracking",
    text: "Cost Tracking",
    icon: <MdOutlineAttachMoney className="text-xl text-[#A8A8A8]" />,
    value: "cost-tracking",
  },
  {
    to: "/wholesaler/settings",
    text: "Settings",
    icon: <MdSettings className="text-xl text-[#A8A8A8]" />,
    value: "settings",
  },
];

export const WholesalerHeader = () => {
  const {
    state: { isOpen, path },
    dispatch: globalDispatch,
  } = useContext(GlobalContext);
  const { state } = React.useContext(AuthContext);
  const { state: authState, dispatch } = useContext(AuthContext);
  const [reportBug, setReportBug] = useState(false);
  const navigate = useNavigate();

  const [profile] = useProfile();

  let toggleOpen = (open) => {
    globalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });
  };

  const reportBugHandler = async (data) => {
    try {
      const result = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/bug-report",
        {
          message: data.message,
          page: window.location.href,
        },
        "POST"
      );
      if (!result.error) {
        showToast(globalDispatch, "Bug report submitted successfully");
        setReportBug(false);
      }
    } catch (error) {
      console.log("Error", error);
      showToast(globalDispatch, error.message, 5000, "error");
    }
  };

  return (
    <>
      <div
        className={`z-50 flex max-h-screen flex-1 flex-col bg-white py-4 text-[#A8A8A8] transition-all ${
          isOpen
            ? "fixed h-screen w-[18rem] min-w-[18rem] max-w-[18rem] md:relative"
            : "relative min-h-screen w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem] bg-transparent text-white"
        } `}
      >
        <div
          className={`text-[#393939] ${
            isOpen ? "flex w-full" : "flex items-center justify-center"
          } `}
        >
          <div></div>
          {isOpen && (
            <div className="text-2xl font-bold">
              <Link to="/">
                <h4 className="flex cursor-pointer items-center px-4 pb-4 font-sans font-bold">
                  AutomateIntel - Wholesaler
                </h4>
              </Link>
            </div>
          )}
        </div>

        <div className="h-fit w-auto flex-1">
          <div className="sidebar-list w-auto">
            <ul className="flex flex-wrap px-2 text-sm">
              {WHOLESALER_NAV_ITEMS.map((item) => (
                <li className="block w-full list-none" key={item.value}>
                  <NavLink
                    to={item.to}
                    className={`${path == item.value ? "active-nav" : ""} `}
                  >
                    <div className="flex items-center gap-3">
                      {item.icon}
                      {isOpen && <span>{item.text}</span>}
                    </div>
                  </NavLink>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="flex w-full items-center justify-center">
          <Menu as="div" className="relative inline-block text-left">
            <div>
              <Menu.Button className="inline-flex w-full justify-center rounded-md bg-transparent px-4 py-2 text-sm font-medium text-[#A8A8A8] hover:bg-black/20 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75">
                <UserCircleIcon
                  className="h-8 w-8 text-[#A8A8A8]"
                  aria-hidden="true"
                />
              </Menu.Button>
            </div>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute bottom-0 left-full mt-2 w-56 origin-bottom-left divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-transparent/5 focus:outline-none">
                <div className="px-1 py-1">
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        className={`${
                          active ? "bg-gray-500 text-white" : "text-gray-900"
                        } group flex w-full items-center rounded-md px-3 py-3`}
                        to={`/wholesaler/profile`}
                      >
                        Account
                      </Link>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        className={`${
                          active ? "bg-gray-500 text-white" : "text-gray-900"
                        } group flex w-full items-center rounded-md px-3 py-3`}
                        onClick={() => {
                          dispatch({ type: "LOGOUT" });
                          navigate(`/wholesaler/login`);
                        }}
                      >
                        Log out
                      </button>
                    )}
                  </Menu.Item>
                </div>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>
      </div>

      {/* Bug Report Modal */}
      <Dialog
        open={reportBug}
        onClose={() => setReportBug(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto max-w-sm rounded bg-white p-6">
            <Dialog.Title className="text-lg font-medium">
              Report a Bug
            </Dialog.Title>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                reportBugHandler({ message: formData.get("message") });
              }}
            >
              <textarea
                name="message"
                placeholder="Describe the issue..."
                className="mt-4 w-full rounded border p-2"
                rows={4}
                required
              />
              <div className="mt-4 flex gap-2">
                <button
                  type="submit"
                  className="rounded bg-blue-500 px-4 py-2 text-white"
                >
                  Submit
                </button>
                <button
                  type="button"
                  onClick={() => setReportBug(false)}
                  className="rounded bg-gray-300 px-4 py-2"
                >
                  Cancel
                </button>
              </div>
            </form>
          </Dialog.Panel>
        </div>
      </Dialog>
    </>
  );
};
