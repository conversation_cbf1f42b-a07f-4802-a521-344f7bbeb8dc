import React, { Fragment, useEffect, useState } from "react";
import { GlobalContext } from "Context/Global";
import { Link, useLocation, useNavigate, NavLink } from "react-router-dom";
import { BackButton } from "Components/BackButton";
import { AuthContext } from "Context/Auth";
import { StringCaser } from "Utils/utils";
import { useProfile } from "Hooks/useProfile";
import MkdSDK from "Utils/MkdSDK";
import {
  MdAssistant,
  MdCampaign,
  MdFormatListNumbered,
  MdHandshake,
  MdOutlineAttachMoney,
  MdOutlineTextsms,
  MdRecordVoiceOver,
} from "react-icons/md";
import { PiUsersThreeFill } from "react-icons/pi";
import { BsTelephoneInbound, BsTelephoneOutboundFill, BsTelephone } from "react-icons/bs";
import { <PERSON>u, Transition, Dialog } from "@headlessui/react";
import { UserCircleIcon } from "@heroicons/react/24/solid";
import { showToast } from 'Context/Global';
import { automateIcon } from 'Assets/images';

const NAV_ITEMS = [
  {
    to: "/user/onboarding",
    text: "Onboarding",
    icon: <MdHandshake className="text-xl text-[#A8A8A8]" />,
    value: "onboarding",
  },
  {
    to: "/user/voice",
    text: "Voice Assistant Test",
    icon: <MdRecordVoiceOver className="text-xl text-[#A8A8A8]" />,
    value: "voice",
  },
  {
    to: "/user/sms",
    text: "SMS Assistant Test",
    icon: <MdOutlineTextsms className="text-xl text-[#A8A8A8]" />,
    value: "sms",
  },
  {
    to: "/user/voice_list",
    text: "Cloned Voices",
    icon: <MdRecordVoiceOver className="text-xl text-[#A8A8A8]" />,
    value: "voice_list",
  },
  {
    to: "/user/assistants",
    text: "Voice Assistants",
    icon: <MdAssistant className="text-xl text-[#A8A8A8]" />,
    value: "assistants",
  },
  {
    to: "/user/numbers",
    text: "Phone #",
    icon: <MdFormatListNumbered className="text-xl text-[#A8A8A8]" />,
    value: "numbers",
  },
  {
    to: "/user/outbound_campaigns",
    text: "OutBound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "outbound_campaigns",
  },
  {
    to: "/user/inbound_campaigns",
    text: "Inbound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "inbound_campaigns",
  },
  {
    to: "/user/outbound_campaigns",
    text: "SMS OutBound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "sms_outbound_campaigns",
  },
  {
    to: "/user/inbound_campaigns",
    text: "SMS Inbound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "sms_inbound_campaigns",
  },
  {
    to: "/user/outbound_call_logs",
    text: "Outbound Call Logs",
    icon: <BsTelephoneOutboundFill className="text-xl text-[#A8A8A8]" />,
    value: "outbound_call_logs",
  },
  {
    to: "/user/inbound_call_logs",
    text: "Inbound Call Logs",
    icon: <BsTelephoneInbound className="text-xl text-[#A8A8A8]" />,
    value: "inbound_call_logs",
  },
  {
    to: "/user/test_logs",
    text: "Sample Voice Call Log",
    icon: <BsTelephone className="text-xl text-[#A8A8A8]" />,
    value: "test_call_logs",
  },
  {
    to: "/user/test_sms_logs",
    text: "Sample SMS Followup Logs",
    icon: <MdOutlineTextsms className="text-xl text-[#A8A8A8]" />,
    value: "test_sms_logs",
  },
  {
    to: "/user/stripe_subscription",
    text: "Billing",
    icon: <MdOutlineAttachMoney className="text-xl text-[#A8A8A8]" />,
    value: "subscription",
  },
  {
    to: "/user/profile",
    text: "Profile",
    icon: <PiUsersThreeFill className="text-xl text-[#A8A8A8]" />,
    value: "profile",
  },
];

let sdk = new MkdSDK();
const TopHeader = () => {
  const { state: globalState, dispatch: globalDispatch } =
    React.useContext(GlobalContext);
  const { state, dispatch } = React.useContext(AuthContext);
  const [currentPath, setCurrentPath] = useState("");
  const { showBackButton } = globalState;
  const location = useLocation();
  const [profile] = useProfile();
  const [points, setPoints] = useState(null);
  const [reportBug, setReportBug] = useState(false);
  const navigate = useNavigate();
  function NavItem({ label, isActive, onClick, onMouseEnter, children }) {
    return (
      <div className="relative" onMouseEnter={onMouseEnter}>
        <button
          onClick={onClick}
          className={`
            px-4 py-1.5 rounded-full transition-colors duration-200 relative z-10
            ${isActive ? 'text-gray-900' : 'text-gray-600 hover:text-gray-900'}
          `}
        >
          {label}
        </button>
        {children}
      </div>
    );
  }
  function Logo() {
    return (
      <div className="flex items-center">
           <span className="text-2xl font-bold bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent">
          AutomateIntel - Voice
        </span>
        {/* <img src={automateIcon} alt="AutomateIntel - Voice" className="h-8 w-auto" /> */}
      </div>
    );
  }
  function NavDropdown({ isOpen, children }) {
    return (
      <div
        className={`
          absolute top-full left-0 pt-3 transition-all duration-200 transform
          ${isOpen ? 'opacity-100 translate-y-0 visible' : 'opacity-0 -translate-y-2 invisible'}
        `}
      >
        <div className="bg-white rounded-lg shadow-lg border border-gray-100 py-2 min-w-[240px]">
          {children}
        </div>
      </div>
    );
  }
  function NavGroup({ children, activeIndex }) {
    return (
      <div className="relative flex items-center bg-gray-100 rounded-full p-1">
        <NavSlider activeIndex={activeIndex} width={100} />
        {children}
      </div>
    );
  }
  function NavSlider({ activeIndex, width }) {
    return (
      <div
        className="absolute h-8 bg-white rounded-full shadow-sm transition-all duration-300 ease-out"
        style={{
          width: `${width}px`,
          transform: `translateX(${activeIndex * width}px)`,
        }}
      />
    );
  }
  useEffect(() => {
    const pathArr = location.pathname.split("/");
    (async function getPoints() {
      sdk.setTable("profile");
      const result = await sdk.callRestAPI(
        { user_id: state.user, filter: [`user_id,eq,${state.user}`] },
        "GETALL"
      );
      if (!result.error) {
        let fetched = result?.list[0]?.points ?? 0;
        if (fetched) {
          setPoints(fetched);
        }
      }
    })();
    if (pathArr[1] === "user" && pathArr[2] === "voice") {
      setCurrentPath("Voice Assistant Test");
    }
    else if (pathArr[1] === "user" && pathArr[2] === "sms") {
      setCurrentPath("SMS Assistant Test");
    } else if (pathArr[1] === "user" && pathArr[2] === "voice_list") {
      setCurrentPath("Cloned Voices");
    } else if (pathArr[1] === "user" && pathArr[2] === "stripe_subscription") {
      setCurrentPath("Plans");
    } else if (pathArr[1] === "user" && pathArr[2] === "assistants") {
      setCurrentPath("Voice Assistants");
    } else if (pathArr[1] !== "user" && pathArr[1] !== "admin") {
      setCurrentPath(pathArr[1]);
    } else if(pathArr[2] === "numbers") {
      setCurrentPath("Phone #")
    } else if(pathArr[2] === "test_logs") {
      setCurrentPath("Sample Voice Call Logs")
    }  else if(pathArr[2] === "test_sms_logs") {
      setCurrentPath("Sample SMS Followup Logs")
    } 
    else {
      setCurrentPath(pathArr[2]);
    }
  }, [location]);
  const [activeLeft, setActiveLeft] = useState(0);
  const [activeRight, setActiveRight] = useState(0);
  const [openDropdown, setOpenDropdown] = useState(null);

  const handleMouseEnter = (section, index) => {
    if (section === 'left') {
      setActiveLeft(index);
    } else {
      setActiveRight(index);
    }
    setOpenDropdown(`${section}-${index}`);
  };

  return (
      <nav className="bg-gray-50 border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left section */}
            <NavGroup activeIndex={activeLeft}>
              <NavItem
                label="Campaigns"
                isActive={activeLeft === 0}
                onClick={() => setActiveLeft(0)}
                onMouseEnter={() => handleMouseEnter('left', 0)}
              >
                <NavDropdown isOpen={openDropdown === 'left-0'}>
                  <Link to="/user/outbound_campaigns" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdCampaign className="w-4 h-4" />
                    <span>OutBound Campaigns</span>
                  </Link>
                  <Link to="/user/inbound_campaigns" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdCampaign className="w-4 h-4" />
                    <span>Inbound Campaigns</span>
                  </Link>
                  <Link to="/user/outbound_campaigns" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdCampaign className="w-4 h-4" />
                    <span>SMS OutBound Campaigns</span>
                  </Link>
                  <Link to="/user/inbound_campaigns" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdCampaign className="w-4 h-4" />
                    <span>SMS Inbound Campaigns</span>
                  </Link>
                </NavDropdown>
              </NavItem>
  
              <NavItem
                label="Call Logs"
                isActive={activeLeft === 1}
                onClick={() => setActiveLeft(1)}
                onMouseEnter={() => handleMouseEnter('left', 1)}
              >
                <NavDropdown isOpen={openDropdown === 'left-1'}>
                  <Link to="/user/outbound_call_logs" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <BsTelephoneOutboundFill className="w-4 h-4" />
                    <span>Outbound Call Logs</span>
                  </Link>
                  <Link to="/user/inbound_call_logs" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <BsTelephoneInbound className="w-4 h-4" />
                    <span>Inbound Call Logs</span>
                  </Link>
                  <Link to="/user/test_logs" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <BsTelephone className="w-4 h-4" />
                    <span>Sample Voice Call Log</span>
                  </Link>
                  <Link to="/user/test_sms_logs" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdOutlineTextsms className="w-4 h-4" />
                    <span>Sample SMS Followup Logs</span>
                  </Link>
                </NavDropdown>
              </NavItem>
  
              <NavItem
                label="Phone #"
                isActive={activeLeft === 2}
                onClick={() => setActiveLeft(2)}
                onMouseEnter={() => handleMouseEnter('left', 2)}
              />
            </NavGroup>
  
            {/* Center Logo */}
            <div className="absolute left-1/2 transform -translate-x-1/2">
              <Logo />
            </div>
  
            {/* Right section */}
            <NavGroup activeIndex={activeRight}>
              <NavItem
                label="Assistant"
                isActive={activeRight === 0}
                onClick={() => setActiveRight(0)}
                onMouseEnter={() => handleMouseEnter('right', 0)}
              >
                <NavDropdown isOpen={openDropdown === 'right-0'}>
                  <a href="/user/onboarding" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdHandshake className="w-4 h-4" />
                    <span>Onboarding</span>
                  </a>
                  <a href="/user/voice" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdRecordVoiceOver className="w-4 h-4" />
                    <span>Voice Assistant Test</span>
                  </a>
                  <a href="/user/sms" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdOutlineTextsms className="w-4 h-4" />
                    <span>SMS Assistant Test</span>
                  </a>
                  <a href="/user/assistants" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdAssistant className="w-4 h-4" />
                    <span>Voice Assistants</span>
                  </a>
                  <a href="/user/voice_list" className="px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-3 cursor-pointer">
                    <MdRecordVoiceOver className="w-4 h-4" />
                    <span>Cloned Voices</span>
                  </a>
                </NavDropdown>
              </NavItem>
  
              <NavItem
                label="Billing"
                isActive={activeRight === 1}
                onClick={() => setActiveRight(1)}
                onMouseEnter={() => handleMouseEnter('right', 1)}
              />
              
              <NavItem
                label="Profile"
                isActive={activeRight === 2}
                onClick={() => setActiveRight(2)}
                onMouseEnter={() => handleMouseEnter('right', 2)}
              />
            </NavGroup>
          </div>
        </div>
      </nav>
  );
};

export default TopHeader;
