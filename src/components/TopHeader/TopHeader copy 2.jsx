import React, { Fragment, useEffect, useState, useRef } from "react";
import { GlobalContext } from "Context/Global";
import { Link, useLocation, useNavigate, NavLink } from "react-router-dom";
import { BackButton } from "Components/BackButton";
import { AuthContext } from "Context/Auth";
import { StringCaser } from "Utils/utils";
import { useProfile } from "Hooks/useProfile";
import MkdSDK from "Utils/MkdSDK";
import clsx from "clsx";
import { twMerge } from "tailwind-merge";
import {
  MdAssistant,
  MdCampaign,
  MdFormatListNumbered,
  MdHandshake,
  MdHearing,
  MdOutlineAttachMoney,
  MdOutlineTextsms,
  MdRecordVoiceOver,
} from "react-icons/md";
import { PiUsersThreeFill } from "react-icons/pi";
import {
  BsTelephoneInbound,
  BsTelephoneOutboundFill,
  BsTelephone,
} from "react-icons/bs";
import { ChevronDown, Heart, Search, ShoppingCart, User } from "lucide-react";
import { Menu, Transition, Dialog } from "@headlessui/react";
import { UserCircleIcon } from "@heroicons/react/24/solid";
import { showToast } from "Context/Global";
import { automateIcon } from "Assets/images";

const NAV_ITEMS = [
  {
    to: "/user/onboarding",
    text: "Onboarding",
    icon: <MdHandshake className="text-xl text-[#A8A8A8]" />,
    value: "onboarding",
  },
  {
    to: "/user/voice",
    text: "Voice Assistant Test",
    icon: <MdRecordVoiceOver className="text-xl text-[#A8A8A8]" />,
    value: "voice",
  },
  {
    to: "/user/sms",
    text: "SMS Assistant Test",
    icon: <MdOutlineTextsms className="text-xl text-[#A8A8A8]" />,
    value: "sms",
  },
  {
    to: "/user/voice_list",
    text: "Cloned Voices",
    icon: <MdRecordVoiceOver className="text-xl text-[#A8A8A8]" />,
    value: "voice_list",
  },
  {
    to: "/user/assistants",
    text: "Voice Assistants",
    icon: <MdAssistant className="text-xl text-[#A8A8A8]" />,
    value: "assistants",
  },
  {
    to: "/user/numbers",
    text: "Phone #",
    icon: <MdFormatListNumbered className="text-xl text-[#A8A8A8]" />,
    value: "numbers",
  },
  {
    to: "/user/outbound_campaigns",
    text: "OutBound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "outbound_campaigns",
  },
  {
    to: "/user/inbound_campaigns",
    text: "Inbound Campaigns",
    icon: <MdHearing className="text-xl text-[#A8A8A8]" />,
    value: "inbound_campaigns",
  },
  {
    to: "/user/outbound_campaigns",
    text: "SMS OutBound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "sms_outbound_campaigns",
  },
  {
    to: "/user/inbound_campaigns",
    text: "SMS Inbound Campaigns",
    icon: <MdCampaign className="text-xl text-[#A8A8A8]" />,
    value: "sms_inbound_campaigns",
  },
  {
    to: "/user/outbound_call_logs",
    text: "Outbound Call Logs",
    icon: <BsTelephoneOutboundFill className="text-xl text-[#A8A8A8]" />,
    value: "outbound_call_logs",
  },
  {
    to: "/user/inbound_call_logs",
    text: "Inbound Call Logs",
    icon: <BsTelephoneInbound className="text-xl text-[#A8A8A8]" />,
    value: "inbound_call_logs",
  },
  {
    to: "/user/test_logs",
    text: "Sample Voice Call Log",
    icon: <BsTelephone className="text-xl text-[#A8A8A8]" />,
    value: "test_call_logs",
  },
  {
    to: "/user/test_sms_logs",
    text: "Sample SMS Followup Logs",
    icon: <MdOutlineTextsms className="text-xl text-[#A8A8A8]" />,
    value: "test_sms_logs",
  },
  {
    to: "/user/stripe_subscription",
    text: "Billing",
    icon: <MdOutlineAttachMoney className="text-xl text-[#A8A8A8]" />,
    value: "subscription",
  },
  {
    to: "/user/profile",
    text: "Profile",
    icon: <PiUsersThreeFill className="text-xl text-[#A8A8A8]" />,
    value: "profile",
  },
];

let sdk = new MkdSDK();

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}
const TopHeader = () => {
  const [isProductsOpen, setIsProductsOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState("campaigns");
  const dropdownRef = useRef(null);
  const timeoutRef = useRef();

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsProductsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsProductsOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsProductsOpen(false);
    }, 200);
  };

  return (
    <nav className="fixed left-0 right-0 top-0 z-50 border-b bg-gradient-to-r from-white via-gray-50 to-gray-100">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex flex-shrink-0 items-center">
            <span className="text-xl font-bold">Fujies</span>
          </div>

          {/* Navigation Links */}
          <div className="mx-8 hidden flex-1 items-center justify-center md:flex">
            <div className="relative flex items-center rounded-full bg-[#F1F1F1] p-1">
              {/* Sliding background indicator */}
              <div
                className={cn(
                  "absolute h-[calc(100%-8px)] rounded-full bg-[#222222] transition-all duration-300 ease-in-out",
                  selectedItem === "campaigns" && "left-1 w-[100px]",
                  selectedItem === "products" && "left-[108px] w-[110px]",
                  selectedItem === "services" && "left-[225px] w-[100px]",
                  selectedItem === "about" && "left-[332px] w-[90px]",
                  selectedItem === "contact" && "left-[429px] w-[100px]"
                )}
              />

              <div
                className="relative"
                ref={dropdownRef}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <button
                  onClick={() => {
                    setIsProductsOpen(!isProductsOpen);
                    setSelectedItem("campaigns");
                  }}
                  className={cn(
                    "relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300",
                    selectedItem === "campaigns"
                      ? "text-white"
                      : "text-gray-700 hover:text-gray-900"
                  )}
                >
                  Campaigns
                  <ChevronDown className="ml-1 h-4 w-4" />
                </button>

                {isProductsOpen && (
                  <div className="absolute left-0 top-full mt-1 w-48 rounded-md bg-white py-1 shadow-lg">
                    <Link
                      to="/user/outbound_campaigns"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      OutBound Campaigns
                    </Link>
                    <Link
                      to="/user/inbound_campaigns"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Inbound Campaigns
                    </Link>
                    <Link
                      to="/user/sms_outbound_campaigns"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      SMS OutBound Campaigns
                    </Link>
                    <Link
                      to="/user/sms_inbound_campaigns"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      SMS Inbound Campaigns
                    </Link>
                  </div>
                )}
              </div>

              <div
                className="relative"
                ref={dropdownRef}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <button
                  onClick={() => {
                    setIsProductsOpen(!isProductsOpen);
                    setSelectedItem("services");
                  }}
                  className={cn(
                    "relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300",
                    selectedItem === "services"
                      ? "text-white"
                      : "text-gray-700 hover:text-gray-900"
                  )}
                >
                  Assistant
                  <ChevronDown className="ml-1 h-4 w-4" />
                </button>

                {isProductsOpen && (
                  <div className="absolute left-0 top-full mt-1 w-48 rounded-md bg-white py-1 shadow-lg">
                    <Link
                      to="/user/onboarding"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Onboarding
                    </Link>
                    <Link
                      to="/user/voice"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Voice Assistant Test
                    </Link>
                    <Link
                      to="/user/sms"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      SMS Assistant Test
                    </Link>
                    <Link
                      to="/user/assistants"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Voice Assistants
                    </Link>
                    <Link
                      to="/user/voice_list"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Cloned Voices
                    </Link>
                  </div>
                )}
              </div>

              <div
                className="relative"
                ref={dropdownRef}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <button
                  onClick={() => {
                    setIsProductsOpen(!isProductsOpen);
                    setSelectedItem("call_logs");
                  }}
                  className={cn(
                    "relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300",
                    selectedItem === "call_logs"
                      ? "text-white"
                      : "text-gray-700 hover:text-gray-900"
                  )}
                >
                  Call Logs
                  <ChevronDown className="ml-1 h-4 w-4" />
                </button>

                {isProductsOpen && (
                  <div className="absolute left-0 top-full mt-1 w-48 rounded-md bg-white py-1 shadow-lg">
                    <Link
                      to="/user/outbound_call_logs"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Outbound Call Logs
                    </Link>
                    <Link
                      to="/user/inbound_call_logs"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Inbound Call Logs
                    </Link>
                    <Link
                      to="/user/test_logs"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sample Voice Call Log
                    </Link>
                    <Link
                      to="/user/test_sms_logs"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sample SMS Followup Logs
                    </Link>
                  </div>
                )}
              </div>

              <button
                onClick={() => {
                  setSelectedItem("about");
                  setIsProductsOpen(false);
                }}
                className={cn(
                  "relative z-10 rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300",
                  selectedItem === "about"
                    ? "text-white"
                    : "text-gray-700 hover:text-gray-900"
                )}
              >
                About
              </button>
              <button
                onClick={() => {
                  setSelectedItem("contact");
                  setIsProductsOpen(false);
                }}
                className={cn(
                  "relative z-10 rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300",
                  selectedItem === "contact"
                    ? "text-white"
                    : "text-gray-700 hover:text-gray-900"
                )}
              >
                Contact
              </button>
            </div>
          </div>

          {/* Right side icons */}
          <div className="hidden items-center space-x-6 md:flex">
            <button className="text-gray-900 hover:text-gray-600">
              <Search className="h-5 w-5" />
            </button>
            <button className="text-gray-900 hover:text-gray-600">
              <User className="h-5 w-5" />
            </button>
            <button className="text-gray-900 hover:text-gray-600">
              <Heart className="h-5 w-5" />
            </button>
            <button className="text-gray-900 hover:text-gray-600">
              <ShoppingCart className="h-5 w-5" />
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <button className="text-gray-900 hover:text-gray-600">
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default TopHeader;
