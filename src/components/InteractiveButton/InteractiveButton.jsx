import React, { memo, useId } from "react";
import Moon<PERSON>oader from "react-spinners/MoonLoader";

const InteractiveButton = memo(
  ({
    loading = false,
    disabled,
    children,
    type = "button",
    className,
    loaderclasses,
    onClick,
    color = "#ffffff",
  }) => {
    const override = {
      borderColor: "#ffffff",
    };
    const id = useId();
    return (
      <button
        type={type}
        disabled={disabled}
        className={`flex gap-5 justify-center items-center text-base ${className}`}
        onClick={onClick}
      >
        <>
          <MoonLoader
            color={color}
            loading={loading}
            cssOverride={override}
            size={20}
            className={loaderclasses}
            // aria-label="Loading Spinner"
            data-testid={id}
          />

          <span>{children}</span>
        </>
      </button>
    );
  }
);

export default InteractiveButton;
