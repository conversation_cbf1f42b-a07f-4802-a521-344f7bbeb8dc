import React, { useState } from 'react';

const SettingsPanel = () => {
  const [interruptSensitivity, setInterruptSensitivity] = useState(false);
  const [doubleCall, setDoubleCall] = useState(false);
  const [responseSpeed, setResponseSpeed] = useState(false);
  const [vmDetection, setVmDetection] = useState(false);
  const [initialMessageDelay, setInitialMessageDelay] = useState(5);
  const [aiCreativity, setAiCreativity] = useState(1);

  return (
    <div className="settings-panel">
      <div className="settings-option">
        <h3>Calendar Booking</h3>
        {/* Placeholder for any action */}
      </div>
      
      <div className="settings-option">
        <h3>Call Transfer</h3>
        {/* Placeholder for any action */}
      </div>

      <div className="settings-option">
        <h3>Advanced Options</h3>
        {/* Placeholder for any action */}
      </div>

      <div className="settings-slider">
        <label>Initial Message Delay: {initialMessageDelay} sec</label>
        <input
          type="range"
          min="0"
          max="10"
          value={initialMessageDelay}
          onChange={(e) => setInitialMessageDelay(e.target.value)}
        />
      </div>

      <div className="settings-slider">
        <label>AI Creativity: {aiCreativity}</label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={aiCreativity}
          onChange={(e) => setAiCreativity(e.target.value)}
        />
      </div>

      <div className="settings-toggle">
        <label>Interrupt Sensitivity: {interruptSensitivity ? 'High' : 'Low'}</label>
        <input
          type="checkbox"
          checked={interruptSensitivity}
          onChange={() => setInterruptSensitivity(!interruptSensitivity)}
        />
      </div>

      <div className="settings-toggle">
        <label>Response Speed: {responseSpeed ? 'Sensitive' : 'Auto'}</label>
        <input
          type="checkbox"
          checked={responseSpeed}
          onChange={() => setResponseSpeed(!responseSpeed)}
        />
      </div>

      <div className="settings-toggle">
        <label>Double Call: {doubleCall ? 'True' : 'False'}</label>
        <input
          type="checkbox"
          checked={doubleCall}
          onChange={() => setDoubleCall(!doubleCall)}
        />
      </div>

      <div className="settings-toggle">
        <label>VM Detection (Beta): {vmDetection ? 'On' : 'Off'}</label>
        <input
          type="checkbox"
          checked={vmDetection}
          onChange={() => setVmDetection(!vmDetection)}
        />
      </div>
    </div>
  );
};

export default SettingsPanel;
