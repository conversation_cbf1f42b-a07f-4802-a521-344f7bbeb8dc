import React from "react";
import { MdAdd, Md<PERSON><PERSON><PERSON> } from "react-icons/md";

export const RetrySettingsEditor = ({ value, onChange }) => {
  const updateSetting = (field, newValue) => {
    onChange({
      ...value,
      [field]: newValue,
    });
  };

  const updateRetryInterval = (index, newValue) => {
    const newIntervals = [...value.retry_intervals];
    newIntervals[index] = parseInt(newValue);
    updateSetting("retry_intervals", newIntervals);
  };

  const addRetryInterval = () => {
    const newIntervals = [...value.retry_intervals, 300];
    updateSetting("retry_intervals", newIntervals);
  };

  const removeRetryInterval = (index) => {
    const newIntervals = value.retry_intervals.filter((_, i) => i !== index);
    updateSetting("retry_intervals", newIntervals);
  };

  const formatDuration = (seconds) => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  };

  return (
    <div className="space-y-6">
      {/* Max Attempts */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Maximum Retry Attempts
        </label>
        <input
          type="number"
          min="1"
          max="10"
          value={value.max_attempts}
          onChange={(e) => updateSetting("max_attempts", parseInt(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <p className="text-sm text-gray-500 mt-1">
          Maximum number of times to retry a failed call (1-10)
        </p>
      </div>

      {/* Retry Intervals */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Retry Intervals (seconds)
        </label>
        <div className="space-y-2">
          {value.retry_intervals.map((interval, index) => (
            <div key={index} className="flex items-center gap-2">
              <span className="text-sm text-gray-600 w-16">
                Attempt {index + 2}:
              </span>
              <input
                type="number"
                min="30"
                value={interval}
                onChange={(e) => updateRetryInterval(index, e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <span className="text-sm text-gray-500 w-20">
                ({formatDuration(interval)})
              </span>
              {value.retry_intervals.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeRetryInterval(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <MdDelete className="text-lg" />
                </button>
              )}
            </div>
          ))}
        </div>
        {value.retry_intervals.length < value.max_attempts && (
          <button
            type="button"
            onClick={addRetryInterval}
            className="mt-2 flex items-center gap-2 px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <MdAdd className="text-sm" />
            Add Interval
          </button>
        )}
        <p className="text-sm text-gray-500 mt-1">
          Time to wait between retry attempts. Each attempt can have a different interval.
        </p>
      </div>

      {/* Retry Conditions */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Retry Conditions
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={value.retry_on_busy}
              onChange={(e) => updateSetting("retry_on_busy", e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Retry on busy signal</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={value.retry_on_no_answer}
              onChange={(e) => updateSetting("retry_on_no_answer", e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Retry on no answer</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={value.retry_on_failed}
              onChange={(e) => updateSetting("retry_on_failed", e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Retry on call failed</span>
          </label>
        </div>
        <p className="text-sm text-gray-500 mt-2">
          Select which call outcomes should trigger a retry attempt.
        </p>
      </div>

      {/* Preview */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Retry Schedule Preview</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <div>Initial attempt: Immediate</div>
          {value.retry_intervals.slice(0, value.max_attempts - 1).map((interval, index) => (
            <div key={index}>
              Retry {index + 1}: After {formatDuration(interval)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
