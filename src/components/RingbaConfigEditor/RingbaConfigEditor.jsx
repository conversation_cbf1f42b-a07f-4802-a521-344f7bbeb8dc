import React, { useState, useContext } from "react";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import { <PERSON>d<PERSON><PERSON><PERSON>, MdError, MdRefresh } from "react-icons/md";

const sdk = new MkdSDK();

export const RingbaConfigEditor = ({ value, onChange }) => {
  const { dispatch } = useContext(GlobalContext);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [campaigns, setCampaigns] = useState([]);
  const [loadingCampaigns, setLoadingCampaigns] = useState(false);

  const updateSetting = (field, newValue) => {
    onChange({
      ...value,
      [field]: newValue,
    });
    // Clear test result when settings change
    setTestResult(null);
  };

  const testConnection = async () => {
    if (!value.api_key) {
      showToast(dispatch, "Please enter an API key first", 5000, "error");
      return;
    }

    try {
      setTesting(true);
      setTestResult(null);
      
      const result = await sdk.ringbaAPI.testConnection({
        api_key: value.api_key,
        campaign_id: value.campaign_id,
      });
      
      setTestResult({
        success: true,
        message: "Connection successful!",
        data: result,
      });
      
      showToast(dispatch, "Ringba connection test successful");
    } catch (error) {
      console.error("Ringba connection test failed:", error);
      setTestResult({
        success: false,
        message: error.message || "Connection failed",
      });
      showToast(dispatch, "Ringba connection test failed", 5000, "error");
    } finally {
      setTesting(false);
    }
  };

  const loadCampaigns = async () => {
    if (!value.api_key) {
      showToast(dispatch, "Please enter an API key first", 5000, "error");
      return;
    }

    try {
      setLoadingCampaigns(true);
      const result = await sdk.ringbaAPI.getCampaigns();
      setCampaigns(result.campaigns || []);
      showToast(dispatch, "Campaigns loaded successfully");
    } catch (error) {
      console.error("Error loading Ringba campaigns:", error);
      showToast(dispatch, "Error loading campaigns", 5000, "error");
    } finally {
      setLoadingCampaigns(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Enable Integration */}
      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={value.enabled}
            onChange={(e) => updateSetting("enabled", e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="ml-2 text-sm font-medium text-gray-700">
            Enable Ringba Integration
          </span>
        </label>
        <p className="text-sm text-gray-500 mt-1">
          Connect this campaign to Ringba for call tracking and analytics
        </p>
      </div>

      {value.enabled && (
        <>
          {/* API Key */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ringba API Key *
            </label>
            <input
              type="password"
              value={value.api_key}
              onChange={(e) => updateSetting("api_key", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter your Ringba API key"
            />
            <p className="text-sm text-gray-500 mt-1">
              You can find your API key in your Ringba account settings
            </p>
          </div>

          {/* Test Connection */}
          <div>
            <button
              type="button"
              onClick={testConnection}
              disabled={testing || !value.api_key}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {testing ? (
                <MdRefresh className="text-lg animate-spin" />
              ) : (
                <MdCheck className="text-lg" />
              )}
              {testing ? "Testing..." : "Test Connection"}
            </button>

            {testResult && (
              <div
                className={`mt-2 p-3 rounded-lg flex items-center gap-2 ${
                  testResult.success
                    ? "bg-green-50 text-green-800 border border-green-200"
                    : "bg-red-50 text-red-800 border border-red-200"
                }`}
              >
                {testResult.success ? (
                  <MdCheck className="text-lg" />
                ) : (
                  <MdError className="text-lg" />
                )}
                <span className="text-sm">{testResult.message}</span>
              </div>
            )}
          </div>

          {/* Campaign Selection */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Ringba Campaign
              </label>
              <button
                type="button"
                onClick={loadCampaigns}
                disabled={loadingCampaigns || !value.api_key}
                className="text-blue-600 hover:text-blue-800 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loadingCampaigns ? "Loading..." : "Refresh"}
              </button>
            </div>
            
            {campaigns.length > 0 ? (
              <select
                value={value.campaign_id}
                onChange={(e) => updateSetting("campaign_id", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select a campaign</option>
                {campaigns.map((campaign) => (
                  <option key={campaign.id} value={campaign.id}>
                    {campaign.name} (ID: {campaign.id})
                  </option>
                ))}
              </select>
            ) : (
              <input
                type="text"
                value={value.campaign_id}
                onChange={(e) => updateSetting("campaign_id", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter Ringba campaign ID"
              />
            )}
            <p className="text-sm text-gray-500 mt-1">
              Select or enter the Ringba campaign ID to associate with this lead campaign
            </p>
          </div>

          {/* Webhook URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Webhook URL (Optional)
            </label>
            <input
              type="url"
              value={value.webhook_url}
              onChange={(e) => updateSetting("webhook_url", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://your-webhook-endpoint.com/ringba"
            />
            <p className="text-sm text-gray-500 mt-1">
              Optional webhook URL to receive real-time call events from Ringba
            </p>
          </div>

          {/* Configuration Summary */}
          {value.api_key && value.campaign_id && (
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                Integration Summary
              </h4>
              <div className="text-sm text-blue-800 space-y-1">
                <div>✓ API Key configured</div>
                <div>✓ Campaign ID: {value.campaign_id}</div>
                {value.webhook_url && <div>✓ Webhook URL configured</div>}
                {testResult?.success && <div>✓ Connection tested successfully</div>}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};
