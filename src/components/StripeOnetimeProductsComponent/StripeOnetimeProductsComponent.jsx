import React, { useState, Fragment } from "react";
import MkdSDK from "Utils/MkdSDK";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { GlobalContext, showToast } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";

const StripeOnetimeProductsComponent = ({ isOpen, onClose }) => {
  const sdk = new MkdSDK();
  const { dispatch, state } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [productsList, setProductsList] = useState([]);

  const checkout = async ({
    priceId,
    priceCurrency,
    priceStripeId,
    priceName,
    productName,
    price,
    quantity = 1,
  }) => {
    let params = {
      success_url: `${sdk.fe_baseurl}/user/stripe_subscription?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${sdk.fe_baseurl}/user/stripe_subscription?success=false&session_id={CHECKOUT_SESSION_ID}`,
      mode: "payment",
      payment_method_types: ["card"],
      shipping_address_collection: {
        allowed_countries: ["CA", "US"],
      },
      shipping_options: [
        {
          shipping_rate_data: {
            type: "fixed_amount",
            display_name: "Shipping fees",
            fixed_amount: {
              currency: priceCurrency,
              amount: 500,
            },
          },
        },
      ],
      locale: "en",
      line_items: [
        {
          price: priceStripeId,
          quantity,
        },
      ],
      phone_number_collection: {
        enabled: false,
      },
      payment_intent_data: {
        metadata: {
          app_price_id: priceId,
          app_product_name: priceName,
          app_highlevel_product_name: productName,
          is_order: "true",
        },
      },
      price,
    };

    try {
      const {
        error,
        model: checkout,
        message,
      } = await sdk.initCheckoutSession(params);
      if (error) {
        showToast(globalDispatch, message);
        return;
      }
      if (checkout?.url) location.href = checkout.url;
    } catch (error) {
      console.error("Error", error);
      showToast(globalDispatch, error.message, 7500);
      tokenExpireError(dispatch, error.code);
    }
  };

  const fetchOnetimeProducts = async () => {
    try {
      const data = await sdk.getStripePrices(
        { limit: "all" },
        { type: "one_time" }
      );
      if (data.error) {
        showToast(globalDispatch, data.message, 7500);
        return;
      }
      setProductsList(data.list);
    } catch (error) {
      showToast(globalDispatch, error.message, 7500);
      tokenExpireError(dispatch, error.code);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "billing",
      },
    });

    fetchOnetimeProducts();
  }, []);

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-[100]" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/50" />
        </Transition.Child>

        <div className="overflow-y-auto fixed inset-0">
          <div className="flex justify-center items-center p-4 min-h-full">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="mx-auto w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex justify-between items-center mb-4">
                  <Dialog.Title className="text-3xl font-medium text-white">
                    Buy Credits
                  </Dialog.Title>
                  <button type="button" onClick={onClose}>
                    <XMarkIcon className="w-6 h-6 text-white" />
                  </button>
                </div>

                <div className="my-4 rounded bg-[#1d2937] shadow-lg">
                  <div className="container flex flex-wrap gap-3 py-5">
                    {productsList.map((product, index) => {
                      return (
                        <div
                          className="flex justify-center mx-1 rounded-lg border"
                          key={index}
                        >
                          <div className="max-w-sm rounded-lg bg-[#1d2937] shadow-lg">
                            <div className="bg-transparent">
                              <img
                                className="object-cover max-h-40 rounded-t-lg"
                                src="https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-product-1_large.png?format=jpg&quality=90&v=1530129292"
                                alt=""
                              />
                            </div>
                            <div className="p-6 text-center">
                              <h3 className="mb-2 text-2xl font-medium text-white">
                                {product.product_name}
                              </h3>
                              <h5 className="mb-2 text-lg font-medium text-white text-slate-500">
                                {product.name}
                              </h5>
                              <p className="mb-4 text-base text-white">
                                ${+product.amount}
                              </p>
                              <button
                                onClick={() =>
                                  checkout({
                                    priceId: product.id,
                                    priceName: product.name,
                                    productName: product.product_name,
                                    priceStripeId: product.stripe_id,
                                    priceCurrency: product.currency,
                                    price: product.amount,
                                  })
                                }
                                type="button"
                                className="inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-[#2cc9d5]/70 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg"
                              >
                                Buy
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default StripeOnetimeProductsComponent;
