import { useSearchParams } from "react-router-dom";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function PaginationBarV2({ paginationData, className }) {
  const { currentPage, pageSize, totalNumber, totalPages } = paginationData;
  const [searchParams, setSearchParams] = useSearchParams();

  function onPageChange(e) {
    searchParams.set("page", e.target.value);
    setSearchParams(searchParams, { replace: true });
  }

  function onPageSizeChange(e) {
    searchParams.set("limit", e.target.value);
    setSearchParams(searchParams, { replace: true });
  }

  return (
    <div
      className={`flex items-center justify-between py-4 ${className ?? ""}`}
    >
      <div className="flex gap-4 items-center text-white">
        <p className="text-sm">
          Page{" "}
          <span className="font-medium">
            <strong>
              {totalNumber < 1
                ? 0
                : currentPage > 1
                ? (currentPage - 1) * pageSize + 1
                : currentPage}
            </strong>
            -
            <strong>
              {currentPage * pageSize < totalNumber
                ? currentPage * pageSize
                : totalNumber}
            </strong>{" "}
            of <strong>{totalNumber}</strong>
          </span>{" "}
        </p>
        <select
          className="w-[104px] rounded-lg bg-[#1d2937] py-1 pr-[30px]  text-sm text-white hover:bg-[#2d3947] focus:outline-none focus:ring-2 focus:ring-[#19b2f6]"
          value={pageSize}
          onChange={onPageSizeChange}
        >
          {[5, 10, 20, 30, 40, 50, "ALL"].map((pageSize) => (
            <option
              key={pageSize}
              value={pageSize == "ALL" ? 1000000 : pageSize}
              className="bg-[#1d2937]"
            >
              Show {pageSize}
            </option>
          ))}
        </select>
      </div>

      <div className="flex gap-1 items-center">
        <button
          className="flex h-7 w-7 items-center justify-center rounded-lg bg-[#1d2937] text-white transition-colors hover:bg-[#2d3947] disabled:opacity-50 disabled:hover:bg-[#1d2937]"
          disabled={currentPage == 1}
          onClick={() => {
            searchParams.set("page", +currentPage - 1);
            setSearchParams(searchParams, { replace: true });
          }}
        >
          <ChevronLeft className="w-5 h-5" />
        </button>

        <select
          className="rounded-md bg-[#1d2937] py-1 pl-[18px] pr-[40px] text-sm text-white hover:bg-[#2d3947] focus:outline-none focus:ring-2 focus:ring-[#19b2f6]"
          value={currentPage}
          onChange={onPageChange}
        >
          {Array(totalPages)
            .fill("")
            .map((_, pageSize) => (
              <option
                key={pageSize + 1}
                value={pageSize + 1}
                className="bg-[#1d2937]"
              >
                Page {pageSize + 1}
              </option>
            ))}
        </select>

        <button
          className="flex h-7 w-7 items-center justify-center rounded-md bg-[#1d2937] text-white transition-colors hover:bg-[#2d3947] disabled:opacity-50 disabled:hover:bg-[#1d2937]"
          disabled={currentPage >= totalPages}
          onClick={() => {
            searchParams.set("page", +currentPage + 1);
            setSearchParams(searchParams, { replace: true });
          }}
        >
          <ChevronRight className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
}
