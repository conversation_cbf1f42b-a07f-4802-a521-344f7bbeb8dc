import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";

const sdk = new MkdSDK();

const UserViewEmailLogPage = () => {
  const { id } = useParams();
  const [log, setLog] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchLog = async () => {
      try {
        sdk.setTable("email_logs");
        const res = await sdk.get(id);
        setLog(res);
      } catch (e) {
        setError("Failed to fetch email log");
      } finally {
        setLoading(false);
      }
    };
    fetchLog();
  }, [id]);

  if (loading) return <div className="p-8">Loading...</div>;
  if (error) return <div className="p-8 text-red-500">{error}</div>;
  if (!log) return <div className="p-8">No log found.</div>;

  return (
    <div className="mx-auto mt-10 max-w-2xl rounded bg-white p-8 shadow">
      <h2 className="mb-4 text-xl font-bold">Email Log Details</h2>
      <div className="mb-4">
        <div className="font-semibold">From:</div>
        <div>{log.from}</div>
      </div>
      <div className="mb-4">
        <div className="font-semibold">To:</div>
        <div>{log.to}</div>
      </div>
      <div className="mb-4">
        <div className="font-semibold">Subject:</div>
        <div>{log.subject}</div>
      </div>
      <div className="mb-4">
        <div className="font-semibold">Incoming Message:</div>
        <div className="mt-1 whitespace-pre-wrap rounded bg-gray-100 p-3">
          {log.incoming_message}
        </div>
      </div>
      <div className="mb-4">
        <div className="font-semibold">AI-Drafted Reply:</div>
        <div className="mt-1 whitespace-pre-wrap rounded bg-blue-50 p-3">
          {log.ai_reply}
        </div>
      </div>
      <button
        className="cursor-not-allowed rounded bg-blue-400 px-6 py-2 text-white opacity-50"
        disabled
      >
        Send
      </button>
    </div>
  );
};

export default UserViewEmailLogPage;
