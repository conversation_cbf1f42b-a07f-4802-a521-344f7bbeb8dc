import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import MkdSDK from 'Utils/MkdSDK';
import { useNavigate } from 'react-router-dom';
import { tokenExpireError } from 'Context/Auth';
import { GlobalContext, showToast } from 'Context/Global';
import ReactQuill from 'react-quill';

import { isImage, empty, isVideo, isPdf } from 'Utils/utils';
import { MkdInput } from 'Components/MkdInput';
import { InteractiveButton } from 'Components/InteractiveButton';
import { AuthContext } from "Context/Auth";
import { SkeletonLoader } from 'Components/Skeleton';
// import MkdSDK from "../../../utils/MkdSDK";
let sdk = new MkdSDK();


const UserAddScriptsTablePage = ({ closeSidebar }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state } = React.useContext(AuthContext);
  const schema = yup
    .object({
      script: yup.string(),
      // status: yup.string(),
    })
    .required();

  const { dispatch } = React.useContext(GlobalContext);
  const [fileObj, setFileObj] = React.useState({});
  const [scripts, setScripts] = React.useState([]);
  const [voices, setVoices] = React.useState([]);
  const [isSubmitLoading, setIsSubmitLoading] = React.useState(false);

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const [pageDetails, setPageDetails] = React.useState([]);

  const getPageDetails = async () => {
    const result = await new TreeSDK()
      .getList('table')
      .catch((e) => console.error(object));
    setPageDetails(result.list);
  };

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    let sdk = new MkdSDK();
    setIsSubmitLoading(true);
    try {
      // for (let item in fileObj) {
      //   let formData = new FormData();
      //   formData.append('file', fileObj[item].file);
      //   let uploadResult = await sdk.uploadImage(formData);
      //   _data[item] = uploadResult.url;
      // }

      sdk.setTable('scripts');

      const result = await sdk.callRestAPI(
        {
          script: _data.script,
          user_id: state.user
        },
        'POST',
      );
      if (!result.error) {
        showToast(globalDispatch, 'Added');
        navigate('/user/scripts');
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: 'manual',
              message: result.validation[field],
            });
          }
        }
      }
      setIsSubmitLoading(false);
      if (closeSidebar) {
        closeSidebar()
      }
    } catch (error) {
      setIsSubmitLoading(false);
      console.log('Error', error);
      setError('script', {
        type: 'manual',
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: 'SETPATH',
      payload: {
        path: 'scripts',
      },
    });
    (async function getScripts(){
      sdk.setTable("scripts");
      console.log("state", state.user)
      const result = await sdk.callRestAPI({ user_id: state.user}, "GETALL");
      if (!result.error) {
        console.log("scripts", result)
        setScripts(result.list ? result.list.map(script => script.script) : [])
      }
    })();
    (async function getVoices(){
      sdk.setTable("voice_list");
      console.log("state", state.user)
      const result = await sdk.callRestAPI({ user_id: state.user}, "GETALL");
      if (!result.error) {
        console.log("voices", result)
        setVoices(result.list ? result.list.map(script => {
          return {
            voice_id:script.voice_id,
            name:script.name
          }
        }) : [])
      }
    })();
  }, []);

  return (
    <div className="mx-auto">
      <h4 className="text-3xl font-semibold">Add Script</h4>
      <form className="w-full mt-4" onSubmit={handleSubmit(onSubmit)}>

        <MkdInput
          type={'textarea'}
          page={'add'}
          name={'script'}
          errors={errors}
          label={'Script'}
          placeholder={'Script'}
          register={register}
          className={''}
          rows={40}
        />


        <InteractiveButton
          type="submit"
          loading={isSubmitLoading}
          disabled={isSubmitLoading}
          className="focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none"
        >
          Submit
        </InteractiveButton>
      </form>
    </div>
  );
};

export default UserAddScriptsTablePage;
