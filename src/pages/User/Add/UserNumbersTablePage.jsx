import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import MkdSDK from 'Utils/MkdSDK';
import { useNavigate } from 'react-router-dom';
import { tokenExpireError } from 'Context/Auth';
import { GlobalContext, showToast } from 'Context/Global';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { isImage, empty, isVideo, isPdf } from 'Utils/utils';
import { MkdInput } from 'Components/MkdInput';
import { InteractiveButton } from 'Components/InteractiveButton';
import { SkeletonLoader } from 'Components/Skeleton';
import { AuthContext } from "Context/Auth";
let sdk = new MkdSDK();
const UserNumbersTablePage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state } = React.useContext(AuthContext);
  const schema = yup
    .object({
      number: yup.string(),
      campaign: yup.string(),
      // status: yup.string(),
    })
    .required();

  const { dispatch } = React.useContext(GlobalContext);
  const [fileObj, setFileObj] = React.useState({});
  const [campaigns, setCampaigns] = React.useState([]);
  const [isSubmitLoading, setIsSubmitLoading] = React.useState(false);

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const [pageDetails, setPageDetails] = React.useState([]);

  const getPageDetails = async () => {
    const result = await new TreeSDK()
      .getList('table')
      .catch((e) => console.error(object));
    setPageDetails(result.list);
  };

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    let sdk = new MkdSDK();
    setIsSubmitLoading(true);
    try {

      sdk.setTable('numbers');

      const result = await sdk.callRestAPI(
        {
          number: _data.number,
          campaign: _data.campaign,
          user_id: state.user,
          status: 1,
        },
        'POST',
      );
      if (!result.error) {
        showToast(globalDispatch, 'Added');
        navigate('/user/numbers');
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: 'manual',
              message: result.validation[field],
            });
          }
        }
      }
      setIsSubmitLoading(false);
    } catch (error) {
      setIsSubmitLoading(false);
      console.log('Error', error);
      setError('number', {
        type: 'manual',
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: 'SETPATH',
      payload: {
        path: 'numbers',
      },
    });

    (async function getCampaigns(){
      sdk.setTable("campaign");
      const result = await sdk.callRestAPI({ user_id: state.user,filter:[`user_id,eq,${state.user}`]}, "GETALL");
      if (!result.error) {
        setCampaigns(result.list ? result.list.map(script => script.name) : [])
      }
    })();
    
    
  }, []);

  return (
    <div className=" mx-auto rounded  p-5 shadow-md">
      <h4 className="text-2xl font-medium">Add New Number</h4>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <MkdInput
          type={'text'}
          page={'add'}
          name={'number'}
          errors={errors}
          label={'Number'}
          placeholder={'Phone Number'}
          register={register}
          className={''}
        />

        <MkdInput
          type={'dropdown'}
          page={'add'}
          name={'campaign'}
          errors={errors}
          label={'Campaign'}
          placeholder={'Campaign'}
          options={campaigns}
          register={register}
          className={''}
        />

        {/* <MkdInput
          type={'text'}
          page={'add'}
          name={'status'}
          errors={errors}
          label={'Status'}
          placeholder={'Status'}
          register={register}
          className={''}
        /> */}

        <InteractiveButton
          type="submit"
          loading={isSubmitLoading}
          disabled={isSubmitLoading}
          className="focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none"
        >
          Submit
        </InteractiveButton>
      </form>
    </div>
  );
};

export default UserNumbersTablePage;
