import React, { Fragment, useMemo, useCallback } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { MkdInput } from "Components/MkdInput";
import { InteractiveButton } from "Components/InteractiveButton";
import { AuthContext } from "Context/Auth";
import { useCSVReader } from "react-papaparse";
import { TrashIcon } from "@heroicons/react/24/solid";
import { DocumentTextIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { Dialog, Transition } from "@headlessui/react";
import { useDropzone } from "react-dropzone";
import PizZip from "pizzip";
import { DOMParser } from "@xmldom/xmldom";
import { InformationCircleIcon } from "@heroicons/react/24/outline";
import pdfToText from "react-pdftotext";
import moment from "moment-timezone";

// words
// Claude <PERSON>ku 3: 84,375 words
// GPT-3.5: 2,304 words
// GPT-4 (Standard Model): 4,608 words
// GPT-4 (Extended Context Model): 18,432 words
const MAX_CONTENT_SIZE_LIMIT = 2000;
let sdk = new MkdSDK();

const requiredFields = ["phone", "first_name", "last_name"];

const languages = {
  "English (USA)": "English (USA)",
  "English (UK)": "English (UK)",
  "English (Australia)": "English (Australia)",
  "English (Canada)": "English (Canada)",
  Japanese: "Japanese",
  Chinese: "Chinese",
  German: "German",
  Hindi: "Hindi",
  "French (France)": "French (France)",
  "French (Canada)": "French (Canada)",
  Korean: "Korean",
  "Portuguese (Brazil)": "Portuguese (Brazil)",
  "Portuguese (Portugal)": "Portuguese (Portugal)",
  Italian: "Italian",
  "Spanish (Spain)": "Spanish (Spain)",
  "Spanish (Mexico)": "Spanish (Mexico)",
  Indonesian: "Indonesian",
  Dutch: "Dutch",
  Turkish: "Turkish",
  Filipino: "Filipino",
  Polish: "Polish",
  Swedish: "Swedish",
  Bulgarian: "Bulgarian",
  Romanian: "Romanian",
  "Arabic (Saudi Arabia)": "Arabic (Saudi Arabia)",
  "Arabic (UAE)": "Arabic (UAE)",
  Czech: "Czech",
  Greek: "Greek",
  Finnish: "Finnish",
  Croatian: "Croatian",
  Malay: "Malay",
  Slovak: "Slovak",
  Danish: "Danish",
  Tamil: "Tamil",
  Ukrainian: "Ukrainian",
  Russian: "Russian",
};

const UserAddSMSOutboundCampaignPage = ({ isOpen, closeSidebar }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState, dispatch: authDispatch } =
    React.useContext(AuthContext);
  const schema = yup
    .object({
      name: yup.string(),
      assistant_id: yup.string(),
      start_time: yup.string(),
      valid_time_opening: yup.string(),
      valid_time_closing: yup.string(),
      days: yup.string(),
      language: yup.string().required("Language is required"),
    })
    .required();

  const [assistants, setAssistants] = React.useState([]);
  const { CSVReader } = useCSVReader();
  const [csvResults, setCsvResults] = React.useState(null);
  const [previewCSV, setPreviewCSV] = React.useState(false);
  const [documents, setDocuments] = React.useState(null);
  const [previewDocuments, setPreviewDocuments] = React.useState(false);
  const [numbers, setNumbers] = React.useState([]);
  const [knowledgeField, setKnowledgeField] = React.useState("");
  const [preview, showPreview] = React.useState(false);
  const [settings, setSettings] = React.useState([]);
  const [modelLimit, setModelLimit] = React.useState(2000);
  const [localTimeOpening, setLocalTimeOpening] = React.useState("");
  const [startDate, setDateChange] = React.useState("");
  const [localTimeClosing, setLocalTimeClosing] = React.useState("");
  const [gmtTime, setGmtTime] = React.useState("");
  const [gmtTimeClose, setGmtTimeClose] = React.useState("");
  const limits = {
    anthropic: {
      totalTokens: 150000,
      usableTokensForPrompt: 112500,
      MAX_CONTENT_SIZE_LIMIT: 84375,
    },
    openai: {
      totalTokens: 4096,
      usableTokensForPrompt: 3072,
      MAX_CONTENT_SIZE_LIMIT: 2304,
    },
    gpt_4: {
      totalTokens: 8192,
      usableTokensForPrompt: 6144,
      MAX_CONTENT_SIZE_LIMIT: 4608,
    },
    gpt_4_extended: {
      totalTokens: 32768,
      usableTokensForPrompt: 24576,
      MAX_CONTENT_SIZE_LIMIT: 18432,
    },
  };
  const days_mapping = {
    0: "All",
    1: "Sundays only",
    2: "Mondays only",
    3: "Tuesdays only",
    4: "Wednesdays only",
    5: "Thursdays only",
    6: "Fridays only",
    7: "Saturdays only",
    8: "Weekdays only",
    9: "Weekends only",
  };

  const parsedCSV = useMemo(() => {
    const headers = csvResults?.data[0] ?? [];
    return {
      headers,
      data:
        csvResults?.data
          .slice(1)
          .filter((row) => !(row.length == 1 && row[0] === ""))
          .map((row) => {
            let obj = {};
            row.forEach((cell, index) => {
              obj[headers[index]] = cell;
            });
            return obj;
          }) ?? [],
    };
  }, [csvResults]);

  const DocumentUploader = ({ allow_preview }) => {
    const handleDocxFile = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = async (e) => {
          try {
            const c = e.target.result;
            const paragraphs = getParagraphs(c);
            const content = paragraphs;
            const wordCount = content.split(/\s+/).length;
            console.log(content, "from docx");
            resolve({ content, wordCount });
          } catch (error) {
            reject(error);
          }
        };

        reader.onerror = (err) => reject(err);

        reader.readAsArrayBuffer(file);
      });
    };

    const handleTxTFile = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = function () {
          try {
            const content = reader.result;
            const wordCount = content.split(/\s+/).length;
            console.log(content, "from docx");
            resolve({ content, wordCount });
          } catch (e) {
            reject(e);
          }
        };
        reader.readAsText(file);
      });
    };

    function str2xml(str) {
      if (str.charCodeAt(0) === 65279) {
        // BOM sequence
        str = str.substr(1);
      }
      return new DOMParser().parseFromString(str, "text/xml");
    }

    function getParagraphs(content) {
      const zip = new PizZip(content);
      const xml = str2xml(zip.files["word/document.xml"].asText());
      const paragraphsXml = xml.getElementsByTagName("w:p");
      const paragraphs = [];

      for (let i = 0, len = paragraphsXml.length; i < len; i++) {
        let fullText = "";
        const textsXml = paragraphsXml[i].getElementsByTagName("w:t");
        for (let j = 0, len2 = textsXml.length; j < len2; j++) {
          const textXml = textsXml[j];
          if (textXml.childNodes) {
            fullText += textXml.childNodes[0].nodeValue;
          }
        }
        if (fullText) {
          paragraphs.push(fullText);
        }
      }
      return paragraphs.join(" ");
    }

    const onDrop = useCallback(async (acceptedFiles) => {
      const updatedDocuments = [];
      let total_content = "";

      for (const file of acceptedFiles) {
        let wordCount = 0;
        let content = "";

        // Extract text content based on file type
        if (file.type === "application/pdf") {
          try {
            const text = await pdfToText(file);
            wordCount = text.split(/\s+/).length;
            content = text;
          } catch (error) {
            console.error("Error reading PDF file:", error);
          }
        } else if (
          file.type ===
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ) {
          const result = await handleDocxFile(file);
          content = result.content;
          wordCount = result.wordCount;
        } else if (file.type === "text/plain") {
          const result = await handleTxTFile(file);
          content = result.content;
          wordCount = result.wordCount;
          console.log("Word Count:", wordCount, content, "from txt");
        }
        console.log(wordCount, "wordCount");
        total_content += content;
        updatedDocuments.push({
          name: file.name,
          size: file.size,
          wordCount,
          content,
        });
      }

      const totalSize = updatedDocuments.reduce(
        (acc, doc) => acc + doc.wordCount,
        0
      );
      const limitExceeded = totalSize > modelLimit;
      if (!limitExceeded) {
        setKnowledgeField(total_content);
        showPreview(true);
      } else {
        setKnowledgeField("");
        showPreview(false);
        showToast(globalDispatch, "Word Limit Exceeded", 5000, "error");
      }

      setDocuments(updatedDocuments);
      setPreviewDocuments(true);
    }, []);

    const { getRootProps, getInputProps } = useDropzone({
      onDrop,
      accept: {
        "application/pdf": [".pdf"],
        "text/plain": [".txt"],
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
          [".docx"],
      },
    });

    return (
      <div className="mt-5 cursor-pointer">
        <label className="mb-2 block cursor-pointer text-sm font-bold text-white">
          {/* Upload Documents (Documents forming the companies knowledge base) */}
        </label>
        <div
          {...getRootProps({
            className: "dropzone",
          })}
        >
          <input {...getInputProps()} />
          <div className="flex h-60 w-full items-center justify-center rounded-md border-2 border-gray-600 p-4">
            <p className="text-center text-white">
              Drag 'n' drop some files here, or click to select files
            </p>
          </div>
        </div>

        {allow_preview && (
          <div className="mt-4">
            <h3 className="text-lg font-semibold">Uploaded Documents:</h3>
            <ul>
              {documents?.map((file, index) => (
                <li key={index} className="mt-2 text-sm text-white">
                  {file.name}
                </li>
              ))}
            </ul>
            <button
              type="button"
              className="mt-4 rounded-md bg-[#2cc9d5] px-4 py-2 text-white hover:bg-blue-600 focus:outline-none"
              onClick={() => setPreviewDocuments(true)}
            >
              Preview Documents
            </button>
          </div>
        )}
      </div>
    );
  };

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: "",
      assistant_id: "",
      from_number_id: "",
      days: 8,
    },
  });

  const DocumentPreview = () => {
    const totalSize =
      documents?.reduce((acc, doc) => acc + doc.wordCount, 0) || 0;
    const limitExceeded = totalSize > modelLimit;

    return (
      <div className="mt-4">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-xl font-bold text-white">Document Preview</h3>
          <p className="text-sm text-white/70">
            Max Words Allowed: {modelLimit.toLocaleString()} words
          </p>
        </div>

        {/* Summary Stats */}
        <div className="mb-4 grid grid-cols-3 gap-4">
          <div className="rounded-lg bg-[#2d3947] p-3">
            <div className="text-sm text-white/70">Total Files</div>
            <div className="text-lg font-semibold text-white">
              {documents?.length || 0}
            </div>
          </div>
          <div className="rounded-lg bg-[#2d3947] p-3">
            <div className="text-sm text-white/70">Total Words</div>
            <div className="text-lg font-semibold text-white">
              {totalSize.toLocaleString()}
            </div>
          </div>
          <div className="rounded-lg bg-[#2d3947] p-3">
            <div className="text-sm text-white/70">Status</div>
            <div
              className={`text-lg font-semibold ${
                limitExceeded ? "text-red-400" : "text-green-400"
              }`}
            >
              {limitExceeded ? "Limit Exceeded" : "Within Limit"}
            </div>
          </div>
        </div>

        {limitExceeded && (
          <div className="mb-4 rounded-lg border border-red-500/30 bg-red-500/20 p-4">
            <div className="mb-2 flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-red-400"></div>
              <h4 className="font-semibold text-red-400">
                Content Size Limit Exceeded
              </h4>
            </div>
            <p className="mb-1 text-sm text-red-300">
              Content size exceeds the limit of {modelLimit.toLocaleString()}{" "}
              words.
            </p>
            <p className="text-sm text-red-300">
              Please reduce the number of files or choose smaller documents.
            </p>
          </div>
        )}

        {/* Document List */}
        <div className="space-y-2">
          <h4 className="mb-3 text-lg font-semibold text-white">
            Uploaded Documents
          </h4>
          {documents?.length > 0 ? (
            <div className="space-y-2">
              {documents.map((doc, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg border border-white/10 bg-[#2d3947] p-3"
                >
                  <div className="flex items-center gap-3">
                    <DocumentTextIcon className="h-5 w-5 text-white/70" />
                    <div>
                      <div className="font-medium text-white">{doc.name}</div>
                      <div className="text-sm text-white/70">
                        {(doc.size / 1024).toFixed(2)} KB
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-white">
                      {doc.wordCount.toLocaleString()} words
                    </div>
                    <div className="text-sm text-white/70">
                      {((doc.wordCount / totalSize) * 100).toFixed(1)}% of total
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-white/70">
              <DocumentTextIcon className="mx-auto mb-2 h-12 w-12 text-white/50" />
              <p>No documents uploaded yet</p>
            </div>
          )}
        </div>

        {/* Usage Bar */}
        {totalSize > 0 && (
          <div className="mt-4">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm text-white/70">Usage</span>
              <span className="text-sm text-white/70">
                {totalSize.toLocaleString()} / {modelLimit.toLocaleString()}{" "}
                words
              </span>
            </div>
            <div className="h-2 w-full rounded-full bg-gray-700">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  limitExceeded
                    ? "bg-red-500"
                    : totalSize > modelLimit * 0.8
                    ? "bg-yellow-500"
                    : "bg-green-500"
                }`}
                style={{
                  width: `${Math.min((totalSize / modelLimit) * 100, 100)}%`,
                }}
              ></div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const getCurrentDateForInput = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const day = now.getDate().toString().padStart(2, "0");
    const hours = now.getHours().toString().padStart(2, "0");
    const minutes = now.getMinutes().toString().padStart(2, "0");
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  const onSubmit = async (_data) => {
    let sdk = new MkdSDK();
    try {
      console.log(_data);
      await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/sms_outbound_campaign/create",
        {
          name: _data.name,
          contacts: parsedCSV.data.filter((c) =>
            requiredFields
              .map((field) => c[field])
              .some((v) => v !== "" && v !== undefined)
          ),
          assistant_id: _data.assistant_id,
          from_number_id: _data.from_number_id,
          // start_time: new Date().toISOString(),
          start_time: new Date(_data.start_time).toISOString(),
          valid_time_opening: gmtTime,
          valid_time_closing: gmtTimeClose,
          days: _data.days,
          knowledgeField: knowledgeField,
          language: _data.language,
        },
        "POST"
      );

      showToast(globalDispatch, "Added");
      if (closeSidebar) {
        closeSidebar();
      }
      reset();
    } catch (error) {
      console.log("Error", error);
      tokenExpireError(authDispatch, error.message);
      showToast(globalDispatch, error.message, 5000, "error");
      setError("name", {
        type: "manual",
        message: error.message,
      });
    }
  };

  React.useEffect(() => {
    if (localTimeOpening) {
      console.log("conversion started");
      const localMoment = moment(localTimeOpening, "HH:mm");
      const gmtMoment = localMoment.clone().tz("Etc/GMT");
      setGmtTime(gmtMoment.format("HH:mm"));
      console.log(gmtMoment, gmtTime);
    }
    if (localTimeClosing) {
      console.log("conversion started");
      const localMoment = moment(localTimeClosing, "HH:mm");
      const gmtMoment = localMoment.clone().tz("Etc/GMT");
      setGmtTimeClose(gmtMoment.format("HH:mm"));
      console.log(gmtMoment, gmtTimeClose);
    }
  }, [localTimeOpening, localTimeClosing]);

  const handleTimeOpeningChange = (e) => {
    setLocalTimeOpening(e.target.value);
  };
  const handleDateChange = (e) => {
    setDateChange(e.target.value);
  };
  const handleTimeClosingChange = (e) => {
    setLocalTimeClosing(e.target.value);
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "sms_outbound_campaigns",
      },
    });

    (async function getAssistants() {
      sdk.setTable("assistants");
      const result = await sdk.callRestAPI(
        { user_id: authState.user, filter: [`user_id,eq,${authState.user}`] },
        "GETALL"
      );
      if (!result.error) {
        setAssistants(result.list);
      }
    })();
    (async function getNumbers() {
      sdk.setTable("numbers");
      const result = await sdk.callRestAPI(
        {
          user_id: authState.user,
          filter: [
            // `user_id,eq,${authState.user}`,
            `status,eq,1`,
          ],
        },
        "GETALL"
      );
      if (!result.error) {
        setNumbers(result.list);
      }
    })();
    (async function getSettings() {
      sdk.setTable("user_settings");
      const result = await sdk.callRestAPI(
        {
          user_id: authState.user,
          filter: [`user_id,eq,${authState.user}`],
        },
        "GETALL"
      );
      if (!result.error) {
        setSettings(result.list);
        // const v = result.list.filter(s => s.setting_key === "llm")
        const v = result.list[0]?.llm_settings;
        let setting = "";
        try {
          setting = JSON.parse(v)?.provider;
        } catch (e) {
          setting = "anthropic";
        }
        console.log("setting", limits[setting].MAX_CONTENT_SIZE_LIMIT);
        setModelLimit(limits[setting].MAX_CONTENT_SIZE_LIMIT);
      }
    })();
  }, []);

  return (
    <div className="">
      {/* Add New SMS Outbound Campaign Modal */}
      <Transition appear={false} show={isOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-[100]"
          onClose={() => {
            if (!previewCSV && !previewDocuments) {
              closeSidebar();
            }
          }}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-x-full"
                enterTo="opacity-100 translate-x-0"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-x-0"
                leaveTo="opacity-0 translate-x-full"
              >
                <Dialog.Panel className=" h-[95vh] w-full max-w-3xl transform overflow-y-auto  bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                  <div className="p-5">
                    <h4 className="text-3xl font-medium text-white">
                      Add New SMS Outbound Campaign
                    </h4>
                    <form
                      className="mt-7 flex w-full flex-col gap-2"
                      onSubmit={handleSubmit(onSubmit)}
                    >
                      {/* Campaign Name */}
                      <div className="mb-4">
                        <label className="flex items-center text-base font-semibold text-white">
                          Campaign Name
                          <span className="group relative ml-1">
                            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white" />
                            <div className="absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block">
                              Enter the name of the SMS campaign.
                            </div>
                          </span>
                        </label>
                        <MkdInput
                          type="text"
                          page="add"
                          name="name"
                          errors={errors}
                          placeholder="Name of the SMS campaign"
                          register={register}
                          className="bg-[#1d2937] placeholder:text-gray-300"
                        />
                      </div>

                      {/* Assistant */}
                      <div className="mb-4">
                        <label className="flex items-center text-base font-semibold text-white">
                          Assistant
                          <span className="group relative ml-1">
                            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white" />
                            <div className="absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block">
                              Select the assistant responsible for the SMS
                              campaign.
                            </div>
                          </span>
                        </label>
                        <MkdInput
                          type="mapping"
                          page="add"
                          name="assistant_id"
                          errors={errors}
                          placeholder="Assistant"
                          options={assistants.map((as) => as.id)}
                          mapping={assistants.reduce((a, c) => {
                            a[c.id] = c.assistant_name;
                            return a;
                          }, {})}
                          register={register}
                          className="bg-[#1d2937] placeholder:text-gray-300"
                        />
                      </div>

                      {/* Choose a Number */}
                      <div className="mb-4">
                        <label className="flex items-center text-base font-semibold text-white">
                          Choose a number
                          <span className="group relative ml-1">
                            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white" />
                            <div className="absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block">
                              Select the phone number to be used for the SMS
                              campaign.
                            </div>
                          </span>
                        </label>
                        <MkdInput
                          type="mapping"
                          page="add"
                          name="from_number_id"
                          errors={errors}
                          placeholder="Choose a number"
                          options={numbers.map((as) => as.id)}
                          mapping={numbers.reduce((a, c) => {
                            a[c.id] = c.number;
                            return a;
                          }, {})}
                          register={register}
                          className="bg-[#1d2937] placeholder:text-gray-300"
                        />
                      </div>

                      <div className="mb-4">
                        <label className="flex items-center text-base font-semibold text-white">
                          Language
                          <span className="group relative ml-1">
                            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white" />
                            <div className="absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block">
                              Select the language for the SMS campaign
                            </div>
                          </span>
                        </label>
                        <MkdInput
                          type="mapping"
                          page="add"
                          name="language"
                          errors={errors}
                          placeholder="Select Language"
                          options={Object.keys(languages)}
                          mapping={languages}
                          register={register}
                          className="bg-[#1d2937] placeholder:text-gray-300"
                        />
                      </div>

                      {/* Start Date */}
                      <div className="mb-4">
                        <label className="flex items-center text-base font-semibold text-white">
                          Start Date
                          <span className="group relative ml-1">
                            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white" />
                            <div className="absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block">
                              Select the start date for the SMS campaign.
                            </div>
                          </span>
                        </label>
                        <MkdInput
                          type="date"
                          page="add"
                          name="start_time"
                          errors={errors}
                          placeholder="Start Date"
                          min={getCurrentDateForInput()}
                          onChange={handleDateChange}
                          dateTime={true}
                          register={register}
                          className="bg-[#1d2937] placeholder:text-gray-300"
                        />
                      </div>

                      {/* SMS Window Start Time */}
                      <div className="mb-4">
                        <label className="flex items-center text-base font-semibold text-white">
                          SMS window start time
                          <span className="group relative ml-1">
                            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white" />
                            <div className="absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block">
                              Select the start time for the SMS window.
                            </div>
                          </span>
                        </label>
                        <MkdInput
                          type="time"
                          page="add"
                          name="valid_time_opening"
                          errors={errors}
                          label=""
                          onChange={handleTimeOpeningChange}
                          time={true}
                          placeholder="SMS window start time"
                          register={register}
                          className="bg-[#1d2937] placeholder:text-gray-300"
                        />
                        {localTimeOpening && (
                          <p className="mt-1 text-sm text-gray-600">
                            Local Time: {localTimeOpening} (GMT: {gmtTime}; The
                            SMS is sent according to GMT Time.)
                          </p>
                        )}
                      </div>

                      {/* SMS Window End Time */}
                      <div className="mb-4">
                        <label className="flex items-center text-base font-semibold text-white">
                          SMS window end time
                          <span className="group relative ml-1">
                            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white" />
                            <div className="absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block">
                              Select the end time for the SMS window.
                            </div>
                          </span>
                        </label>
                        <MkdInput
                          type="time"
                          page="add"
                          name="valid_time_closing"
                          errors={errors}
                          label=""
                          onChange={handleTimeClosingChange}
                          time={true}
                          placeholder="SMS window end time"
                          register={register}
                          className="bg-[#1d2937] placeholder:text-gray-300"
                        />
                        {localTimeClosing && (
                          <p className="mt-1 text-sm text-gray-600">
                            Local Time: {localTimeClosing} (GMT: {gmtTimeClose};
                            The SMS is sent according to GMT Time.)
                          </p>
                        )}
                      </div>

                      <div className="mb-4">
                        <label className="flex items-center text-base font-semibold text-white">
                          Days AI can send SMS
                          <span className="group relative ml-1">
                            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white" />
                            <div className="absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block">
                              Choose the specific days when the AI is allowed to
                              send SMS messages.
                            </div>
                          </span>
                        </label>
                        <MkdInput
                          type="mapping"
                          page="add"
                          name="days"
                          errors={errors}
                          label=""
                          placeholder="Days AI can send SMS"
                          options={Object.keys(days_mapping)}
                          mapping={days_mapping}
                          register={register}
                          className="bg-[#1d2937] placeholder:text-gray-300"
                        />
                      </div>

                      {/* CSV Template Link */}
                      <p className="mt-2 text-sm text-white">
                        Need a template?{" "}
                        <a
                          href="/contact_template.csv" // Replace with your actual CSV template path
                          download
                          className="text-blue-600 underline"
                        >
                          Download sample CSV format
                        </a>
                      </p>

                      {/* Upload CSV */}
                      <div className="mt-5">
                        <label className="mb-2 block cursor-pointer text-sm font-bold text-white">
                          Upload CSV{" "}
                          <span className="text-sm font-normal text-white">
                            (Please ensure to include the 'phone' and 'name'
                            columns, as they are required.)
                          </span>
                        </label>
                        <CSVReader
                          onUploadAccepted={(results) => {
                            console.log("---------------------------");
                            console.log(results);
                            console.log("---------------------------");
                            setCsvResults(results);
                            setPreviewCSV(true);
                          }}
                        >
                          {({
                            getRootProps,
                            acceptedFile,
                            ProgressBar,
                            getRemoveFileProps,
                          }) => (
                            <>
                              <div>
                                <button
                                  type="button"
                                  {...getRootProps()}
                                  className={`flex h-[10.375rem] w-full cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-600 bg-transparent text-white `}
                                >
                                  {acceptedFile ? (
                                    <div className="flex flex-col items-center font-bold">
                                      <DocumentTextIcon className="h-5 w-5" />
                                      {acceptedFile.name}
                                    </div>
                                  ) : (
                                    <div className="bg-transparent font-bold">
                                      Select CSV File
                                    </div>
                                  )}
                                </button>
                                {acceptedFile ? (
                                  <div className="flex items-center justify-between">
                                    <button
                                      className="focus:shadow-outline mt-6 rounded bg-transparent px-4 py-2 font-bold text-white focus:outline-none"
                                      type="button"
                                      onClick={() => setPreviewCSV(true)}
                                    >
                                      Preview CSV
                                    </button>
                                    <button
                                      {...getRemoveFileProps()}
                                      onClick={(e) => {
                                        setCsvResults(null);
                                        getRemoveFileProps().onClick(e);
                                      }}
                                      type="button"
                                      className="mt-3 flex items-center gap-3"
                                    >
                                      Remove <TrashIcon className="h-6 w-6" />
                                    </button>
                                  </div>
                                ) : null}
                              </div>
                              <ProgressBar />
                            </>
                          )}
                        </CSVReader>
                      </div>

                      {/* Document Uploader */}
                      <div className="mt-4">
                        <label className="flex items-center text-base font-semibold text-white">
                          Upload Documents <br />
                          (Documents forming the companies knowledge base)
                          <span className="group relative ml-1">
                            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white" />
                            <div className="absolute bottom-full mb-2 hidden w-64 rounded-md bg-transparent p-2 text-sm text-white group-hover:block">
                              Upload Documents to build the Assistants knowledge
                              base for the SMS conversation.
                            </div>
                          </span>
                        </label>
                        <DocumentUploader allow_preview={preview} />
                      </div>

                      {/* Submit Button */}
                      <InteractiveButton
                        type="submit"
                        loading={isSubmitting}
                        disabled={isSubmitting}
                        className="focus:shadow-outline  mt-6 rounded bg-[#19b2f6]/80  px-4 py-2 font-bold text-white hover:bg-[#19b2f6]/60 focus:outline-none"
                      >
                        Submit
                      </InteractiveButton>
                    </form>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Preview CSV Modal */}
      <Transition
        appear={false}
        show={previewCSV && csvResults !== null}
        as={Fragment}
      >
        <Dialog
          as="div"
          className="relative z-[100]"
          onClose={() => setPreviewCSV(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="mx-auto w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                  <div className="flex items-center justify-between">
                    <Dialog.Title className="text-3xl font-medium">
                      Preview CSV
                    </Dialog.Title>
                    <button type="button" onClick={() => setPreviewCSV(false)}>
                      <XMarkIcon className="h-6 w-6" />
                    </button>
                  </div>

                  <div className="mt-5 space-y-3">
                    <div className="flex items-center gap-4">
                      <div className="h-6 w-6 bg-red-300"></div>
                      Missing one or more required columns
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="h-6 w-6 bg-yellow-300"></div>
                      Missing one or more optional columns
                    </div>
                  </div>
                  <div className="small-scroll mt-5 max-h-[60vh] overflow-x-auto overflow-y-auto">
                    <table className="w-full border">
                      <thead className="border-b bg-gray-50">
                        <tr>
                          {parsedCSV.headers.map((header) => {
                            const required = requiredFields.includes(header);
                            return (
                              <th
                                className="whitespace-nowrap px-4 py-2"
                                key={header}
                              >
                                {required ? "*" : ""} {header}
                              </th>
                            );
                          })}
                        </tr>
                      </thead>
                      <tbody>
                        {parsedCSV.data.map((row, key) => {
                          const warning = parsedCSV.headers
                            .map((field) => row[field])
                            .some((v) => v === "" || v === undefined);
                          const error = requiredFields
                            .map((field) => row[field])
                            .some((v) => v === "" || v === undefined);
                          return (
                            <tr
                              className={`border-b ${
                                error ? "!bg-red-300" : ""
                              } ${warning ? "bg-yellow-300" : ""}`}
                              key={key}
                            >
                              {parsedCSV.headers.map((header) => (
                                <td
                                  className="whitespace-nowrap px-4 py-2"
                                  key={header}
                                >
                                  {row[header]}
                                </td>
                              ))}
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Document Preview Modal */}
      <Transition appear={false} show={previewDocuments} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-[100]"
          onClose={() => setPreviewDocuments(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="mx-auto w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                  <div className="flex items-center justify-between">
                    <Dialog.Title className="text-3xl font-medium">
                      Preview Document
                    </Dialog.Title>
                    <button
                      type="button"
                      onClick={() => setPreviewDocuments(false)}
                    >
                      <XMarkIcon className="h-6 w-6" />
                    </button>
                  </div>
                  <DocumentPreview />
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default UserAddSMSOutboundCampaignPage;
