import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "Utils/utils";
import { MkdInput } from "Components/MkdInput";
import { InteractiveButton } from "Components/InteractiveButton";
import { Spinner } from "Assets/svgs";
import { PiXBold } from "react-icons/pi";

let sdk = new MkdSDK();

const UserEditVoiceTablePage = ({ activeId: id, closeSidebar }) => {
  const { dispatch } = React.useContext(AuthContext);
  const schema = yup
    .object({
      name: yup.string().required("Name is required"),
      description: yup.string().required("Description is required"),
    })
    .required();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [fileObj, setFileObj] = React.useState({});
  const [isLoading, setIsLoading] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const navigate = useNavigate();

  // const [id, setId] = useState(0);
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();

  useEffect(function () {
    (async function () {
      try {
        setLoading(true);

        sdk.setTable("voice_list");
        const result = await sdk.callRestAPI(
          { id: id ? id : Number(params?.id) },
          "GET"
        );
        console.log(result, "<---");
        if (!result.error) {
          setValue("name", result.model.name);
          setValue("description", result.model.description);
        }
        setLoading(false);
      } catch (error) {
        setLoading(false);

        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    setIsLoading(true);
    try {
      sdk.setTable("voice_list");
      // for (let item in fileObj) {
      //     let formData = new FormData();
      //     formData.append('file', fileObj[item].file);
      //     let uploadResult = await sdk.uploadImage(formData);
      //     _data[item] = uploadResult.url;

      // }
      const result = await sdk.callRestAPI(
        {
          id: id,
          name: _data.name,
          description: _data.description,
        },
        "PUT"
      );

      if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/user/voice_list");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
      if (closeSidebar) {
        closeSidebar();
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
    }
  };
  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "voice_list",
      },
    });
  }, []);

  return (
    <div className="flex flex-col h-fit">
      <div className="flex flex-row justify-between">
        <h4 className="text-3xl font-semibold text-white">Edit Voice Name</h4>
        <button onClick={closeSidebar}>
          <PiXBold className="text-2xl text-white" />
        </button>
      </div>
      {loading ? (
        <div
          className={`flex justify-center items-center py-5 w-full min-w-full max-w-full max-h-fit min-h-fit`}
        >
          <Spinner size={100} color="#0EA5E9" />
        </div>
      ) : (
        <form
          className="flex flex-col flex-grow mt-7 w-full"
          onSubmit={handleSubmit(onSubmit)}
        >
          <MkdInput
            type={"text"}
            page={"edit"}
            name={"name"}
            errors={errors}
            label={"Name"}
            placeholder={"name"}
            register={register}
            className={"text-white bg-transparent placeholder:text-gray-300"}
          />
          <MkdInput
            type={"textarea"}
            page={"edit"}
            name={"description"}
            errors={errors}
            label={"Description"}
            placeholder={"description"}
            register={register}
            className={"text-white bg-transparent placeholder:text-gray-300"}
            containerClassName="flex flex-col flex-grow"
            rows={6}
          />

          <InteractiveButton
            type="submit"
            className="focus:shadow-outline w-fit rounded bg-[#19b2f6]/80 px-4 py-2 font-semibold text-white focus:outline-none"
            loading={isLoading}
            disable={isLoading}
          >
            Submit
          </InteractiveButton>
        </form>
      )}
    </div>
  );
};

export default UserEditVoiceTablePage;
