import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "Utils/utils";
import { MkdInput } from "Components/MkdInput";
import { InteractiveButton } from "Components/InteractiveButton";
import { SkeletonLoader } from "Components/Skeleton";
import { PiXBold } from "react-icons/pi";
import { Spinner } from "Assets/svgs";

let sdk = new MkdSDK();

const UserEditAssistantTablePage = ({ activeId: id, closeSidebar }) => {
  const { dispatch } = React.useContext(AuthContext);
  const schema = yup
    .object({
      script: yup.string().required("Script is required"),
      voicemail_script: yup.string().required("Voicemail script is required"),
      assistant_name: yup.string().required("Assistant name is required"),
      first_message: yup.string().required("First message is required"),

      notes: yup.string().required("Notes are required"),
    })
    .required();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [fileObj, setFileObj] = React.useState({});
  const [isLoading, setIsLoading] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();

  useEffect(function () {
    (async function () {
      setLoading(true);
      try {
        sdk.setTable("assistants".trim(""));
        const result = await sdk.callRestAPI({ id: Number(id) }, "GET");
        if (!result.error) {
          setValue("assistant_name", result.model.assistant_name);
          setValue("script", result.model.script);
          setValue("voicemail_script", result.model.voicemail_script);
          setValue("first_message", result.model.first_message);
          setValue("notes", result.model.notes);
        }
      } catch (error) {
        console.log("error", error);
        tokenExpireError(dispatch, error.message);
      }
      setLoading(false);
    })();
  }, []);

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    console.log("data", _data);
    setIsLoading(true);
    try {
      sdk.setTable("assistants");
      const result = await sdk.callRestAPI(
        {
          id: id,
          assistant_name: _data.assistant_name,
          script: _data.script,
          voicemail_script: _data.voicemail_script,
          first_message: _data.first_message,
          notes: _data.notes,
        },
        "PUT"
      );

      if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/user/assistants");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
      setIsLoading(false);
      if (closeSidebar) {
        closeSidebar();
      }
    } catch (error) {
      setIsLoading(false);
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
    }
  };
  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "assistants",
      },
    });
  }, []);

  return (
    <div className="flex flex-col pb-5 h-screen">
      <div className="flex flex-row justify-between">
        <h4 className="mt-4 text-3xl font-semibold text-white">
          Edit Assistant
        </h4>
        <button onClick={closeSidebar}>
          <PiXBold className="text-2xl text-white" />
        </button>
      </div>
      {loading ? (
        <div
          className={`flex justify-center items-center py-5 w-full min-w-full max-w-full max-h-fit min-h-[70vh]`}
        >
          <Spinner size={100} color="#0EA5E9" />
        </div>
      ) : (
        <form
          className="flex flex-col flex-grow pb-10 mt-7 w-full"
          onSubmit={handleSubmit(onSubmit)}
        >
          <MkdInput
            type={"text"}
            page={"edit"}
            name={"assistant_name"}
            errors={errors}
            label={"Assistant Name"}
            placeholder={"name"}
            register={register}
            className={"text-white bg-transparent placeholder:text-gray-300"}
          />
          <MkdInput
            type={"text"}
            page={"edit"}
            name={"first_message"}
            errors={errors}
            label={"First Message"}
            placeholder={"First Message"}
            register={register}
            className={"text-white bg-transparent placeholder:text-gray-300"}
          />
          <MkdInput
            type={"textarea"}
            page={"edit"}
            name={"script"}
            errors={errors}
            label={"script"}
            placeholder={"script"}
            register={register}
            className={
              "flex-grow text-white bg-transparent placeholder:text-gray-300"
            }
            containerClassName="flex flex-col flex-grow"
            rows={10}
          />
          <MkdInput
            type={"textarea"}
            page={"edit"}
            name={"voicemail_script"}
            errors={errors}
            label={"Voicemail script"}
            placeholder={"voicemail script"}
            register={register}
            className={
              "flex-grow text-white bg-transparent placeholder:text-gray-300"
            }
            containerClassName="flex flex-col flex-grow"
            maxLength={500}
            rows={10}
          />
          <MkdInput
            type={"textarea"}
            page={"edit"}
            name={"notes"}
            errors={errors}
            label={"Notes"}
            placeholder={"Notes"}
            register={register}
            className={"text-white bg-transparent placeholder:text-gray-300"}
            rows={5}
          />

          <InteractiveButton
            type="submit"
            disabled={isLoading}
            className="focus:shadow-outline w-full  rounded bg-[#19b2f6]/80   px-4 py-2 font-semibold text-white focus:outline-none"
            loading={isLoading}
          >
            Submit
          </InteractiveButton>
        </form>
      )}
    </div>
  );
};

export default UserEditAssistantTablePage;
