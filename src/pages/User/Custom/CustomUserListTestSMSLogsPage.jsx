import React, { Fragment } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Dialog, Popover, Transition } from "@headlessui/react";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { MkdDebounceInput } from "Components/MkdDebounceInput";
import { RiDeleteBin5Line } from "react-icons/ri";
import TreeSDK from "Utils/TreeSDK";
import { MkdListTableHead } from "Components/MkdListTable";
import { useForm } from "react-hook-form";
import PaginationBarV2 from "Components/PaginationBar/PaginationBarV2";
import TableSkeleton from "Components/MkdListTable/TableSkeleton";
import { useCSVDownloader } from "react-papaparse";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { XIcon } from "lucide-react";

const STATUS_MAPPING = {
  0: "Inactive",
  1: "Active",
  2: "Paused",
};

const REVERSE_STATUS_MAPPING = {
  inactive: 0,
  active: 1,
  paused: 2,
  INACTIVE: 0,
  ACTIVE: 1,
  PAUSED: 2,
  Inactive: 0,
  Active: 1,
  Paused: 2,
};

const columns = [
  {
    header: "Action",
    accessor: "",
  },
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Chat History",
    accessor: "chat_history",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Phone Number",
    accessor: "to_number",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Ended",
    accessor: "ended",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 0: "True", 1: "False" },
  },
  {
    header: "Credit Used",
    accessor: "cost",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "AI Response",
    accessor: "ai_response",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
];

const filterableColumns = columns.filter((col) => {
  const excludedColumns = [
    "", // Action column
  ];
  return !excludedColumns.includes(col.accessor);
});

const CustomUserListTestSMSLogsPage = () => {
  const { state: authState, dispatch: authDispatch } =
    React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [visibleMessages, setVisibleMessages] = React.useState(4);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [searchValue, setSearchValue] = React.useState("");
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [currentTableData, setCurrentTableData] = React.useState([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const { CSVDownloader } = useCSVDownloader();
  const [isViewModalOpen, setIsViewModalOpen] = React.useState(false);
  const [selectedRow, setSelectedRow] = React.useState(null);

  const [paginationData, setPaginationData] = React.useState({
    currentPage: 0,
    pageSize: 0,
    totalNumber: 0,
    totalPages: 0,
  });

  const { handleSubmit } = useForm({ defaultValues: {} });

  const [filterValues, setFilterValues] = React.useState({});
  const [optionValue, setOptionValue] = React.useState("eq");

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "test_sms_logs",
      },
    });

    getData();
  }, [filterConditions, searchValue]);

  const addFilterCondition = (option, selectedValue, inputValue) => {
    if (!inputValue) {
      setFilterConditions((prevConditions) => {
        return prevConditions.filter((cond) => !cond.startsWith(option + ","));
      });
      return;
    }

    let value = inputValue;
    let operator = selectedValue || "eq";

    if (option === "ended") {
      value = value.toLowerCase() === "true" ? "0" : "1";
    }

    if (option === "chat_history") {
      operator = "cs";
    }

    const condition = `${option},${operator},${value}`;

    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (cond) => !cond.startsWith(option + ",")
      );
      return [...newConditions, condition];
    });
  };

  const ChatHistory = ({ chatHistory }) => {
    const [visibleMessages, setVisibleMessages] = React.useState(4);

    const handleViewMore = () => {
      setVisibleMessages((prev) => prev + 4);
    };

    return (
      <>
        {chatHistory.slice(0, visibleMessages).map((chat, index) => (
          <div
            key={index}
            className={`overflow-x-auto rounded-md p-3 ${
              chat.role === "user"
                ? "bg-blue-500/20 text-white"
                : "bg-transparent text-white"
            }`}
          >
            <p className="font-medium capitalize">{chat.role}:</p>
            <p className="mt-1">{chat.content}</p>
          </div>
        ))}
        {visibleMessages < chatHistory.length && (
          <button
            onClick={handleViewMore}
            className="mt-4 text-[#19b2f6] hover:underline"
          >
            View More
          </button>
        )}
      </>
    );
  };

  function formatPhoneNumber(phoneNumber) {
    if (!phoneNumber) return "N/A";

    // Remove any non-digit characters except the plus sign at the start
    const cleaned = phoneNumber.toString().replace(/(?!^\+)\D/g, "");

    // Check if the number starts with +1
    const hasCountryCode = cleaned.startsWith("+1");
    const digitsOnly = hasCountryCode ? cleaned.slice(2) : cleaned;

    // Format for 10-digit North American numbers
    const match = digitsOnly.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
      return `+1 (${match[1]}) ${match[2]}-${match[3]}`;
    } else if (digitsOnly.length === 10) {
      return phoneNumber; // Return original if format doesn't match
    } else {
      return phoneNumber;
    }
  }
  async function getData() {
    try {
      setLoading(true);
      const tdk = new TreeSDK();
      const tablePrefix = `${tdk.getProjectId()}_sms_logs`;

      let filterArray = [
        "status,eq,3", // For test SMS
        searchParams.get("campaign_id")
          ? `${tablePrefix}.campaign_id,eq,${searchParams.get("campaign_id")}`
          : undefined,
      ].filter(Boolean);

      if (searchValue) {
        filterArray.push(`${tablePrefix}.chat_history,cs,${searchValue}`);
      }

      if (filterConditions.length > 0) {
        const updatedFilters = filterConditions.map((condition) => {
          const [field, operator, value] = condition.split(",");
          return `${tablePrefix}.${field},${operator},${value}`;
        });
        filterArray = [...filterArray, ...updatedFilters];
      }

      const result = await tdk.getPaginate("sms_logs", {
        size: searchParams.get("limit") ?? 50,
        page: searchParams.get("page") ?? 1,
        filter: filterArray.filter(Boolean),
      });

      const { list, total, limit, num_pages, page } = result;
      setCurrentTableData(list);
      setPaginationData({
        currentPage: page,
        pageSize: limit,
        totalNumber: total,
        totalPages: num_pages,
      });
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(authDispatch, error.message);
      showToast(globalDispatch, error.message, 5000, "error");
    }
    setLoading(false);
  }

  const handleClearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    setFilterValues({});
    for (const key of searchParams.keys()) {
      if (key !== "campaign_id") {
        searchParams.delete(key);
      }
    }
    setSearchParams(searchParams);
    getData();
  };

  const EndedFilterInput = ({ option, onReady, value, onChange }) => {
    return (
      <select
        value={value}
        onChange={(e) => {
          onChange(option, e.target.value);
          onReady(e.target.value);
        }}
        className="h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
      >
        <option value="">Select Status</option>
        <option value="true">True</option>
        <option value="false">False</option>
      </select>
    );
  };

  const ViewModal = ({ isOpen, onClose, row }) => {
    if (!row) return null;
    return (
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-[100]" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-3xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                  <div className="mb-4 flex items-center justify-between">
                    <Dialog.Title className="text-xl font-medium text-white">
                      SMS Details
                    </Dialog.Title>
                    <button onClick={onClose}>
                      <XMarkIcon className="h-6 w-6 text-white/70 hover:text-white" />
                    </button>
                  </div>

                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-white/70">ID</p>
                        <p className="text-white">{row.id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Phone Number</p>
                        <p className="text-white">
                          {console.log(row.to_number)}
                          {row?.to_number
                            ? formatPhoneNumber(row.to_number)
                            : "N/A"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Ended</p>
                        <p className="text-white">
                          {row.ended === 1
                            ? "False"
                            : row.ended === 0
                            ? "True"
                            : "N/A"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Cost</p>
                        <p className="text-white">
                          {row.cost ? `$${row.cost?.toFixed(2)}` : "N/A"}
                        </p>
                      </div>
                    </div>

                    <div>
                      <p className="mb-2 text-sm text-white/70">Chat History</p>
                      {row.chat_history ? (
                        <ChatHistory
                          chatHistory={JSON.parse(row.chat_history || "[]")}
                        />
                      ) : (
                        <p className="text-white">N/A</p>
                      )}
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    );
  };

  return (
    <>
      <div className="overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5">
        <h3 className="my-4 px-8 text-2xl font-bold text-white">
          Test SMS Logs
        </h3>
        <div className="bg-[#1d2937] px-8 py-4">
          <div className="flex h-fit items-center justify-between gap-3">
            <div className="flex w-[200px] min-w-[200px] items-center justify-between">
              <div className="relative z-10 rounded bg-[#1d2937]">
                <Popover>
                  <div className="flex items-center gap-4 bg-[#1d2937] text-white">
                    <Popover.Button className="border-white/50ss flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white focus-visible:outline-0 focus-visible:outline-transparent ">
                      <BiFilterAlt />
                      <span>Filters</span>
                      {selectedOptions.length > 0 && (
                        <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white">
                          {selectedOptions.length}
                        </span>
                      )}
                    </Popover.Button>
                    <div className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white focus-within:border-gray-400">
                      <BiSearch className="text-xl text-white" />
                      <input
                        type="text"
                        placeholder="Search in chat history"
                        className="border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none"
                        style={{ boxShadow: "0 0 transparent" }}
                        value={searchValue}
                        onChange={(e) => {
                          setSearchValue(e.target.value);
                          addFilterCondition(
                            "chat_history",
                            "cs",
                            e.target.value
                          );
                        }}
                      />
                      {searchValue && (
                        <AiOutlineClose
                          className="cursor-pointer text-lg text-white"
                          onClick={() => {
                            setSearchValue("");
                            addFilterCondition("chat_history", "cs", "");
                          }}
                        />
                      )}
                    </div>
                  </div>

                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <Popover.Panel>
                      <div className="filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10">
                        <span className="absolute left-5 top-2 font-medium text-white">
                          Filters
                        </span>
                        <Popover.Button
                          onClick={() => {
                            console.log("clicked");
                            setSelectedOptions([]);
                            setFilterConditions([]);
                            setFilterValues({});
                          }}
                        >
                          <XIcon className="absolute right-2 top-2 cursor-pointer text-white" />
                        </Popover.Button>
                        {selectedOptions?.map((option, index) => (
                          <div
                            key={index}
                            className="mb-2 flex w-full items-center justify-between gap-3 text-gray-600"
                          >
                            <button
                              type="button"
                              className="block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none"
                              title={option}
                              style={{ WebkitTouchCallout: "none" }}
                            >
                              {filterableColumns.find(
                                (col) => col.accessor === option
                              )?.header || option}
                            </button>

                            <select
                              className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none"
                              value={optionValue}
                              onChange={(e) => {
                                setOptionValue(e.target.value);
                                addFilterCondition(
                                  option,
                                  e.target.value,
                                  filterValues[option]
                                );
                              }}
                            >
                              <option value="eq">equals</option>
                              <option value="cs">contains</option>
                              <option value="sw">start with</option>
                              <option value="ew">ends with</option>
                              <option value="lt">lower than</option>
                              <option value="le">lower or equal</option>
                              <option value="ge">greater or equal</option>
                              <option value="gt">greater than</option>
                              <option value="bt">between</option>
                              <option value="in">in</option>
                              <option value="is">is null</option>
                            </select>

                            {option === "status" ? (
                              <select
                                value={filterValues[option] || ""}
                                onChange={(e) => {
                                  setFilterValues((prev) => ({
                                    ...prev,
                                    [option]: e.target.value,
                                  }));
                                  addFilterCondition(
                                    option,
                                    optionValue,
                                    e.target.value
                                  );
                                }}
                                className="h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                              >
                                <option value="">Select Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                              </select>
                            ) : (
                              <MkdDebounceInput
                                type="text"
                                labelClassName="!mb-0"
                                placeholder="Enter value"
                                setValue={() => {}}
                                showIcon={false}
                                className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                                onReady={(value) =>
                                  addFilterCondition(option, optionValue, value)
                                }
                              />
                            )}

                            <RiDeleteBin5Line
                              className="cursor-pointer text-2xl text-red-600"
                              onClick={() => {
                                setSelectedOptions((prevOptions) =>
                                  prevOptions.filter((op) => op !== option)
                                );
                                setFilterConditions((prevConditions) =>
                                  prevConditions.filter(
                                    (condition) => !condition.includes(option)
                                  )
                                );
                                setFilterValues((prev) => {
                                  const newValues = { ...prev };
                                  delete newValues[option];
                                  return newValues;
                                });
                              }}
                            />
                          </div>
                        ))}

                        <div className="search-buttons relative flex items-center justify-between font-semibold">
                          <div
                            className="mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out"
                            onClick={() =>
                              setShowFilterOptions(!showFilterOptions)
                            }
                          >
                            <AiOutlinePlus />
                            Add filter
                          </div>

                          {showFilterOptions && (
                            <div className="absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3">
                              <ul className="flex flex-col gap-2 text-gray-500">
                                {filterableColumns.map((column) => (
                                  <li
                                    key={column.accessor}
                                    className={`${
                                      selectedOptions.includes(column.accessor)
                                        ? "cursor-not-allowed text-gray-100"
                                        : "cursor-pointer text-gray-400 hover:text-white"
                                    }`}
                                    onClick={() => {
                                      if (
                                        !selectedOptions.includes(
                                          column.accessor
                                        )
                                      ) {
                                        setSelectedOptions((prev) => [
                                          ...prev,
                                          column.accessor,
                                        ]);
                                        setFilterValues((prev) => ({
                                          ...prev,
                                          [column.accessor]: "",
                                        }));
                                      }
                                      setShowFilterOptions(false);
                                    }}
                                  >
                                    {column.header}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {selectedOptions.length > 0 && (
                            <div
                              onClick={handleClearAllFilters}
                              className="inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white"
                            >
                              Clear all filter
                            </div>
                          )}
                        </div>
                      </div>
                    </Popover.Panel>
                  </Transition>
                </Popover>
              </div>
            </div>

            <CSVDownloader
              filename={"test_sms_logs"}
              className="relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]"
              data={() => currentTableData}
            >
              Download CSV
            </CSVDownloader>
          </div>

          <div className="mt-4 overflow-x-auto  bg-[#1d2937]">
            <div
              className={
                loading ? "" : "overflow-x-auto border-b border-gray-400 shadow"
              }
            >
              {loading && currentTableData.length === 0 ? (
                <TableSkeleton columns={columns} />
              ) : (
                <table className="min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]">
                  <thead className="bg-[#1d2937]">
                    <MkdListTableHead
                      actionPosition={"onTable"}
                      onSort={() => {}}
                      columns={columns}
                      actions={{ view: { show: true } }}
                    />
                  </thead>
                  <tbody className="divide-y divide-gray-400 bg-[#1d2937]">
                    {currentTableData.map((row) => (
                      <tr key={row.id}>
                        <td className="whitespace-nowrap px-6 py-4">
                          <div className="flex items-center justify-between gap-3 text-sm">
                            <button
                              className="rounded-[30px] bg-[#19b2f6]/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-[#19b2f6]/30"
                              onClick={() => {
                                setSelectedRow(row);
                                setIsViewModalOpen(true);
                              }}
                            >
                              View
                            </button>
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {row.id}
                        </td>
                        <td className="whitespace-wrap overflow-x-auto px-6 py-4">
                          {row.chat_history ? (
                            JSON.parse(row.chat_history || "[]")
                              .find((message) => message.role === "assistant")
                              ?.content.slice(0, 120) + "..."
                          ) : (
                            <span className="text-white">N/A</span>
                          )}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {row.to_number
                            ? formatPhoneNumber(row.to_number)
                            : "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {row.ended === 1
                            ? "False"
                            : row.ended === 0
                            ? "True"
                            : "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {row.cost ? `$${row.cost.toFixed(2)}` : "N/A"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>

          <PaginationBarV2 paginationData={paginationData} />
        </div>
      </div>

      <ViewModal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        row={selectedRow}
      />
    </>
  );
};

export default CustomUserListTestSMSLogsPage;
