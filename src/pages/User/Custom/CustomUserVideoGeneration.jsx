import React, { useState, useContext } from "react";
//  import { LazyLoad } from "Components/LazyLoad";
import { tokenExpireError, AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";

import { Chat } from "Components/Chat";
import { ChatBot } from "Components/ChatBot";
import MoonLoader from "react-spinners/MoonLoader";

const CustomUserVideoGeneration = () => {
  const { state, dispatch } = useContext(AuthContext);
  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "video",
      },
    });
  }, []);

  const videoUrl = "https://www.w3schools.com/html/mov_bbb.mp4";

  return (
    <div className="mx-auto rounded p-5">
      <video className="w-[90%] md:w-[700px]" controls>
        <source src={videoUrl} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      <textarea
        placeholder="type here"
        className="mt-10 w-[90%] border-black md:w-[700px]"
        rows={6}
      ></textarea>
      <button
        // onClick={submitData}
        className="mt-4 flex items-center justify-center gap-2 rounded-[3px] bg-[black]  px-10 py-3 text-white"
      >
        {loading ? (
          <MoonLoader color={"white"} loading={true} size={20} />
        ) : null}
        Generate Video
      </button>
    </div>
  );
};

export default CustomUserVideoGeneration;
