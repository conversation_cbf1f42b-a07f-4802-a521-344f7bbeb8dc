import React, { useState, useContext } from 'react';
import { tokenExpireError, AuthContext } from 'Context/Auth';
import { GlobalContext, showToast } from 'Context/Global';

const EmbedPage = () => {
  const { state, dispatch } = useContext(AuthContext);
  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);

  React.useEffect(() => {
    globalDispatch({
      type: 'SETPATH',
      payload: {
        path: 'embed',
      },
    });
  }, []);

  return (
    <div className=" mx-auto rounded  p-5 shadow-md">
      <div className={`min-h-screen `}>
        <div className="flex min-h-[300px] max-w-[600px] cursor-pointer items-center justify-center border-2 border-black">
          <div className="hidden">copied Succesfully!</div>
        </div>
      </div>
    </div>
  );
};

export default EmbedPage;
