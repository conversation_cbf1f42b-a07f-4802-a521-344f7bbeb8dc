import React, { useState, useContext, Fragment, useId } from "react";
import { tokenExpireError, AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { MkdFileUpload } from "Components/MkdFileUpload";
import MkdSDK from "Utils/MkdSDK";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { useNavigate } from "react-router";
import { useSearchParams } from "react-router-dom";
import MoonLoader from "react-spinners/MoonLoader";

const OnboardingPage = () => {
  const { dispatch: authDispatch } = useContext(AuthContext);
  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);
  const [fileObj, setFileObj] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const [name, setName] = React.useState(null);
  const [description, setDescription] = React.useState(null);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const sdk = new MkdSDK();
  const fileItemId = useId();

  const checkStatus = async (id) => {
    try {
      const res = sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/check_onboarding",
        { user_id: localStorage.getItem("user"), task_id: id },
        "POST"
      );
      return res;
    } catch (err) {}
  };

  const submitData = async () => {
    setLoading(true);
    // const profile = await sdk.callRawAPI(
    //   `/v4/api/records/profile/${user_id}`,
    //   '',
    //   'GET',
    // );
    // let task_id = profile?.model?.task_id;
    // if (task_id) {
    //   let i = await checkStatus(task_id);
    //   showToast(globalDispatch, i?.message?.reason, 5000, 'error');
    //   if (i.message.status == 'failed') {
    //     showToast(
    //       globalDispatch,
    //       'reuploading onboarding materials',
    //       5000,
    //       'success',
    //     );
    //   } else {
    //     setLoading(false);
    //     showToast(globalDispatch, 'Onboarding completed', 5000, 'error');
    //     return;
    //   }
    // }

    if (!name || name.trim().length <= 0) {
      showToast(globalDispatch, "Please enter a name!", 2000, "error");
      setLoading(false);
      return;
    }
    if (!fileObj?.length) {
      showToast(
        globalDispatch,
        "No audio/video files uploaded!",
        2000,
        "error"
      );
      setLoading(false);
      return;
    }
    const newFiles = fileObj?.map((item) => item.file);

    try {
      await handleOnboarding(newFiles);

      setLoading(false);
      navigate("/user/voice_list");
    } catch (err) {
      setLoading(false);
      showToast(
        globalDispatch,
        err.message || "An error occurred",
        2000,
        "error"
      );
    }
  };

  const handleOnboarding = async (files) => {
    try {
      const formData = new FormData();
      if (!name) {
        showToast(globalDispatch, "Please enter a name", 2000, "error");
      }
      files.forEach((file) => {
        formData.append("file", file);
      });
      formData.append("name", name);
      formData.append("description", description);

      await sdk.sendOnboardingData(formData, "POST");
      showToast(globalDispatch, "Onboarding complete");
      if (searchParams.get("redirect_uri")) {
        navigate(searchParams.get("redirect_uri"));
      }
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      showToast(globalDispatch, err.message, 2000, "error");
    }
  };

  const handleChange = async (e, type = 1) => {
    type === 1 ? setName(e.target.value) : setDescription(e.target.value);
  };

  const getAudioTranscript = async (formData) => {
    try {
      const res = await sdk.callRawAPI(
        `/v3/api/custom/voiceoutreach/transcribe`,
        { url: formData },
        "POST"
      );
      return res;
    } catch (err) {
      return err;
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "onboarding",
      },
    });
  }, []);

  return (
    <div className="mx-auto flex  items-center justify-center  rounded-md  bg-[#1d2937]    px-5 text-white  md:max-w-2xl md:px-0 lg:max-w-2xl 2xl:max-w-2xl">
      <div className="mx-auto rounded">
        <Transition.Root show={open} as={Fragment}>
          <Dialog
            as="div"
            className="fixed inset-0 z-50 overflow-hidden"
            onClose={setOpen}
          >
            <Transition.Child
              as={Fragment}
              enter="transition-opacity ease-linear duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="transition-opacity ease-linear duration-300"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-gray-900/80" />
            </Transition.Child>

            <div className="fixed inset-0 flex justify-center">
              <Transition.Child
                as={Fragment}
                enter="transition ease-in-out duration-300 transform"
                enterFrom="-translate-x-full"
                enterTo="translate-x-0"
                leave="transition ease-in-out duration-300 transform"
                leaveFrom="translate-x-0"
                leaveTo="-translate-x-full"
              >
                <Dialog.Panel className="fixed inset-y-0 right-0 flex w-full justify-center">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-in-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in-out duration-300"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <div className="absolute right-8 top-8 flex">
                      <button
                        type="button"
                        className="p-2.5 text-white"
                        onClick={() => {
                          setOpen(false);
                          navigate("/user/knowledge_bank");
                        }}
                      >
                        <span className="sr-only">Close sidebar</span>
                        <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                      </button>
                    </div>
                  </Transition.Child>
                  <div className="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
                    <div className="flex min-h-[300px] w-full min-w-[500px] items-center justify-center rounded-[3px] bg-white">
                      <h4>Uploaded successfully</h4>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </Dialog>
        </Transition.Root>

        <div className=" rounded-lg  bg-[#1d2937]  p-10 text-white  md:max-w-2xl  lg:max-w-2xl 2xl:max-w-2xl">
          <p className="text-gray-200">
            Enter a unique name and description for the voice. Upload the audio
            file by selecting or dragging it into the box. Supported formats:
            MP3, WAV. Click "Save" to submit.
          </p>
          <div className="mt-3">
            <h3 className="mb-2 text-[13px] font-medium lg:text-[15px]">
              Voice Name
            </h3>
            <input
              onChange={handleChange}
              type="text"
              className="h-[30px] w-full rounded-md bg-transparent xl:h-[43px]"
            />
          </div>
          <div className="mt-3">
            <h3 className="mb-2 text-[13px] font-medium lg:text-[15px]">
              Description
            </h3>
            <input
              onChange={(e) => handleChange(e, 2)}
              type="text"
              className="h-[30px] w-full rounded-md bg-transparent xl:h-[43px] "
            />
          </div>
          <div className="mt-3">
            <h3 className="mb-2 text-[13px] font-medium lg:text-[15px]">
              Voice(s)
            </h3>
          </div>
          {/* TODO: don't use this component as it manages fileObj state internally so delete will not work */}
          <MkdFileUpload
            multiple={true}
            name={"fileData"}
            fileType={"audio"}
            onAddSuccess={(files) => {
              setFileObj(files.fileData);
            }}
            removeWidthStyles={true}
          />
          <div className="mt-4 space-y-4">
            {fileObj.map((item, index) => (
              <div
                className="flex items-center justify-between rounded-xl border-2 border-gray-200 p-4 font-medium"
                key={fileItemId}
              >
                <p>{item.file?.name}</p>
                <button
                  type="button"
                  onClick={() => {
                    setFileObj((prev) => {
                      const copy = [...prev];
                      copy.splice(index, 1);
                      return copy;
                    });
                  }}
                >
                  <XMarkIcon className="h-5 w-5 2xl:w-7" />
                </button>
              </div>
            ))}
          </div>
          <div className="flex w-full items-center justify-center">
            <button
              onClick={submitData}
              className="mt-3 flex w-full items-center justify-center gap-2 rounded-[3px] bg-[#19b2f6]/80 px-5 py-2 text-white shadow-md shadow-black/30 hover:bg-[#19b2f6]/90"
            >
              {loading ? (
                <MoonLoader color={"white"} loading={true} size={20} />
              ) : null}
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;
