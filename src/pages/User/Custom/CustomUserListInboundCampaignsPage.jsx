import React, { Fragment, useRef, useState } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { Dialog, Popover, Transition } from "@headlessui/react";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { MkdDebounceInput } from "Components/MkdDebounceInput";
import { RiDeleteBin5Line } from "react-icons/ri";
import { AddButton } from "Components/AddButton";
import CustomDeleteModal from "Components/MkdListTable/CustomDeleteModal";
import TreeSDK from "Utils/TreeSDK";
import { MkdListTableHead } from "Components/MkdListTable";
import { useForm } from "react-hook-form";
import PaginationBarV2 from "Components/PaginationBar/PaginationBarV2";
import TableSkeleton from "Components/MkdListTable/TableSkeleton";
import UserAddInboundCampaignPage from "../Add/UserAddInboundCampaignPage";
import { XMarkIcon } from "@heroicons/react/24/outline";
import UserAddOutboundCampaignPage from "../Add/UserAddOutboundCampaignPage";
import { XIcon } from "lucide-react";

let sdk = new MkdSDK();

const STATUS_MAPPING = {
  0: "Inactive",
  1: "Active",
  2: "Paused",
};

const REVERSE_STATUS_MAPPING = {
  inactive: 0,
  active: 1,
  paused: 2,
  INACTIVE: 0,
  ACTIVE: 1,
  PAUSED: 2,
  Inactive: 0,
  Active: 1,
  Paused: 2,
};

const columns = [
  {
    header: "Action",
    accessor: "",
  },
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Name",
    accessor: "name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Assistant name",
    accessor: "assistant_name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Contacts",
    accessor: "called",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: STATUS_MAPPING,
  },
];

const filterableColumns = columns.filter((col) => {
  const excludedColumns = ["", "assistant_name", "called"];
  return !excludedColumns.includes(col.accessor);
});

const CAMPAIGN_STATUSES = {
  INACTIVE: 0,
  ACTIVE: 1,
  PAUSED: 2,
};

const CustomUserListInboundCampaignsPage = () => {
  const { state: authState, dispatch: authDispatch } =
    React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [searchValue, setSearchValue] = React.useState("");
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [currentTableData, setCurrentTableData] = React.useState([]);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [deleteId, setDeleteId] = React.useState(null);
  const [searchParams, setSearchParams] = useSearchParams();

  const [paginationData, setPaginationData] = React.useState({
    currentPage: 0,
    pageSize: 0,
    totalNumber: 0,
    totalPages: 0,
  });

  const { handleSubmit } = useForm({ defaultValues: {} });

  const [filterValues, setFilterValues] = React.useState({});
  const [optionValue, setOptionValue] = React.useState("eq");

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "inbound_campaigns",
      },
    });

    getData();
  }, [filterConditions, searchValue]);

  const addFilterCondition = (option, selectedValue, inputValue) => {
    if (!inputValue) {
      setFilterConditions((prevConditions) => {
        return prevConditions.filter((cond) => !cond.startsWith(option + ","));
      });
      return;
    }

    let value = inputValue;
    let operator = selectedValue || "eq";

    if (option === "status") {
      const statusKey = inputValue.toLowerCase();
      if (REVERSE_STATUS_MAPPING.hasOwnProperty(statusKey)) {
        value = REVERSE_STATUS_MAPPING[statusKey];
      }
    } else if (operator === "eq") {
      value = inputValue;
    } else {
      value = inputValue.toLowerCase();
    }

    const condition = `${option},${operator},${value}`;

    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (cond) => !cond.startsWith(option + ",")
      );
      return [...newConditions, condition];
    });
  };

  async function getData() {
    try {
      setLoading(true);
      const tdk = new TreeSDK();
      const result = await tdk.getPaginate("campaign", {
        size: searchParams.get("limit") ?? 50,
        page: searchParams.get("page") ?? 1,
        filter: [
          "campaign_type,eq,2",
          `${tdk.getProjectId()}_campaign.user_id,eq,${authState.user}`,
        ],
        join: "assistants|assistant_id",
      });

      const { list, total, limit, num_pages, page } = result;
      setCurrentTableData(list);
      setPaginationData({
        currentPage: page,
        pageSize: limit,
        totalNumber: total,
        totalPages: num_pages,
      });
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(authDispatch, error.message);
      showToast(globalDispatch, error.message, 5000, "error");
    }
    setLoading(false);
  }

  async function updateCampaignStatus(id, status) {
    setLoading(true);
    try {
      sdk.setTable("campaign");
      await sdk.callRestAPI({ status, id }, "PUT");
      showToast(globalDispatch, "Updated");
      getData();
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      showToast(globalDispatch, err.message, 5000, "error");
    }
    setLoading(false);
  }

  async function deleteCampaign(campaign_id) {
    setLoading(true);
    try {
      sdk.setTable("campaign");
      await sdk.callRestAPI({ id: campaign_id }, "DELETE");
      showToast(globalDispatch, "Deleted");
      getData();
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      showToast(globalDispatch, err.message, 5000, "error");
    }
    setLoading(false);
  }

  const handleClearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    setFilterValues({});
    getData();
  };

  return (
    <>
      <div className="overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5">
        <h3 className="my-4 px-8 text-2xl font-bold text-white">
          Inbound Campaigns
        </h3>
        <div className="bg-[#1d2937] px-8 py-4">
          <div className="flex h-fit items-center justify-between gap-3">
            <div className="flex w-[200px] min-w-[200px] items-center justify-between">
              <div className="relative z-10 rounded bg-[#1d2937]">
                <Popover>
                  <div className="flex items-center gap-4 bg-[#1d2937] text-white">
                    <Popover.Button className="flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white focus-visible:outline-0 focus-visible:outline-transparent">
                      <BiFilterAlt />
                      <span>Filters</span>
                      {selectedOptions.length > 0 && (
                        <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white">
                          {selectedOptions.length}
                        </span>
                      )}
                    </Popover.Button>
                    <div className="focus-within:border-gray-40 flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white">
                      <BiSearch className="text-xl text-white" />
                      <input
                        type="text"
                        placeholder="search by name"
                        className="border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none"
                        style={{ boxShadow: "0 0 transparent" }}
                        value={searchValue}
                        onChange={(e) => {
                          setSearchValue(e.target.value);
                          addFilterCondition("name", "cs", e.target.value);
                        }}
                      />
                      {searchValue && (
                        <AiOutlineClose
                          className="cursor-pointer text-lg text-white"
                          onClick={() => {
                            setSearchValue("");
                            addFilterCondition("name", "cs", "");
                          }}
                        />
                      )}
                    </div>
                  </div>

                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <Popover.Panel>
                      <div className="filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10">
                        <span className="absolute left-5 top-2 font-medium text-white">
                          Filters
                        </span>
                        <Popover.Button
                          onClick={() => {
                            console.log("clicked");
                            setSelectedOptions([]);
                            setFilterConditions([]);
                            setFilterValues({});
                          }}
                        >
                          <XIcon className="absolute right-2 top-2 cursor-pointer text-white" />
                        </Popover.Button>
                        {selectedOptions?.map((option, index) => (
                          <div
                            key={index}
                            className="mb-2 flex w-full items-center justify-between gap-3 text-gray-600"
                          >
                            <button
                              type="button"
                              className="block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none"
                              title={option}
                              style={{ WebkitTouchCallout: "none" }}
                            >
                              {filterableColumns.find(
                                (col) => col.accessor === option
                              )?.header || option}
                            </button>

                            <select
                              className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none"
                              value={optionValue}
                              onChange={(e) => {
                                setOptionValue(e.target.value);
                                addFilterCondition(
                                  option,
                                  e.target.value,
                                  filterValues[option]
                                );
                              }}
                            >
                              <option value="eq">equals</option>
                              <option value="cs">contains</option>
                              <option value="sw">start with</option>
                              <option value="ew">ends with</option>
                              <option value="lt">lower than</option>
                              <option value="le">lower or equal</option>
                              <option value="ge">greater or equal</option>
                              <option value="gt">greater than</option>
                              <option value="bt">between</option>
                              <option value="in">in</option>
                              <option value="is">is null</option>
                            </select>

                            {option === "status" ? (
                              <select
                                value={filterValues[option] || ""}
                                onChange={(e) => {
                                  setFilterValues((prev) => ({
                                    ...prev,
                                    [option]: e.target.value,
                                  }));
                                  addFilterCondition(
                                    option,
                                    optionValue,
                                    e.target.value
                                  );
                                }}
                                className="h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                              >
                                <option value="">Select Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                              </select>
                            ) : (
                              <MkdDebounceInput
                                type="text"
                                labelClassName="!mb-0"
                                placeholder="Enter value"
                                setValue={() => {}}
                                showIcon={false}
                                className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                                onReady={(value) =>
                                  addFilterCondition(option, optionValue, value)
                                }
                              />
                            )}

                            <RiDeleteBin5Line
                              className="cursor-pointer text-2xl text-red-600"
                              onClick={() => {
                                setSelectedOptions((prevOptions) =>
                                  prevOptions.filter((op) => op !== option)
                                );
                                setFilterConditions((prevConditions) =>
                                  prevConditions.filter(
                                    (condition) => !condition.includes(option)
                                  )
                                );
                                setFilterValues((prev) => {
                                  const newValues = { ...prev };
                                  delete newValues[option];
                                  return newValues;
                                });
                              }}
                            />
                          </div>
                        ))}

                        <div className="search-buttons relative flex items-center justify-between font-semibold">
                          <div
                            className="mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out"
                            onClick={() =>
                              setShowFilterOptions(!showFilterOptions)
                            }
                          >
                            <AiOutlinePlus />
                            Add filter
                          </div>

                          {showFilterOptions && (
                            <div className="absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3">
                              <ul className="flex flex-col gap-2 text-gray-500">
                                {filterableColumns.map((column) => (
                                  <li
                                    key={column.accessor}
                                    className={`${
                                      selectedOptions.includes(column.accessor)
                                        ? "cursor-not-allowed text-gray-100"
                                        : "cursor-pointer text-gray-400 hover:text-white"
                                    }`}
                                    onClick={() => {
                                      if (
                                        !selectedOptions.includes(
                                          column.accessor
                                        )
                                      ) {
                                        setSelectedOptions((prev) => [
                                          ...prev,
                                          column.accessor,
                                        ]);
                                        setFilterValues((prev) => ({
                                          ...prev,
                                          [column.accessor]: "",
                                        }));
                                      }
                                      setShowFilterOptions(false);
                                    }}
                                  >
                                    {column.header}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {selectedOptions.length > 0 && (
                            <div
                              onClick={handleClearAllFilters}
                              className="inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white"
                            >
                              Clear all filter
                            </div>
                          )}
                        </div>
                      </div>
                    </Popover.Panel>
                  </Transition>
                </Popover>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <AddButton
                onClick={() => {
                  setShowAddSidebar(true);
                }}
                showChildren={true}
                className="relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]"
              >
                Add New
              </AddButton>
            </div>
          </div>

          <div className="mt-4 overflow-x-auto  bg-[#1d2937]">
            <div
              className={
                loading ? "" : "overflow-x-auto border-b border-gray-200 shadow"
              }
            >
              {loading && currentTableData.length === 0 ? (
                <TableSkeleton columns={columns} />
              ) : (
                <table className="min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]">
                  <thead className="bg-[#1d2937]">
                    <MkdListTableHead
                      actionPosition={"onTable"}
                      onSort={() => {}}
                      columns={columns}
                      actions={{ view: { show: true } }}
                    />
                  </thead>
                  <tbody className="divide-y divide-gray-400 bg-[#1d2937]">
                    {currentTableData.map((row) => (
                      <tr key={row.id} className="text-white">
                        <td className="pl-3">
                          <div className="flex max-w-[260px] items-center justify-between gap-3 text-sm">
                            {row.status === 1 ? (
                              <button
                                title="Pause Campaign"
                                className="rounded-[30px] bg-orange-500/20 p-1.5 px-5 font-medium text-orange-500 transition-colors hover:bg-orange-500/30"
                                disabled={loading}
                                onClick={() =>
                                  updateCampaignStatus(
                                    row.id,
                                    CAMPAIGN_STATUSES.PAUSED
                                  )
                                }
                              >
                                Pause
                              </button>
                            ) : null}
                            {row.status !== 1 ? (
                              <button
                                title="Start Campaign"
                                className="rounded-[30px] bg-green-500/20 p-1.5 px-5 font-medium text-green-500 transition-colors hover:bg-green-500/30"
                                disabled={loading}
                                onClick={() =>
                                  updateCampaignStatus(
                                    row.id,
                                    CAMPAIGN_STATUSES.ACTIVE
                                  )
                                }
                              >
                                Start
                              </button>
                            ) : null}

                            <button
                              title="Delete Campaign"
                              className="rounded-[30px] bg-red-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-red-500/30"
                              onClick={() => {
                                setShowDeleteModal(true);
                                setDeleteId(row.id);
                              }}
                            >
                              Delete
                            </button>
                            <Link
                              to={`/user/inbound_call_logs?campaign_id=${row.id}`}
                            >
                              <button
                                title="View Call Logs"
                                className="rounded-[30px] bg-blue-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-blue-500/30"
                                type="button"
                              >
                                View
                              </button>
                            </Link>
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {row.id}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {row.name}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {row.assistants?.assistant_name ?? "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {row.contacts ?? "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {STATUS_MAPPING[row.status] ?? "N/A"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>

          <PaginationBarV2 paginationData={paginationData} />
        </div>
      </div>

      <Transition appear show={showAddSidebar} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-[100]"
          onClose={() => setShowAddSidebar(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-3xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                  <div className="mb-4 flex items-center justify-between">
                    <Dialog.Title className="text-xl font-medium text-white">
                      Add New Campaign
                    </Dialog.Title>
                    <button onClick={() => setShowAddSidebar(false)}>
                      <XMarkIcon className="h-6 w-6 text-white/70 hover:text-white" />
                    </button>
                  </div>
                  <UserAddInboundCampaignPage
                    closeSidebar={() => {
                      setShowAddSidebar(false);
                      getData();
                    }}
                  />
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <CustomDeleteModal
        isOpen={showDeleteModal && !!deleteId}
        closeModal={() => setShowDeleteModal(false)}
        onDelete={async () => {
          await deleteCampaign(deleteId);
          setDeleteId(null);
          setShowDeleteModal(false);
        }}
      />
    </>
  );
};

export default CustomUserListInboundCampaignsPage;
