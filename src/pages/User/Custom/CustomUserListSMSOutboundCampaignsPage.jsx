import React, { Fragment } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { Dialog, Popover, Transition } from "@headlessui/react";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { MkdDebounceInput } from "Components/MkdDebounceInput";
import { RiDeleteBin5Line } from "react-icons/ri";
import { AddButton } from "Components/AddButton";
import CustomDeleteModal from "Components/MkdListTable/CustomDeleteModal";
import TreeSDK from "Utils/TreeSDK";
import { MkdListTableHead } from "Components/MkdListTable";
import { useForm } from "react-hook-form";
import PaginationBarV2 from "Components/PaginationBar/PaginationBarV2";
import TableSkeleton from "Components/MkdListTable/TableSkeleton";
import { ArrowDownTrayIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { parseJsonSafely } from "Utils/utils";
import { XIcon } from "lucide-react";
import { UserAddSMSOutboundCampaignPage } from "Routes/LazyLoad";

let sdk = new MkdSDK();

const STATUS_MAPPING = {
  0: "Inactive",
  1: "Active",
  2: "Paused",
};

const REVERSE_STATUS_MAPPING = {
  inactive: 0,
  active: 1,
  paused: 2,
  INACTIVE: 0,
  ACTIVE: 1,
  PAUSED: 2,
  Inactive: 0,
  Active: 1,
  Paused: 2,
};

const columns = [
  {
    header: "Action",
    accessor: "",
  },
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Name",
    accessor: "name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Assistant Name",
    accessor: "assistant_name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "# of contacts",
    accessor: "num_of_contacts",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "# Sent",
    accessor: "total_sms",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: STATUS_MAPPING,
  },
];

const filterableColumns = columns.filter((col) => {
  const excludedColumns = ["", "assistant_name", "num_of_contacts"];
  return !excludedColumns.includes(col.accessor);
});

const CAMPAIGN_STATUSES = {
  INACTIVE: 0,
  ACTIVE: 1,
  PAUSED: 2,
};

const CustomUserListSMSOutboundCampaignsPage = () => {
  const { state: authState, dispatch: authDispatch } =
    React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [searchValue, setSearchValue] = React.useState("");
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [currentTableData, setCurrentTableData] = React.useState([]);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [deleteId, setDeleteId] = React.useState(null);
  const [searchParams, setSearchParams] = useSearchParams();

  const [paginationData, setPaginationData] = React.useState({
    currentPage: 0,
    pageSize: 0,
    totalNumber: 0,
    totalPages: 0,
  });

  const { handleSubmit } = useForm({ defaultValues: {} });

  const [filterValues, setFilterValues] = React.useState({});
  const [optionValue, setOptionValue] = React.useState("eq");

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "sms_outbound_campaigns",
      },
    });

    getData();
  }, [filterConditions, searchValue]);

  const addFilterCondition = (option, selectedValue, inputValue) => {
    if (!inputValue) {
      setFilterConditions((prevConditions) => {
        return prevConditions.filter((cond) => !cond.startsWith(option + ","));
      });
      return;
    }

    let value = inputValue;
    let operator = selectedValue || "eq";

    if (option === "status") {
      const statusKey = inputValue.toLowerCase();
      if (REVERSE_STATUS_MAPPING.hasOwnProperty(statusKey)) {
        value = REVERSE_STATUS_MAPPING[statusKey];
      }
    } else if (operator === "eq") {
      value = inputValue;
    } else {
      value = inputValue.toLowerCase();
    }

    const condition = `${option},${operator},${value}`;

    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (cond) => !cond.startsWith(option + ",")
      );
      return [...newConditions, condition];
    });
  };

  async function getData() {
    try {
      setLoading(true);
      const tdk = new TreeSDK();
      const tablePrefix = `${tdk.getProjectId()}_campaign`;

      let filterArray = [
        `${tablePrefix}.campaign_type,eq,3`,
        `${tablePrefix}.user_id,eq,${authState.user}`,
      ];

      if (searchValue) {
        filterArray.push(`${tablePrefix}.name,cs,${searchValue}`);
      }

      if (filterConditions.length > 0) {
        const updatedFilters = filterConditions.map((condition) => {
          const [field, operator, value] = condition.split(",");
          return `${tablePrefix}.${field},${operator},${value}`;
        });
        filterArray = [...filterArray, ...updatedFilters];
      }

      const result = await tdk.getPaginate("campaign", {
        size: searchParams.get("limit") ?? 50,
        page: searchParams.get("page") ?? 1,
        filter: filterArray,
        join: "assistants|assistant_id",
      });

      const { list, total, limit, num_pages, page } = result;
      setCurrentTableData(list);
      setPaginationData({
        currentPage: page,
        pageSize: limit,
        totalNumber: total,
        totalPages: num_pages,
      });
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(authDispatch, error.message);
      showToast(globalDispatch, error.message, 5000, "error");
    }
    setLoading(false);
  }

  async function updateCampaignStatus(id, status) {
    setLoading(true);
    try {
      sdk.setTable("campaign");
      await sdk.callRestAPI({ status, id }, "PUT");
      showToast(globalDispatch, "Updated");
      getData();
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      showToast(globalDispatch, err.message, 5000, "error");
    }
    setLoading(false);
  }

  async function deleteCampaign(campaign_id) {
    setLoading(true);
    try {
      sdk.setTable("campaign");
      await sdk.callRestAPI({ id: campaign_id }, "DELETE");
      showToast(globalDispatch, "Deleted");
      getData();
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      showToast(globalDispatch, err.message, 5000, "error");
    }
    setLoading(false);
  }

  const handleClearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    setFilterValues({});
    getData();
  };

  return (
    <>
      <div className="overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5">
        <h3 className="my-4 px-8 text-2xl font-bold text-white">
          SMS Outbound Campaigns
        </h3>
        <div className="bg-[#1d2937] px-8 py-4">
          <div className="flex h-fit items-center justify-between gap-3">
            <div className="flex w-[200px] min-w-[200px] items-center justify-between">
              <div className="relative z-10 rounded bg-[#1d2937]">
                <Popover>
                  <div className="flex items-center gap-4 bg-[#1d2937] text-white">
                    <Popover.Button className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white focus-visible:outline-0 focus-visible:outline-transparent">
                      <BiFilterAlt />
                      <span>Filters</span>
                      {selectedOptions.length > 0 && (
                        <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white">
                          {selectedOptions.length}
                        </span>
                      )}
                    </Popover.Button>
                    <div className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white focus-within:border-gray-400">
                      <BiSearch className="text-xl text-white" />
                      <input
                        type="text"
                        placeholder="search by name"
                        className="border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none"
                        style={{ boxShadow: "0 0 transparent" }}
                        value={searchValue}
                        onChange={(e) => {
                          setSearchValue(e.target.value);
                          addFilterCondition("name", "cs", e.target.value);
                        }}
                      />
                      {searchValue && (
                        <AiOutlineClose
                          className="cursor-pointer text-lg text-white"
                          onClick={() => {
                            setSearchValue("");
                            addFilterCondition("name", "cs", "");
                          }}
                        />
                      )}
                    </div>
                  </div>

                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <Popover.Panel>
                      <div className="filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10">
                        <span className="absolute left-5 top-2 font-medium text-white">
                          Filters
                        </span>
                        <Popover.Button
                          onClick={() => {
                            console.log("clicked");
                            setSelectedOptions([]);
                            setFilterConditions([]);
                            setFilterValues({});
                          }}
                        >
                          <XIcon className="absolute right-2 top-2 cursor-pointer text-white" />
                        </Popover.Button>
                        {selectedOptions?.map((option, index) => (
                          <div
                            key={index}
                            className="mb-2 flex w-full items-center justify-between gap-3 text-gray-600"
                          >
                            <button
                              type="button"
                              className="block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none"
                              title={option}
                              style={{ WebkitTouchCallout: "none" }}
                            >
                              {filterableColumns.find(
                                (col) => col.accessor === option
                              )?.header || option}
                            </button>

                            <select
                              className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none"
                              value={optionValue}
                              onChange={(e) => {
                                setOptionValue(e.target.value);
                                addFilterCondition(
                                  option,
                                  e.target.value,
                                  filterValues[option]
                                );
                              }}
                            >
                              <option value="eq">equals</option>
                              <option value="cs">contains</option>
                              <option value="sw">start with</option>
                              <option value="ew">ends with</option>
                              <option value="lt">lower than</option>
                              <option value="le">lower or equal</option>
                              <option value="ge">greater or equal</option>
                              <option value="gt">greater than</option>
                              <option value="bt">between</option>
                              <option value="in">in</option>
                              <option value="is">is null</option>
                            </select>

                            {option === "status" ? (
                              <select
                                value={filterValues[option] || ""}
                                onChange={(e) => {
                                  setFilterValues((prev) => ({
                                    ...prev,
                                    [option]: e.target.value,
                                  }));
                                  addFilterCondition(
                                    option,
                                    optionValue,
                                    e.target.value
                                  );
                                }}
                                className="h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                              >
                                <option value="">Select Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                              </select>
                            ) : (
                              <MkdDebounceInput
                                type="text"
                                labelClassName="!mb-0"
                                placeholder="Enter value"
                                setValue={() => {}}
                                showIcon={false}
                                className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                                onReady={(value) =>
                                  addFilterCondition(option, optionValue, value)
                                }
                              />
                            )}

                            <RiDeleteBin5Line
                              className="cursor-pointer text-2xl text-red-600"
                              onClick={() => {
                                setSelectedOptions((prevOptions) =>
                                  prevOptions.filter((op) => op !== option)
                                );
                                setFilterConditions((prevConditions) =>
                                  prevConditions.filter(
                                    (condition) => !condition.includes(option)
                                  )
                                );
                                setFilterValues((prev) => {
                                  const newValues = { ...prev };
                                  delete newValues[option];
                                  return newValues;
                                });
                              }}
                            />
                          </div>
                        ))}

                        <div className="search-buttons relative flex items-center justify-between font-semibold">
                          <div
                            className="mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out"
                            onClick={() =>
                              setShowFilterOptions(!showFilterOptions)
                            }
                          >
                            <AiOutlinePlus />
                            Add filter
                          </div>

                          {showFilterOptions && (
                            <div className="absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3">
                              <ul className="flex flex-col gap-2 text-gray-500">
                                {filterableColumns.map((column) => (
                                  <li
                                    key={column.accessor}
                                    className={`${
                                      selectedOptions.includes(column.accessor)
                                        ? "cursor-not-allowed text-gray-100"
                                        : "cursor-pointer text-gray-400 hover:text-white"
                                    }`}
                                    onClick={() => {
                                      if (
                                        !selectedOptions.includes(
                                          column.accessor
                                        )
                                      ) {
                                        setSelectedOptions((prev) => [
                                          ...prev,
                                          column.accessor,
                                        ]);
                                        setFilterValues((prev) => ({
                                          ...prev,
                                          [column.accessor]: "",
                                        }));
                                      }
                                      setShowFilterOptions(false);
                                    }}
                                  >
                                    {column.header}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {selectedOptions.length > 0 && (
                            <div
                              onClick={handleClearAllFilters}
                              className="inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white"
                            >
                              Clear all filter
                            </div>
                          )}
                        </div>
                      </div>
                    </Popover.Panel>
                  </Transition>
                </Popover>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <a
                className="relative flex h-9 items-center justify-center gap-2 rounded-md border border-[#19b2f6]/80 bg-transparent px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]/20"
                href="/contact_template.csv"
                download={"template.csv"}
                target="_blank"
              >
                <ArrowDownTrayIcon className="h-4 w-4" strokeWidth={2} />
                Contact Template
              </a>
              <AddButton
                onClick={() => {
                  setShowAddSidebar(true);
                }}
                showChildren={true}
                className="relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]"
              >
                Add New
              </AddButton>
            </div>
          </div>

          <div className="mt-4 overflow-x-auto  bg-[#1d2937]">
            <div className="overflow-x-auto border-b border-gray-400">
              {loading && currentTableData.length === 0 ? (
                <TableSkeleton columns={columns} />
              ) : (
                <table className="min-w-full divide-y  divide-gray-400 rounded border border-b-0 border-gray-400 bg-[#1d2937] bg-[#1d2937]">
                  <thead className="bg-[#1d2937]">
                    <MkdListTableHead
                      actionPosition={"onTable"}
                      onSort={() => {}}
                      columns={columns}
                      actions={{ view: { show: true } }}
                    />
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-[#1d2937]">
                    {currentTableData.map((row) => (
                      <tr key={row.id} className="text-white">
                        <td className="pl-3">
                          <div className="flex max-w-[520px] items-center justify-between gap-3 text-sm">
                            {row.status === 1 ? (
                              <button
                                title="Pause Campaign"
                                className="rounded-[30px] bg-orange-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-orange-500/30"
                                disabled={loading}
                                onClick={() =>
                                  updateCampaignStatus(
                                    row.id,
                                    CAMPAIGN_STATUSES.PAUSED
                                  )
                                }
                              >
                                Pause
                              </button>
                            ) : null}
                            {row.status !== 1 ? (
                              <button
                                title="Start Campaign"
                                className="rounded-[30px] bg-green-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-green-500/30"
                                disabled={loading}
                                onClick={() =>
                                  updateCampaignStatus(
                                    row.id,
                                    CAMPAIGN_STATUSES.ACTIVE
                                  )
                                }
                              >
                                Start
                              </button>
                            ) : null}
                            <button
                              title="Delete Campaign"
                              className="rounded-[30px] bg-red-500/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-red-500/30"
                              onClick={() => {
                                setShowDeleteModal(true);
                                setDeleteId(row.id);
                              }}
                            >
                              Delete
                            </button>
                            <Link
                              to={`/user/sms_outbound_logs?campaign_id=${row.id}`}
                            >
                              <button
                                title="View SMS Logs"
                                className="whitespace-nowrap rounded-[30px] bg-[#19b2f6]/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-[#19b2f6]/30"
                                type="button"
                              >
                                View Logs
                              </button>
                            </Link>
                            <Link
                              to={`/user/campaign-summary?campaign_id=${row.id}`}
                            >
                              <button
                                title="View Summary"
                                className="whitespace-nowrap rounded-[30px] bg-purple-500/20 p-1.5 px-5 font-medium text-white transition-colors"
                                type="button"
                              >
                                View Summary
                              </button>
                            </Link>
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {row.id}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {row.name}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {row.assistants?.assistant_name ?? "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          <p className="max-w-3xl truncate">
                            {parseJsonSafely(row.contacts, []).length}
                          </p>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {row.total_sms ?? "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {STATUS_MAPPING[row.status] ?? "N/A"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>

          <PaginationBarV2 paginationData={paginationData} />
        </div>
      </div>

      <UserAddSMSOutboundCampaignPage
        closeSidebar={() => {
          setShowAddSidebar(false);
          getData();
        }}
        isOpen={showAddSidebar}
      />

      <CustomDeleteModal
        isOpen={showDeleteModal && !!deleteId}
        closeModal={() => setShowDeleteModal(false)}
        onDelete={async () => {
          await deleteCampaign(deleteId);
          setDeleteId(null);
          setShowDeleteModal(false);
        }}
      />
    </>
  );
};

export default CustomUserListSMSOutboundCampaignsPage;
