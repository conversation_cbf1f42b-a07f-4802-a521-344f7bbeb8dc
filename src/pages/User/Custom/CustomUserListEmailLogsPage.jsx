import React, { Fragment } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Dialog, Popover, Transition } from "@headlessui/react";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { MkdDebounceInput } from "Components/MkdDebounceInput";
import { RiDeleteBin5Line } from "react-icons/ri";
import TreeSDK from "Utils/TreeSDK";
import { MkdListTableHead } from "Components/MkdListTable";
import PaginationBarV2 from "Components/PaginationBar/PaginationBarV2";
import TableSkeleton from "Components/MkdListTable/TableSkeleton";
import { useCSVDownloader } from "react-papaparse";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { XIcon } from "lucide-react";
import MkdSDK from "Utils/MkdSDK";

const STATUS_MAPPING = {
  0: "Inactive",
  1: "Active",
  2: "Paused",
  3: "Completed",
};

const columns = [
  { header: "Action", accessor: "" },
  { header: "Id", accessor: "id" },
  { header: "To Email", accessor: "to_email" },
  { header: "From Email", accessor: "from_email" },
  {
    header: "Status",
    accessor: "status",
    mappingExist: true,
    mappings: STATUS_MAPPING,
  },
  { header: "Provider", accessor: "provider" },
  { header: "Created At", accessor: "create_at" },
];

const filterableColumns = columns.filter((col) => col.accessor);

const CustomUserListEmailLogsPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch: authDispatch } = React.useContext(AuthContext);
  const navigate = useNavigate();
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [searchValue, setSearchValue] = React.useState("");
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [currentTableData, setCurrentTableData] = React.useState([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const { CSVDownloader } = useCSVDownloader();
  const [isViewModalOpen, setIsViewModalOpen] = React.useState(false);
  const [selectedRow, setSelectedRow] = React.useState(null);
  const [paginationData, setPaginationData] = React.useState({
    currentPage: 0,
    pageSize: 0,
    totalNumber: 0,
    totalPages: 0,
  });
  const [filterValues, setFilterValues] = React.useState({});
  const [optionValue, setOptionValue] = React.useState("eq");
  const [isEmailConnected, setIsEmailConnected] = React.useState(false);
  const [checkingIntegration, setCheckingIntegration] = React.useState(true);

  React.useEffect(() => {
    globalDispatch({ type: "SETPATH", payload: { path: "email_logs" } });
    checkEmailIntegration();
    getData();
  }, []);

  React.useEffect(() => {
    getData();
  }, [filterConditions, searchValue]);

  const checkEmailIntegration = async () => {
    try {
      setCheckingIntegration(true);
      const sdk = new MkdSDK();

      const response = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/gmail/check-integration",
        [],
        "GET"
      );

      if (response && response.active) {
        setIsEmailConnected(true);
      } else {
        setIsEmailConnected(false);
        showToast(
          globalDispatch,
          "Email not connected - logs may not be updated",
          5000,
          "warning"
        );
      }
    } catch (error) {
      console.error("Error checking email integration:", error);
      setIsEmailConnected(false);
      showToast(
        globalDispatch,
        "Unable to check email integration status",
        5000,
        "error"
      );
    } finally {
      setCheckingIntegration(false);
    }
  };

  const addFilterCondition = (option, selectedValue, inputValue) => {
    if (!inputValue) {
      setFilterConditions((prev) =>
        prev.filter((cond) => !cond.startsWith(option + ","))
      );
      return;
    }
    let value = inputValue;
    let operator = selectedValue || "eq";
    if (option === "email_history") operator = "cs";
    const condition = `${option},${operator},${value}`;
    setFilterConditions((prev) => {
      const newConditions = prev.filter(
        (cond) => !cond.startsWith(option + ",")
      );
      return [...newConditions, condition];
    });
  };

  const EmailHistory = ({ emailHistory }) => {
    const [visibleMessages, setVisibleMessages] = React.useState(4);
    const handleViewMore = () => setVisibleMessages((prev) => prev + 4);
    return (
      <>
        {emailHistory.slice(0, visibleMessages).map((email, idx) => (
          <div
            key={idx}
            className={`mb-2 overflow-x-auto rounded-md p-3 ${
              email.role === "user"
                ? "bg-blue-500/20 text-white"
                : "bg-green-500/20 text-white"
            }`}
          >
            <p className="font-medium capitalize">{email.role}:</p>
            <div className="mt-1 whitespace-pre-wrap">{email.content}</div>
          </div>
        ))}
        {visibleMessages < emailHistory.length && (
          <button
            onClick={handleViewMore}
            className="mt-4 text-[#19b2f6] hover:underline"
          >
            View More
          </button>
        )}
      </>
    );
  };

  async function getData() {
    try {
      setLoading(true);
      const tdk = new TreeSDK();
      const tablePrefix = `${tdk.getProjectId()}_email_logs`;
      let filterArray = [
        searchParams.get("campaign_id")
          ? `${tablePrefix}.campaign_id,eq,${searchParams.get("campaign_id")}`
          : undefined,
      ].filter(Boolean);
      if (searchValue)
        filterArray.push(`${tablePrefix}.email_history,cs,${searchValue}`);
      if (filterConditions.length > 0) {
        const updatedFilters = filterConditions.map((condition) => {
          const [field, operator, value] = condition.split(",");
          return `${tablePrefix}.${field},${operator},${value}`;
        });
        filterArray = [...filterArray, ...updatedFilters];
      }
      const result = await tdk.getPaginate("email_logs", {
        size: searchParams.get("limit") ?? 50,
        page: searchParams.get("page") ?? 1,
        filter: filterArray.filter(Boolean),
      });
      const { list, total, limit, num_pages, page } = result;
      setCurrentTableData(list);
      setPaginationData({
        currentPage: page,
        pageSize: limit,
        totalNumber: total,
        totalPages: num_pages,
      });
    } catch (error) {
      tokenExpireError(authDispatch, error.message);
      showToast(globalDispatch, error.message, 5000, "error");
    }
    setLoading(false);
  }

  const handleClearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    setFilterValues({});
    for (const key of searchParams.keys()) {
      if (key !== "campaign_id") searchParams.delete(key);
    }
    setSearchParams(searchParams);
    getData();
  };

  const ViewModal = ({ isOpen, onClose, row }) => {
    if (!row) return null;
    let emailHistory = [];
    try {
      emailHistory = JSON.parse(row.email_history || "[]");
    } catch (e) {
      // ignore
    }
    return (
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-[100]" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                  <div className="mb-4 flex items-center justify-between">
                    <Dialog.Title className="text-xl font-medium text-white">
                      Email Details
                    </Dialog.Title>
                    <button onClick={onClose}>
                      <XMarkIcon className="h-6 w-6 text-white/70 hover:text-white" />
                    </button>
                  </div>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-white/70">ID</p>
                        <p className="text-white">{row.id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Status</p>
                        <p className="text-white">
                          {STATUS_MAPPING[row.status] || "N/A"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">To Email</p>
                        <p className="text-white">{row.to_email || "N/A"}</p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">From Email</p>
                        <p className="text-white">{row.from_email || "N/A"}</p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Provider</p>
                        <p className="text-white">{row.provider || "N/A"}</p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Created At</p>
                        <p className="text-white">{row.create_at || "N/A"}</p>
                      </div>
                    </div>
                    <div>
                      <p className="mb-2 text-sm text-white/70">
                        Email Conversation
                      </p>
                      {emailHistory.length > 0 ? (
                        <EmailHistory emailHistory={emailHistory} />
                      ) : (
                        <p className="text-white">No email history available</p>
                      )}
                    </div>
                    <div className="mt-6 flex justify-end">
                      <button
                        className="cursor-not-allowed rounded bg-blue-400 px-6 py-2 text-white opacity-50"
                        disabled
                      >
                        Send Reply
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    );
  };

  return (
    <>
      <div className="overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5">
        <h3 className="my-4 px-8 text-2xl font-bold text-white">Email Logs</h3>

        {/* Email Integration Warning Banner */}
        {!checkingIntegration && !isEmailConnected && (
          <div className="mx-8 mb-4 rounded-lg border border-yellow-500/30 bg-yellow-500/20 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-yellow-500/20 p-2">
                  <svg
                    className="h-5 w-5 text-yellow-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-yellow-200">
                    Email Not Connected
                  </p>
                  <p className="text-sm text-yellow-300">
                    Your email logs may not be updated. Connect your email for
                    real-time updates.
                  </p>
                </div>
              </div>
              <button
                onClick={() => navigate("/user/settings")}
                className="rounded-lg bg-yellow-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-yellow-600"
              >
                Connect Email
              </button>
            </div>
          </div>
        )}

        <div className="bg-[#1d2937] px-8 py-4">
          <div className="flex h-fit items-center justify-between gap-3">
            <div className="flex w-[200px] min-w-[200px] items-center justify-between">
              <div className="relative z-10 rounded bg-[#1d2937]">
                <Popover>
                  <div className="flex items-center gap-4 bg-[#1d2937] text-white">
                    <Popover.Button className="border-white/50ss flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white focus-visible:outline-0 focus-visible:outline-transparent ">
                      <BiFilterAlt />
                      <span>Filters</span>
                      {selectedOptions.length > 0 && (
                        <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white">
                          {selectedOptions.length}
                        </span>
                      )}
                    </Popover.Button>
                    <div className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white focus-within:border-gray-400">
                      <BiSearch className="text-xl text-white" />
                      <input
                        type="text"
                        placeholder="Search in email history"
                        className="border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none"
                        style={{ boxShadow: "0 0 transparent" }}
                        value={searchValue}
                        onChange={(e) => {
                          setSearchValue(e.target.value);
                          addFilterCondition(
                            "email_history",
                            "cs",
                            e.target.value
                          );
                        }}
                      />
                      {searchValue && (
                        <AiOutlineClose
                          className="cursor-pointer text-lg text-white"
                          onClick={() => {
                            setSearchValue("");
                            addFilterCondition("email_history", "cs", "");
                          }}
                        />
                      )}
                    </div>
                  </div>

                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <Popover.Panel>
                      <div className="filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10">
                        <span className="absolute left-5 top-2 font-medium text-white">
                          Filters
                        </span>
                        <Popover.Button
                          onClick={() => {
                            setSelectedOptions([]);
                            setFilterConditions([]);
                            setFilterValues({});
                          }}
                        >
                          <XIcon className="absolute right-2 top-2 cursor-pointer text-white" />
                        </Popover.Button>
                        {selectedOptions?.map((option, index) => (
                          <div
                            key={index}
                            className="mb-2 flex w-full items-center justify-between gap-3 text-gray-600"
                          >
                            <button
                              type="button"
                              className="block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none"
                              title={option}
                              style={{ WebkitTouchCallout: "none" }}
                            >
                              {filterableColumns.find(
                                (col) => col.accessor === option
                              )?.header || option}
                            </button>

                            <select
                              className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none"
                              value={optionValue}
                              onChange={(e) => {
                                setOptionValue(e.target.value);
                                addFilterCondition(
                                  option,
                                  e.target.value,
                                  filterValues[option]
                                );
                              }}
                            >
                              <option value="eq">equals</option>
                              <option value="cs">contains</option>
                              <option value="sw">start with</option>
                              <option value="ew">ends with</option>
                              <option value="lt">lower than</option>
                              <option value="le">lower or equal</option>
                              <option value="ge">greater or equal</option>
                              <option value="gt">greater than</option>
                              <option value="bt">between</option>
                              <option value="in">in</option>
                              <option value="is">is null</option>
                            </select>

                            {option === "status" ? (
                              <select
                                value={filterValues[option] || ""}
                                onChange={(e) => {
                                  setFilterValues((prev) => ({
                                    ...prev,
                                    [option]: e.target.value,
                                  }));
                                  addFilterCondition(
                                    option,
                                    optionValue,
                                    e.target.value
                                  );
                                }}
                                className="h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                              >
                                <option value="">Select Status</option>
                                <option value="0">Inactive</option>
                                <option value="1">Active</option>
                                <option value="2">Paused</option>
                                <option value="3">Completed</option>
                              </select>
                            ) : (
                              <MkdDebounceInput
                                type="text"
                                labelClassName="!mb-0"
                                placeholder="Enter value"
                                setValue={() => {}}
                                showIcon={false}
                                className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                                onReady={(value) =>
                                  addFilterCondition(option, optionValue, value)
                                }
                              />
                            )}

                            <RiDeleteBin5Line
                              className="cursor-pointer text-2xl text-red-600"
                              onClick={() => {
                                setSelectedOptions((prevOptions) =>
                                  prevOptions.filter((op) => op !== option)
                                );
                                setFilterConditions((prevConditions) =>
                                  prevConditions.filter(
                                    (condition) => !condition.includes(option)
                                  )
                                );
                                setFilterValues((prev) => {
                                  const newValues = { ...prev };
                                  delete newValues[option];
                                  return newValues;
                                });
                              }}
                            />
                          </div>
                        ))}

                        <div className="search-buttons relative flex items-center justify-between font-semibold">
                          <div
                            className="mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out"
                            onClick={() =>
                              setShowFilterOptions(!showFilterOptions)
                            }
                          >
                            <AiOutlinePlus />
                            Add filter
                          </div>

                          {showFilterOptions && (
                            <div className="absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3">
                              <ul className="flex flex-col gap-2 text-gray-500">
                                {filterableColumns.map((column) => (
                                  <li
                                    key={column.accessor}
                                    className={`${
                                      selectedOptions.includes(column.accessor)
                                        ? "cursor-not-allowed text-gray-100"
                                        : "cursor-pointer text-gray-400 hover:text-white"
                                    }`}
                                    onClick={() => {
                                      if (
                                        !selectedOptions.includes(
                                          column.accessor
                                        )
                                      ) {
                                        setSelectedOptions((prev) => [
                                          ...prev,
                                          column.accessor,
                                        ]);
                                        setFilterValues((prev) => ({
                                          ...prev,
                                          [column.accessor]: "",
                                        }));
                                      }
                                      setShowFilterOptions(false);
                                    }}
                                  >
                                    {column.header}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {selectedOptions.length > 0 && (
                            <div
                              onClick={handleClearAllFilters}
                              className="inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white"
                            >
                              Clear all filter
                            </div>
                          )}
                        </div>
                      </div>
                    </Popover.Panel>
                  </Transition>
                </Popover>
              </div>
            </div>

            <CSVDownloader
              filename={"email_logs"}
              className="relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]"
              data={() => currentTableData}
            >
              Download CSV
            </CSVDownloader>
          </div>

          <div className="mt-4 overflow-x-auto  bg-[#1d2937]">
            <div
              className={
                loading ? "" : "overflow-x-auto border-b border-gray-400 shadow"
              }
            >
              {loading && currentTableData.length === 0 ? (
                <TableSkeleton columns={columns} />
              ) : (
                <table className="min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]">
                  <thead className="bg-[#1d2937]">
                    <MkdListTableHead
                      actionPosition={"onTable"}
                      onSort={() => {}}
                      columns={columns}
                      actions={{ view: { show: true } }}
                    />
                  </thead>
                  <tbody className="divide-y divide-gray-400 bg-[#1d2937]">
                    {currentTableData.map((row) => (
                      <tr key={row.id}>
                        <td className="whitespace-nowrap px-6 py-4">
                          <div className="flex items-center justify-between gap-3 text-sm">
                            <button
                              className="rounded-[30px] bg-[#19b2f6]/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-[#19b2f6]/30"
                              onClick={() => {
                                setSelectedRow(row);
                                setIsViewModalOpen(true);
                              }}
                            >
                              View
                            </button>
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {row.id}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {row.to_email || "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {row.from_email || "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {STATUS_MAPPING[row.status] || "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {row.provider || "N/A"}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-white">
                          {row.create_at || "N/A"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>

          <PaginationBarV2 paginationData={paginationData} />
        </div>
      </div>

      <ViewModal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        row={selectedRow}
      />
    </>
  );
};

export default CustomUserListEmailLogsPage;
