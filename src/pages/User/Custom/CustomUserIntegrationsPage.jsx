import React, { useState, useContext } from 'react';
import { tokenExpireError, AuthContext } from 'Context/Auth';
import { GlobalContext, showToast } from 'Context/Global';

const IntegrationsPage = () => {
  const { state, dispatch } = useContext(AuthContext);
  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);

  React.useEffect(() => {
    globalDispatch({
      type: 'SETPATH',
      payload: {
        path: 'integrations',
      },
    });
  }, []);

  return (
    <div className=" mx-auto rounded  p-5 shadow-md">
      <div className="grid max-w-[700px] grid-cols-1 gap-[20px] md:grid-cols-3">
        <button className="mt-10 rounded-[3px] bg-[black]  px-10 py-3 text-white">
          Integrate Slack
        </button>

        <button className="mt-10 rounded-[3px] bg-[black]  px-10 py-3 text-white">
          Integrate Whatsapp
        </button>

        <button className="mt-10 rounded-[3px] bg-[black]  px-10 py-3 text-white">
          Integrate SMS
        </button>
      </div>
    </div>
  );
};

export default IntegrationsPage;
