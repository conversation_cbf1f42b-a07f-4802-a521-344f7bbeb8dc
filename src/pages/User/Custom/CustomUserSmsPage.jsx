import React, { useState, useContext, useRef } from "react";
import { useForm } from "react-hook-form";
import { tokenExpireError, AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { MicrophoneIcon, SpeakerWaveIcon } from "@heroicons/react/24/solid";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { Tab } from "@headlessui/react";
import { w3cwebsocket as W3CWebSocket } from "websocket";
import MoonLoader from "react-spinners/MoonLoader";

const RECORDING_STATUSES = {
  INACTIVE: "inactive",
  IN_PROGRESS: "recording",
};
const mimeType = "audio/webm";
let sdk = new MkdSDK();

const SmsPage = () => {
  const { state, dispatch } = useContext(AuthContext);
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  const [permission, setPermission] = useState(false);
  const mediaRecorder = useRef(null);
  const [recordingStatus, setRecordingStatus] = useState(
    RECORDING_STATUSES.INACTIVE
  );
  const [stream, setStream] = useState(null);
  const [code, setCode] = useState("+1");
  const [audioChunks, setAudioChunks] = useState([]);
  const [assistants, setAssistant] = useState([]);
  const [numbers, setNumbers] = useState([]);
  const [audio, setAudio] = useState(null);
  const [webSocket, setWebSocket] = useState(null);

  const getMicrophonePermission = async () => {
    if ("MediaRecorder" in window) {
      try {
        const streamData = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: false,
        });
        setPermission(true);
        setStream(streamData);
      } catch (err) {
        alert(err.message);
      }
    } else {
      alert("The MediaRecorder API is not supported in your browser.");
    }
  };

  const schema = yup.object({
    assistant_id: yup.string().required("Please select an assistant"),
    number_id: yup.string().required("Please select the inbound number"),
    to_number: yup.string().required("Please enter the destination number"),
  });

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors, isSubmitting, isValid },
    reset,
  } = useForm({
    mode: "onChange",
    resolver: yupResolver(schema),
    defaultValues: {
      assistant_id: "",
      number_id: "",
      to_number: "",
    },
  });

  const onSubmit = async (_data) => {
    let sdk = new MkdSDK();
    try {
      sdk.setTable("numbers");
      if (!_data.number_id || !_data.assistant_id) {
        throw new Error("Missing values");
      }

      await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/test_sms",
        {
          from_number: _data.number_id,
          assistant_id: _data.assistant_id,
          to_number: code + _data.to_number,
        },
        "POST"
      );

      showToast(globalDispatch, "Successful");
      reset();
    } catch (error) {
      tokenExpireError(dispatch, error.message);
      showToast(globalDispatch, error.message, 10000, "error");
      setError("assistant_id", {
        type: "manual",
        message: error.message,
      });
    }
  };

  const startRecord = async () => {
    setRecordingStatus(RECORDING_STATUSES.IN_PROGRESS);
    const media = new MediaRecorder(stream, { mimeType });
    mediaRecorder.current = media;
    mediaRecorder.current.start();

    mediaRecorder.current.ondataavailable = (event) => {
      if (typeof event.data === "undefined") return;
      if (event.data.size === 0) return;
      if (webSocket) {
        webSocket.send(event.data);
      }
    };
  };

  const stopRecording = () => {
    setRecordingStatus(RECORDING_STATUSES.INACTIVE);
    mediaRecorder.current.stop();
  };

  const startWebSocket = () => {
    const ws = new W3CWebSocket("wss://callagentds.manaknightdigital.com/1");

    ws.onopen = () => {};
    ws.onmessage = (message) => {
      const audioBlob = new Blob([message.data], { type: mimeType });
      const audioUrl = URL.createObjectURL(audioBlob);
      setAudio(audioUrl);
    };
    ws.onclose = () => {};
    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
    };
    setWebSocket(ws);
  };

  const stopWebSocket = () => {
    if (webSocket) {
      webSocket.close();
      setWebSocket(null);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "sms",
      },
    });

    (async function getNumbers() {
      sdk.setTable("numbers");
      const result = await sdk.callRestAPI({ filter: [`sms,eq,1`] }, "GETALL");
      if (!result.error) {
        setNumbers(
          result.list
            ? result.list.map((num) => {
                return {
                  number_id: num.id,
                  name: num.number,
                };
              })
            : []
        );
      }
    })();

    (async function getAssistants() {
      sdk.setTable("assistants");
      const result = await sdk.callRestAPI(
        { user_id: state.user, filter: [`user_id,eq,${state.user}`] },
        "GETALL"
      );
      if (!result.error) {
        setAssistant(
          result.list
            ? result.list.map((as) => {
                return {
                  assistant_id: as.id,
                  name: as.assistant_name,
                };
              })
            : []
        );
      }
    })();

    return () => {
      stopWebSocket();
    };
  }, []);

  return (
    <div className="mx-auto flex w-full justify-center rounded-md bg-[#1d2937] px-5 py-10 md:max-w-2xl md:px-0 lg:max-w-2xl 2xl:max-w-2xl">
      <Tab.Group
        as={"div"}
        className="w-full max-w-xl rounded-xl bg-[#1d2937] px-0 py-0 text-[#ffffff]"
      >
        <Tab.List className="flex justify-center items-center">
          <Tab
            className={({ selected }) =>
              ` rounded-lg px-3 py-1 text-2xl ${
                selected ? " text-white" : "text-white"
              } `
            }
          >
            Send an SMS
          </Tab>
          {/* <Tab
            className={({ selected }) =>
              `cursor-pointer rounded-lg px-3 py-1 ${
                selected ? "bg-[#f4f4f4] text-[#525252]" : ""
              } `
            }
          >
            Interact with Assistant
          </Tab> */}
        </Tab.List>

        <Tab.Panels>
          <Tab.Panel
            as="form"
            className="max-w-xl"
            onSubmit={handleSubmit(onSubmit)}
          >
            <div
              className={`flex flex-col justify-center items-center mt-5 max-w-xl`}
            >
              <audio
                className={`h-[fit-content] w-[100%]`}
                loop={false}
                src={"https://via.placeholder.com/50?text=%20"}
                controls={false}
                muted={false}
                autoPlay={false}
              />

              {!permission ? (
                <>
                  {/* <MicrophoneIcon
                    onClick={() => {
                      getMicrophonePermission();
                    }}
                    className={`cursor-pointer h-[50px] w-[50px]`}
                  />
                  <div className="flex items-center my-4">
                    <span className="block  h-[15px] w-[15px] rounded-full bg-[red]"></span>
                    <span className="block mx-2 font-bold">Get microphone</span>
                  </div> */}
                </>
              ) : null}
              {permission && recordingStatus === RECORDING_STATUSES.INACTIVE ? (
                <>
                  <MicrophoneIcon
                    onClick={() => {
                      startRecord();
                      startWebSocket();
                    }}
                    className={`cursor-pointer h-[50px] w-[50px]`}
                  />
                  <div className="flex items-center my-4">
                    <span className="block  h-[15px] w-[15px] rounded-full bg-[red]"></span>
                    <span className="block mx-2 font-bold">
                      Start recording
                    </span>
                  </div>
                </>
              ) : null}
              {recordingStatus === RECORDING_STATUSES.IN_PROGRESS ? (
                <>
                  <MicrophoneIcon
                    onClick={() => {
                      stopRecording();
                      stopWebSocket();
                    }}
                    className={`cursor-pointer h-[50px] w-[50px]`}
                  />
                  <div className="flex items-center my-4">
                    <span className="block h-[15px] w-[15px] animate-pulse rounded-full bg-[red]"></span>
                  </div>
                </>
              ) : null}

              {audio && recordingStatus === RECORDING_STATUSES.INACTIVE ? (
                <div className="audio-player">
                  <audio src={audio} controls></audio>
                </div>
              ) : null}
            </div>
            <div className="">
              <h3 className="mb-2 text-[13px] font-medium lg:text-[15px]">
                Assistants
              </h3>
              <select
                type={"dropdown"}
                id="assistant_id"
                {...register("assistant_id")}
                className={`focus:shadow-outline h-[30px] w-full appearance-none rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none lg:h-[38px] lg:text-base xl:h-[43px]  ${
                  errors["assistant_id"]?.message ? "border-red-500" : ""
                }`}
              >
                <option value={""}>Choose Assistant</option>
                {assistants.map((option, key) => (
                  <option value={option.assistant_id} key={key + 1}>
                    {option.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="mt-3">
              <h3 className="mb-2 text-[13px] font-medium lg:text-[15px]">
                Phone Number
              </h3>
              <select
                type={"dropdown"}
                id="number_id"
                {...register("number_id")}
                className={`focus:shadow-outline h-[30px] w-full appearance-none rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none lg:h-[38px] lg:text-base xl:h-[43px]  ${
                  errors["number_id"]?.message ? "border-red-500" : ""
                }`}
              >
                <option value={""}>Choose Phone Number</option>
                {numbers.map((option, key) => (
                  <option value={option.number_id} key={key + 1}>
                    {option.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="mt-3">
              <label className="mb-2 text-[13px] font-medium lg:text-[15px]">
                Phone to Text
              </label>
              <div className="flex mt-2">
                <select
                  id="country_code"
                  {...register("country_code")}
                  onChange={(e) => setCode(e.target.value)}
                  className={`focus:shadow-outline h-[30px] appearance-none rounded-l border bg-[#1d2937] px-3 py-1 pr-[2rem] text-[12px] leading-tight text-white shadow focus:outline-none lg:h-[38px] lg:text-base xl:h-[43px] ${
                    errors["country_code"]?.message ? "border-red-500" : ""
                  }`}
                >
                  <option value={""}>Country Code</option>
                  <option value="+1">+1 (USA)</option>
                  <option value="+1">+1 (CA)</option>
                  <option value="+44">+44 (UK)</option>
                  <option value="+234">+234 (Nigeria)</option>
                  {/* Add more country codes as needed */}
                </select>
                <input
                  type="text"
                  {...register("to_number")}
                  placeholder="Enter Phone Number"
                  className={`focus:shadow-outline h-[30px] w-full appearance-none rounded-r border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow placeholder:text-gray-300 focus:outline-none lg:h-[38px] lg:text-base xl:h-[43px] ${
                    errors["to_number"]?.message ? "border-red-500" : ""
                  }`}
                />
              </div>
            </div>
            <div className="flex justify-center items-center w-full">
              <button
                disabled={!isValid}
                className="mt-4 flex h-[43px] w-full items-center justify-center gap-2 rounded-[3px] bg-[#19b2f6]/80 px-5 py-2 text-white shadow-md shadow-black/30 hover:bg-[#19b2f6]/90 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isSubmitting ? (
                  <MoonLoader color={"white"} loading={true} size={20} />
                ) : null}
                Send Sms
              </button>
            </div>
          </Tab.Panel>
          <Tab.Panel
            as="div"
            className={
              "flex flex-col gap-4 justify-center items-center h-[435px]"
            }
          >
            <SpeakerWaveIcon className="w-12 h-12" />
            <h3>Coming soon</h3>
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
};

export default SmsPage;
