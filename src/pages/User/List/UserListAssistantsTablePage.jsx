import React, { Fragment, useRef } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { ModalSidebar } from "Components/ModalSidebar";
import { MkdListTableV3 } from "Components/MkdListTable";
import {
  UserAddAssistantsTablePage,
  UserEditAssistantTablePage,
} from "Routes/LazyLoad";
import { Dialog, Transition } from "@headlessui/react";
const filterConfig = {
  id: {
    operations: ["eq", "lt", "le", "gt", "gte"],
    type: "number",
    label: "ID",
  },
  notes: {
    operations: ["eq", "cs", "sw", "ew"],
    type: "string",
    label: "notes",
  },
  assistant_name: {
    operations: ["eq", "cs", "sw", "ew"],
    type: "string",
    label: "Assistant Name",
  },
};

const columns = [
  {
    header: "Action",
    accessor: "",
  },
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    editable: false,
  },
  {
    header: "Assistant Name",
    accessor: "assistant_name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    editable: false,
    isSearchable: true,
  },
  {
    header: "Notes",
    accessor: "notes",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    editable: false,
  },
  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 0: "inactive", 1: "active" },
    editable: false,
  },
];

const UserListAssistantsTablePage = () => {
  const { state, dispatch: authDispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const refreshRef = useRef(null);

  const [selectedItems, setSelectedItems] = React.useState([]);
  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "assistants",
      },
    });
  }, []);
  const onToggleModal = (modal, toggle, ids = []) => {
    switch (modal) {
      case "add":
        setShowAddSidebar(toggle);
        // setSelectedItems(ids);
        break;
      case "edit":
        setShowEditSidebar(toggle);
        setSelectedItems(ids);
        setActiveEditId(ids[0]);
        break;
    }
  };

  async function deleteRow(id) {
    try {
      const sdk = new MkdSDK();
      sdk.setTable("assistants");
      await sdk.callRestAPI({ id }, "DELETE");
      refreshRef.current?.click();
      showToast(globalDispatch, "Delete successful");
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      showToast(globalDispatch, err.message, 5000, "error");
    }
  }
  return (
    <div className="">
      <>
        <div className="overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5">
          <MkdListTableV3
            columns={columns}
            tableRole={"user"}
            table={"assistants"}
            defaultFilter={[`user_id,eq,${state.user}`]}
            actionId={"id"}
            actions={{
              view: { show: false, action: null, multiple: false },
              edit: {
                show: true,
                multiple: false,
                action: (ids) => {
                  setShowEditSidebar(true);
                  setActiveEditId(ids[0]);
                },
              },
              delete: { show: true, action: deleteRow, multiple: false },
              select: { show: false, action: null, multiple: false },
              add: {
                show: true,
                action: () => onToggleModal("add", true),
                multiple: false,
                children: "Add New",
                showChildren: true,
              },
              export: { show: false, action: null, multiple: true },
            }}
            actionPosition={`onTable`}
            refreshRef={refreshRef}
            filterConfig={filterConfig}
          />
        </div>
      </>
      <Transition appear show={showAddSidebar} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-[100]"
          onClose={() => setShowAddSidebar(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-transparent/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-x-full"
                enterTo="opacity-100 translate-x-0"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-x-0"
                leaveTo="opacity-0 translate-x-full"
              >
                <Dialog.Panel className=" h-[95vh] w-full max-w-3xl transform overflow-y-auto  bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                  <UserAddAssistantsTablePage
                    closeSidebar={() => {
                      setShowAddSidebar(false);
                      refreshRef.current?.click();
                    }}
                  />
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition
        appear={false}
        show={showEditSidebar && !!activeEditId}
        as={Fragment}
      >
        <Dialog
          as="div"
          className="relative z-[100]"
          onClose={() => setShowEditSidebar(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-transparent/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-x-0"
                enterTo="opacity-100 translate-x-0"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-x-0"
                leaveTo="opacity-0 translate-x-full"
              >
                <Dialog.Panel className="h-[95vh] w-full max-w-3xl transform overflow-y-auto bg-[#1d2937]  p-6 text-left align-middle shadow-xl transition-all">
                  <UserEditAssistantTablePage
                    activeId={activeEditId}
                    closeSidebar={() => {
                      setShowEditSidebar(false);
                      refreshRef.current?.click();
                    }}
                  />
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default UserListAssistantsTablePage;
