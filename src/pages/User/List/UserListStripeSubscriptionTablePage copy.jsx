import React from "react";
import { GlobalContext } from "Context/Global";
import { CheckIcon } from "@heroicons/react/24/outline";

const StripeSubscriptionListPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "subscription",
      },
    });
  }, []);
  return (
    <> 
      <>
        <div className="overflow-x-auto  rounded bg-white px-10 shadow">
          <div className="container mx-auto mt-24 grid gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            <div className="relative flex flex-col rounded-2xl border border-gray-200 p-8 shadow-sm">
              <div>
                <p className="mt-4 flex items-baseline ">
                  <span className="text-5xl font-extrabold tracking-tight">
                    $25
                  </span>
                  <span className="ml-1 text-xl font-semibold">/month</span>
                </p>
                <p className="mt-6 ">
                  You want to learn and have a voice outreach
                </p>
                <ul role="list" className="mt-6 space-y-6">
                  <li className="flex">
                    <CheckIcon className="h-5 w-5 text-green-500" />
                    <span className="ml-3 ">30 credits</span>
                  </li>
                  <li className="flex">
                    <CheckIcon className="h-5 w-5 text-green-500" />
                    <span className="ml-3 ">
                      Powered by GPT-4 (more accurate)
                    </span>
                  </li>
                  <li className="flex">
                    <CheckIcon className="h-5 w-5 text-green-500" />
                    <span className="ml-3 ">Generate video (2 credits)</span>
                  </li>
                  <li className="flex">
                    <CheckIcon className="h-5 w-5 text-green-500" />
                    <span className="ml-3 ">Quiz (1 credits) </span>
                  </li>
                  <li className="flex">
                    <CheckIcon className="h-5 w-5 text-green-500" />
                    <span className="ml-3 ">Analytics on the quiz</span>
                  </li>
                </ul>
              </div>
              <span className="mt-8 block  w-full cursor-pointer rounded-md border border-transparent bg-[black] px-6 py-3 text-center font-medium text-white hover:bg-[blue]">
                SUBSCRIBE
              </span>
            </div>

          </div>
        </div>
      </>
    </>
  );
};

export default StripeSubscriptionListPage;
