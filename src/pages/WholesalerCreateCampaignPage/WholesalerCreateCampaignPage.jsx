import React, { useState, useEffect, useContext } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import { ScheduleWindowsEditor } from "Components/ScheduleWindowsEditor";
import { RetrySettingsEditor } from "Components/RetrySettingsEditor";
import { RingbaConfigEditor } from "Components/RingbaConfigEditor";

const sdk = new MkdSDK();

const WholesalerCreateCampaignPage = () => {
  const { dispatch } = useContext(GlobalContext);
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    status: "active",
    settings: {
      call_settings: {
        max_call_duration: 300,
        call_timeout: 30,
        enable_voicemail: true,
        voicemail_message: "",
      },
      schedule_windows: [
        {
          days: ["monday", "tuesday", "wednesday", "thursday", "friday"],
          start_time: "09:00",
          end_time: "17:00",
          timezone: "America/New_York",
        },
      ],
      retry_settings: {
        max_attempts: 3,
        retry_intervals: [60, 300, 1800],
        retry_on_busy: true,
        retry_on_no_answer: true,
        retry_on_failed: true,
      },
      lead_routing: {
        distribution_method: "round_robin",
        priority_rules: [],
        fallback_number: "",
      },
      integrations: {
        ringba: {
          enabled: false,
          campaign_id: "",
          api_key: "",
          webhook_url: "",
        },
      },
    },
  });

  useEffect(() => {
    if (isEdit) {
      loadCampaign();
    }
  }, [id, isEdit]);

  const loadCampaign = async () => {
    try {
      setLoading(true);
      const result = await sdk.leadCampaignAPI.get(id);
      setFormData(result);
    } catch (error) {
      console.error("Error loading campaign:", error);
      showToast(dispatch, "Error loading campaign", 5000, "error");
      navigate("/wholesaler/lead-campaigns");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      
      if (isEdit) {
        await sdk.leadCampaignAPI.update(id, formData);
        showToast(dispatch, "Campaign updated successfully");
      } else {
        await sdk.leadCampaignAPI.create(formData);
        showToast(dispatch, "Campaign created successfully");
      }
      
      navigate("/wholesaler/lead-campaigns");
    } catch (error) {
      console.error("Error saving campaign:", error);
      showToast(dispatch, "Error saving campaign", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSettingsChange = (section, value) => {
    setFormData(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        [section]: value,
      },
    }));
  };

  if (loading && isEdit) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          {isEdit ? "Edit Campaign" : "Create New Campaign"}
        </h1>
        <p className="text-gray-600 mt-1">
          {isEdit ? "Update your campaign settings" : "Set up a new lead generation campaign"}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange("status", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="active">Active</option>
                <option value="paused">Paused</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Call Settings */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Call Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Call Duration (seconds)
              </label>
              <input
                type="number"
                value={formData.settings.call_settings.max_call_duration}
                onChange={(e) => handleSettingsChange("call_settings", {
                  ...formData.settings.call_settings,
                  max_call_duration: parseInt(e.target.value),
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Call Timeout (seconds)
              </label>
              <input
                type="number"
                value={formData.settings.call_settings.call_timeout}
                onChange={(e) => handleSettingsChange("call_settings", {
                  ...formData.settings.call_settings,
                  call_timeout: parseInt(e.target.value),
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.settings.call_settings.enable_voicemail}
                onChange={(e) => handleSettingsChange("call_settings", {
                  ...formData.settings.call_settings,
                  enable_voicemail: e.target.checked,
                })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Enable Voicemail</span>
            </label>
          </div>
          {formData.settings.call_settings.enable_voicemail && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Voicemail Message
              </label>
              <textarea
                value={formData.settings.call_settings.voicemail_message}
                onChange={(e) => handleSettingsChange("call_settings", {
                  ...formData.settings.call_settings,
                  voicemail_message: e.target.value,
                })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your voicemail message..."
              />
            </div>
          )}
        </div>

        {/* Schedule Windows */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Schedule Windows</h2>
          <ScheduleWindowsEditor
            value={formData.settings.schedule_windows}
            onChange={(value) => handleSettingsChange("schedule_windows", value)}
          />
        </div>

        {/* Retry Settings */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Retry Settings</h2>
          <RetrySettingsEditor
            value={formData.settings.retry_settings}
            onChange={(value) => handleSettingsChange("retry_settings", value)}
          />
        </div>

        {/* Ringba Integration */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Ringba Integration</h2>
          <RingbaConfigEditor
            value={formData.settings.integrations.ringba}
            onChange={(value) => handleSettingsChange("integrations", {
              ...formData.settings.integrations,
              ringba: value,
            })}
          />
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-end gap-4">
          <button
            type="button"
            onClick={() => navigate("/wholesaler/lead-campaigns")}
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? "Saving..." : isEdit ? "Update Campaign" : "Create Campaign"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default WholesalerCreateCampaignPage;
