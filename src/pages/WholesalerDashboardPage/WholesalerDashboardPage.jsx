import React, { useState, useEffect, useContext } from "react";
import { Link } from "react-router-dom";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import {
  MdCampaign,
  MdAnalytics,
  MdKey,
  MdTrendingUp,
  MdPhone,
  MdAttachMoney,
  MdAdd,
} from "react-icons/md";

const sdk = new MkdSDK();

const WholesalerDashboardPage = () => {
  const { dispatch } = useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCampaigns: 0,
    activeCampaigns: 0,
    totalLeads: 0,
    totalCalls: 0,
    totalCost: 0,
    conversionRate: 0,
  });
  const [recentCampaigns, setRecentCampaigns] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load campaigns
      const campaignsResult = await sdk.leadCampaignAPI.list({ limit: 5 });
      setRecentCampaigns(campaignsResult.list || []);
      
      // Calculate stats from campaigns
      const totalCampaigns = campaignsResult.total || 0;
      const activeCampaigns = campaignsResult.list?.filter(c => c.status === 'active').length || 0;
      
      // Load analytics for overall stats
      const analyticsResult = await sdk.leadCampaignAPI.analytics('all', {
        period: '30d'
      });
      
      setStats({
        totalCampaigns,
        activeCampaigns,
        totalLeads: analyticsResult.totalLeads || 0,
        totalCalls: analyticsResult.totalCalls || 0,
        totalCost: analyticsResult.totalCost || 0,
        conversionRate: analyticsResult.conversionRate || 0,
      });
      
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      showToast(dispatch, "Error loading dashboard data", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ title, value, icon, color, link }) => (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>{value}</p>
        </div>
        <div className={`p-3 rounded-full ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
          {icon}
        </div>
      </div>
      {link && (
        <Link to={link} className="text-blue-600 text-sm hover:underline mt-2 inline-block">
          View Details →
        </Link>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome to your wholesaler dashboard</p>
        </div>
        <Link
          to="/wholesaler/lead-campaigns/create"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <MdAdd className="text-lg" />
          Create Campaign
        </Link>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard
          title="Total Campaigns"
          value={stats.totalCampaigns}
          icon={<MdCampaign className="text-xl text-blue-600" />}
          color="text-blue-600"
          link="/wholesaler/lead-campaigns"
        />
        <StatCard
          title="Active Campaigns"
          value={stats.activeCampaigns}
          icon={<MdTrendingUp className="text-xl text-green-600" />}
          color="text-green-600"
          link="/wholesaler/lead-campaigns?status=active"
        />
        <StatCard
          title="Total Leads"
          value={stats.totalLeads.toLocaleString()}
          icon={<MdAnalytics className="text-xl text-purple-600" />}
          color="text-purple-600"
          link="/wholesaler/analytics"
        />
        <StatCard
          title="Total Calls"
          value={stats.totalCalls.toLocaleString()}
          icon={<MdPhone className="text-xl text-orange-600" />}
          color="text-orange-600"
          link="/wholesaler/analytics"
        />
        <StatCard
          title="Total Cost"
          value={`$${stats.totalCost.toFixed(2)}`}
          icon={<MdAttachMoney className="text-xl text-red-600" />}
          color="text-red-600"
          link="/wholesaler/cost-tracking"
        />
        <StatCard
          title="Conversion Rate"
          value={`${stats.conversionRate.toFixed(1)}%`}
          icon={<MdTrendingUp className="text-xl text-indigo-600" />}
          color="text-indigo-600"
          link="/wholesaler/analytics"
        />
      </div>

      {/* Recent Campaigns */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">Recent Campaigns</h2>
            <Link
              to="/wholesaler/lead-campaigns"
              className="text-blue-600 hover:underline text-sm"
            >
              View All →
            </Link>
          </div>
        </div>
        <div className="p-6">
          {recentCampaigns.length === 0 ? (
            <div className="text-center py-8">
              <MdCampaign className="text-4xl text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No campaigns yet</p>
              <Link
                to="/wholesaler/lead-campaigns/create"
                className="text-blue-600 hover:underline mt-2 inline-block"
              >
                Create your first campaign
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {recentCampaigns.map((campaign) => (
                <div
                  key={campaign.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <div>
                    <h3 className="font-medium text-gray-900">{campaign.name}</h3>
                    <p className="text-sm text-gray-600">
                      Created: {new Date(campaign.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center gap-4">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        campaign.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : campaign.status === 'paused'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {campaign.status}
                    </span>
                    <Link
                      to={`/wholesaler/lead-campaigns/${campaign.id}`}
                      className="text-blue-600 hover:underline text-sm"
                    >
                      View →
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            to="/wholesaler/lead-campaigns/create"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <MdCampaign className="text-2xl text-blue-600 mb-2" />
            <h3 className="font-medium text-gray-900">Create Campaign</h3>
            <p className="text-sm text-gray-600">Set up a new lead campaign</p>
          </Link>
          <Link
            to="/wholesaler/api-keys"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <MdKey className="text-2xl text-green-600 mb-2" />
            <h3 className="font-medium text-gray-900">Manage API Keys</h3>
            <p className="text-sm text-gray-600">Generate and manage API keys</p>
          </Link>
          <Link
            to="/wholesaler/analytics"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <MdAnalytics className="text-2xl text-purple-600 mb-2" />
            <h3 className="font-medium text-gray-900">View Analytics</h3>
            <p className="text-sm text-gray-600">Analyze campaign performance</p>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default WholesalerDashboardPage;
