import React, { useState, useEffect, useContext } from "react";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import {
  MdTrendingUp,
  MdTrendingDown,
  MdPhone,
  MdPeople,
  MdAttachMoney,
  MdDownload,
  MdDateRange,
} from "react-icons/md";
import Chart from "react-apexcharts";

const sdk = new MkdSDK();

const WholesalerAnalyticsPage = () => {
  const { dispatch } = useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState("30d");
  const [selectedCampaign, setSelectedCampaign] = useState("all");
  const [campaigns, setCampaigns] = useState([]);
  const [analytics, setAnalytics] = useState({
    overview: {
      totalLeads: 0,
      totalCalls: 0,
      totalCost: 0,
      conversionRate: 0,
      avgCallDuration: 0,
      successfulCalls: 0,
    },
    trends: {
      labels: [],
      leads: [],
      calls: [],
      costs: [],
    },
    sources: [],
    callOutcomes: {
      successful: 0,
      busy: 0,
      no_answer: 0,
      failed: 0,
    },
  });

  useEffect(() => {
    loadData();
  }, [dateRange, selectedCampaign]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load campaigns
      const campaignsResult = await sdk.leadCampaignAPI.list({ limit: 100 });
      setCampaigns(campaignsResult.list || []);
      
      // Load analytics
      const analyticsResult = await sdk.leadCampaignAPI.analytics(selectedCampaign, {
        period: dateRange,
      });
      
      setAnalytics(analyticsResult);
      
    } catch (error) {
      console.error("Error loading analytics:", error);
      showToast(dispatch, "Error loading analytics", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const exportData = async () => {
    try {
      await sdk.leadCampaignAPI.export(selectedCampaign, {
        period: dateRange,
        format: 'csv',
      });
      showToast(dispatch, "Export started - download will begin shortly");
    } catch (error) {
      console.error("Error exporting data:", error);
      showToast(dispatch, "Error exporting data", 5000, "error");
    }
  };

  const MetricCard = ({ title, value, change, icon, color, format = "number" }) => {
    const formatValue = (val) => {
      if (format === "currency") return `$${val.toFixed(2)}`;
      if (format === "percentage") return `${val.toFixed(1)}%`;
      if (format === "duration") return `${Math.floor(val / 60)}:${(val % 60).toString().padStart(2, '0')}`;
      return val.toLocaleString();
    };

    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className={`text-2xl font-bold ${color}`}>{formatValue(value)}</p>
            {change !== undefined && (
              <div className="flex items-center mt-1">
                {change >= 0 ? (
                  <MdTrendingUp className="text-green-500 text-sm mr-1" />
                ) : (
                  <MdTrendingDown className="text-red-500 text-sm mr-1" />
                )}
                <span className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(change).toFixed(1)}%
                </span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-full ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
            {icon}
          </div>
        </div>
      </div>
    );
  };

  const lineChartOptions = {
    chart: {
      type: 'line',
      height: 350,
      toolbar: {
        show: false,
      },
    },
    title: {
      text: 'Performance Trends',
      align: 'left',
    },
    xaxis: {
      categories: analytics.trends.labels,
    },
    yaxis: {
      title: {
        text: 'Count',
      },
    },
    stroke: {
      curve: 'smooth',
      width: 2,
    },
    colors: ['#3B82F6', '#10B981'],
    legend: {
      position: 'top',
    },
  };

  const lineChartSeries = [
    {
      name: 'Leads',
      data: analytics.trends.leads,
    },
    {
      name: 'Calls',
      data: analytics.trends.calls,
    },
  ];

  const doughnutOptions = {
    chart: {
      type: 'donut',
      height: 350,
    },
    labels: ['Successful', 'Busy', 'No Answer', 'Failed'],
    colors: ['#10B981', '#F59E0B', '#6B7280', '#EF4444'],
    legend: {
      position: 'bottom',
    },
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
        },
      },
    },
  };

  const doughnutSeries = [
    analytics.callOutcomes.successful,
    analytics.callOutcomes.busy,
    analytics.callOutcomes.no_answer,
    analytics.callOutcomes.failed,
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics & Reports</h1>
          <p className="text-gray-600 mt-1">Track performance and analyze campaign data</p>
        </div>
        <button
          onClick={exportData}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
        >
          <MdDownload className="text-lg" />
          Export Data
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Campaign
            </label>
            <select
              value={selectedCampaign}
              onChange={(e) => setSelectedCampaign(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Campaigns</option>
              {campaigns.map((campaign) => (
                <option key={campaign.id} value={campaign.id}>
                  {campaign.name}
                </option>
              ))}
            </select>
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Range
            </label>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
          </div>
        </div>
      </div>

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <MetricCard
          title="Total Leads"
          value={analytics.overview.totalLeads}
          icon={<MdPeople className="text-xl text-blue-600" />}
          color="text-blue-600"
        />
        <MetricCard
          title="Total Calls"
          value={analytics.overview.totalCalls}
          icon={<MdPhone className="text-xl text-green-600" />}
          color="text-green-600"
        />
        <MetricCard
          title="Total Cost"
          value={analytics.overview.totalCost}
          icon={<MdAttachMoney className="text-xl text-red-600" />}
          color="text-red-600"
          format="currency"
        />
        <MetricCard
          title="Conversion Rate"
          value={analytics.overview.conversionRate}
          icon={<MdTrendingUp className="text-xl text-purple-600" />}
          color="text-purple-600"
          format="percentage"
        />
        <MetricCard
          title="Successful Calls"
          value={analytics.overview.successfulCalls}
          icon={<MdPhone className="text-xl text-indigo-600" />}
          color="text-indigo-600"
        />
        <MetricCard
          title="Avg Call Duration"
          value={analytics.overview.avgCallDuration}
          icon={<MdDateRange className="text-xl text-orange-600" />}
          color="text-orange-600"
          format="duration"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trends Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Performance Trends</h2>
          <div className="h-80">
            <Chart
              options={lineChartOptions}
              series={lineChartSeries}
              type="line"
              height={350}
            />
          </div>
        </div>

        {/* Call Outcomes */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Call Outcomes</h2>
          <div className="h-80 flex items-center justify-center">
            <Chart
              options={doughnutOptions}
              series={doughnutSeries}
              type="donut"
              height={350}
            />
          </div>
        </div>
      </div>

      {/* Source Performance */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Source Performance</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Source
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Leads
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Calls
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Conversion Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cost
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cost per Lead
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analytics.sources.map((source, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {source.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {source.leads.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {source.calls.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {source.conversionRate.toFixed(1)}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${source.cost.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${source.costPerLead.toFixed(2)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default WholesalerAnalyticsPage;
