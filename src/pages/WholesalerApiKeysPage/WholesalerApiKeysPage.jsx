import React, { useState, useEffect, useContext } from "react";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import {
  MdAdd,
  MdDelete,
  MdContentCopy,
  MdVisibility,
  MdVisibilityOff,
  Md<PERSON><PERSON>,
  Md<PERSON><PERSON>ning,
} from "react-icons/md";

const sdk = new MkdSDK();

const WholesalerApiKeysPage = () => {
  const { dispatch } = useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [apiKeys, setApiKeys] = useState([]);
  const [campaigns, setCampaigns] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [visibleKeys, setVisibleKeys] = useState(new Set());
  const [newKey, setNewKey] = useState({
    campaign_id: "",
    key_name: "",
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [keysResult, campaignsResult] = await Promise.all([
        sdk.apiKeyAPI.list(),
        sdk.leadCampaignAPI.list({ limit: 100 }),
      ]);
      setApiKeys(keysResult.list || []);
      setCampaigns(campaignsResult.list || []);
    } catch (error) {
      console.error("Error loading data:", error);
      showToast(dispatch, "Error loading API keys", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateKey = async (e) => {
    e.preventDefault();
    
    try {
      const result = await sdk.apiKeyAPI.generate(newKey.campaign_id, newKey.key_name);
      showToast(dispatch, "API key generated successfully");
      setShowCreateModal(false);
      setNewKey({ campaign_id: "", key_name: "" });
      loadData();
      
      // Show the new key temporarily
      if (result.api_key) {
        setVisibleKeys(new Set([result.id]));
        setTimeout(() => {
          setVisibleKeys(new Set());
        }, 30000); // Hide after 30 seconds
      }
    } catch (error) {
      console.error("Error creating API key:", error);
      showToast(dispatch, "Error creating API key", 5000, "error");
    }
  };

  const handleRevokeKey = async (keyId) => {
    if (!window.confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
      return;
    }
    
    try {
      await sdk.apiKeyAPI.revoke(keyId);
      showToast(dispatch, "API key revoked successfully");
      loadData();
    } catch (error) {
      console.error("Error revoking API key:", error);
      showToast(dispatch, "Error revoking API key", 5000, "error");
    }
  };

  const copyToClipboard = async (text) => {
    try {
      await sdk.copyToClipboard(text);
      showToast(dispatch, "Copied to clipboard");
    } catch (error) {
      showToast(dispatch, "Failed to copy to clipboard", 5000, "error");
    }
  };

  const toggleKeyVisibility = (keyId) => {
    const newVisible = new Set(visibleKeys);
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId);
    } else {
      newVisible.add(keyId);
    }
    setVisibleKeys(newVisible);
  };

  const maskKey = (key) => {
    if (!key) return "";
    return key.substring(0, 8) + "..." + key.substring(key.length - 8);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">API Keys</h1>
          <p className="text-gray-600 mt-1">Manage API keys for lead ingestion</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <MdAdd className="text-lg" />
          Generate API Key
        </button>
      </div>

      {/* Warning Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <MdWarning className="text-yellow-600 text-xl mt-0.5" />
          <div>
            <h3 className="text-yellow-800 font-medium">Important Security Notice</h3>
            <p className="text-yellow-700 text-sm mt-1">
              API keys provide access to submit leads to your campaigns. Keep them secure and never share them publicly.
              You can only view the full API key immediately after generation.
            </p>
          </div>
        </div>
      </div>

      {/* API Keys List */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {apiKeys.length === 0 ? (
          <div className="text-center py-12">
            <MdKey className="text-4xl text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">No API keys generated yet</p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
            >
              <MdAdd className="text-lg" />
              Generate Your First API Key
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Key Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Campaign
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    API Key
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {apiKeys.map((key) => {
                  const campaign = campaigns.find(c => c.id === key.campaign_id);
                  const isVisible = visibleKeys.has(key.id);
                  
                  return (
                    <tr key={key.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {key.key_name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {campaign ? campaign.name : "Unknown Campaign"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <code className="text-sm bg-gray-100 px-2 py-1 rounded font-mono">
                            {isVisible ? key.api_key : maskKey(key.api_key)}
                          </code>
                          <button
                            onClick={() => toggleKeyVisibility(key.id)}
                            className="text-gray-500 hover:text-gray-700"
                            title={isVisible ? "Hide key" : "Show key"}
                          >
                            {isVisible ? <MdVisibilityOff /> : <MdVisibility />}
                          </button>
                          {isVisible && (
                            <button
                              onClick={() => copyToClipboard(key.api_key)}
                              className="text-blue-600 hover:text-blue-800"
                              title="Copy to clipboard"
                            >
                              <MdContentCopy />
                            </button>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            key.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {key.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(key.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleRevokeKey(key.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Revoke key"
                        >
                          <MdDelete className="text-lg" />
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Create API Key Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Generate New API Key
            </h2>
            <form onSubmit={handleCreateKey} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Key Name *
                </label>
                <input
                  type="text"
                  value={newKey.key_name}
                  onChange={(e) => setNewKey({ ...newKey, key_name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Production API Key"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Campaign *
                </label>
                <select
                  value={newKey.campaign_id}
                  onChange={(e) => setNewKey({ ...newKey, campaign_id: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Select a campaign</option>
                  {campaigns.map((campaign) => (
                    <option key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex justify-end gap-4 mt-6">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Generate Key
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Usage Instructions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">API Usage</h2>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Submit Leads</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <code className="text-sm">
                POST /v3/api/custom/voiceoutreach/leads/webhook/leads<br />
                Headers: x-api-key: YOUR_API_KEY<br />
                Content-Type: application/json
              </code>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Example Payload</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <pre className="text-sm text-gray-800">
{`{
  "leads": [
    {
      "phone": "+1234567890",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "source": "website",
      "custom_fields": {
        "property_type": "single_family",
        "budget": "500000"
      }
    }
  ]
}`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WholesalerApiKeysPage;
