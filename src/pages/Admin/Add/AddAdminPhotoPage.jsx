
  import React, { useState } from "react";
import Uppy from "@uppy/core";
import XHRUpload from "@uppy/xhr-upload";
import { Dashboard, useUppy } from "@uppy/react";
import "@uppy/core/dist/style.css";
import "@uppy/drag-drop/dist/style.css";

import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { useNavigate } from "react-router";
let sdk = new MkdSDK();

const AddAdminPhotoPage = ({ setSidebar }) => {
  const { dispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const uppy = useUppy(() => {
    let model = new Uppy();
    model.use(XHRUpload, {
      id: "XHRUpload",
      method: "post",
      formData: true,
      limit: 0,
      fieldName: "file",
      allowedMetaFields: ["caption", "size"],
      headers: sdk.getHeader(),
      endpoint: sdk.uploadUrl(),
    });

    model.on("file-added", (file) => {
      model.setFileMeta(file.id, {
        size: file.size,
        caption: "",
      });
    });

    model.on("upload-success", async (file, response) => {
      const httpStatus = response.status; // HTTP status code
      const responseBody = response.body;
      console.log("response", response);
      showToast(globalDispatch, "Uploaded");
      navigate("/admin/photos");
    });

    model.on("upload-error", (file, error, response) => {
      const httpStatus = response.status; // HTTP status code
      if (httpStatus == 401) {
        tokenExpireError(dispatch, "TOKEN_EXPIRED");
      }
    });
    return model;
  });

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "photos",
      },
    });
  }, []);

  return (
    <div className="relative p-4 flex-auto">
      <div className={`flex items-center pb-3 gap-4 border-b border-b-[#E0E0E0] justify-between`}>
        <div className="flex items-center gap-3">
          <svg onClick={() => setSidebar(false)} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218" stroke="#A8A8A8" stroke-width="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          <span className="text-lg font-semibold">Add Photo</span>
        </div>
        <div className="flex items-center gap-4">
          <button className="flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]" onClick={() => setSidebar(false)}>Cancel</button>
        </div>
      </div>
      {/* <h4 className="text-2xl font-medium mb-2">Add Photo</h4> */}
      <Dashboard uppy={uppy} />
    </div>
  );
};

export default AddAdminPhotoPage;

  