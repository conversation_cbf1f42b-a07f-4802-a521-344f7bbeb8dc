import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Context/Global";
import { tokenExpireError } from "Context/Auth";

const AddCustomerPage = ({ setSidebar, closeSidebar }) => {
  const schema = yup
    .object({
      firstName: yup.string().required(),
      lastName: yup.string().required(),
      email: yup.string().email().required(),
      password: yup.string().required(),
      status: yup.string().required(),
      telephonicService: yup.string().required(),
      speechRecognition: yup.string().required(),
      textToSpeech: yup.string().required(),
      llm: yup.string().required(),
    })
    .required();

  const { dispatch } = React.useContext(GlobalContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isUpdating, setIsUpdating] = React.useState(false);
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const statusOptions = [
    { name: "Active", value: "active" },
    { name: "Inactive", value: "inactive" },
  ];

  const telephonicServiceOptions = [
    { name: "Twilio", value: "twilio" },
    { name: "Telynx", value: "telynx" },
  ];

  const speechRecognitionOptions = [{ name: "Deepgram", value: "deepgram" }];

  const textToSpeechOptions = [
    { name: "Eleven Labs", value: "elevenlabs" },
    { name: "Azure Speech SDK", value: "azure" },
  ];

  const llmOptions = [
    { name: "Claude Haiku", value: "claude-3-haiku-20240307" },
    { name: "GPT-3.5", value: "ai_energy-3-turbo" },
  ];

  const onSubmit = async (data) => {
    let sdk = new MkdSDK();
    setIsUpdating(true);
    try {
      const result = await sdk.register(
        data.email,
        data.password,
        "user",
        data.firstName,
        data.lastName
      );

      if (!result.error) {
        const transformedData = {
          phone_service: data.telephonicService,
          tts_settings: JSON.stringify({
            provider: data.textToSpeech,
            voice_id: "qzsSemAullBuJxvBTx9G",
            speed: "normal",
            output_format: "ulaw_8000",
          }),
          llm_settings: JSON.stringify({
            provider: data.llm.includes("claude") ? "anthropic" : "openai",
            temperature: "0.01",
            max_tokens: "128",
            top_p: "0.9",
            model_name: data.llm,
            system_prompt: `You are a customer care agent for an insurance company, making outbound calls. Follow the decision tree provided below and decide which response best suits what the lead says. Always stick to the script, picking the best response at all times to keep the conversation going. You are Alice, and the user will respond as the lead. I will respond as the lead; just provide responses and decide which is next in the tree from the responses I give. Start with the introduction. Don\'t say the headers like introduction or any other ones in between **. Just say what is under them for the right situation. Don\'t include numbers, rather spell it. If you use any acronym, also spell it out.\\n\\n---\\n\\nHi, this is Alice from ABC. Do you have a minute to discuss how you can save on your insurance?\\n\\n---\\n\\nI’d love to tell you about our latest promotion. Are you interested in saving on auto, home, or life insurance?\\n\\n---\\n\\nCould you provide some basic details like your age, and location?\\n\\n---\\n\\nBased on your information, you could save up to [specific amount] annually on [insurance type]. Would you like to hear more about these savings or perhaps get a detailed quote?\\n\\n---\\n\\nGreat! I’ll need a few more details to prepare a personalized quote for you. Could you tell me more about your current coverage?\\n\\n---\\n\\nOur plans offer great coverage options tailored to your needs, with significant savings. Would you like me to walk you through some of the benefits and coverage options?\\n\\n---\\n\\nIs there anything else I can help you with today?\\n\\n---\\n\\nThank you for your time. We appreciate your interest in our services and look forward to helping you save on your insurance.\\n\\n---\\n\\nAlright, everything is all set! We\'ll be in touch soon. We are looking forward to helping you save on your insurance!`,
            first_message:
              "Hello, Stacy! I'm Alice from ABC Insurance company. I can help you save on the insurance, do you have a minute?",
          }),
          telephony_settings: JSON.stringify({
            provider: data.telephonicService,
          }),
          speech_recognition_settings: JSON.stringify({
            provider: data.speechRecognition,
            model: "nova-2-phonecall",
            encoding: "mulaw",
            sample_rate: 8000,
          }),
          stt_service: data.speechRecognition,
          llm_service: data.llm,
          user_id: result.user_id,
        };

        sdk.setTable("user_settings");
        const settings = await sdk.callRestAPI(transformedData, "POST");
        showToast(dispatch, "Added");
        // navigate("/admin/customers");
        if (closeSidebar) {
          closeSidebar();
        }
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("email", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
    setIsUpdating(false);
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "users",
      },
    });
  }, []);
  return (
    <div className="mx-auto rounded">
      <div
        className={`flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3`}
      >
        <div className="flex gap-3 items-center">
          <svg
            onClick={() => setSidebar(false)}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218"
              stroke="#A8A8A8"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-lg font-semibold">Add Customer</span>
        </div>
        <div className="flex gap-4 items-center">
          <button
            className="flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]"
            onClick={() => setSidebar(false)}
          >
            Cancel
          </button>
          <button
            className="flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm"
            onClick={async () => {
              await handleSubmit(onSubmit)();
              setSidebar(false);
            }}
            disabled={isUpdating}
          >
            {isUpdating ? "Saving" : "Save"}
          </button>
        </div>
      </div>
      <form
        className="p-4 w-full max-w-lg text-left"
        style={{ overflowY: "auto" }}
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="mb-4">
          <label
            className="block mb-2 text-sm font-bold text-gray-700"
            htmlFor="firstName"
          >
            First Name
          </label>
          <input
            type="text"
            placeholder="First Name"
            {...register("firstName")}
            className={`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
              errors.firstName?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-xs italic text-red-500">
            {errors.firstName?.message}
          </p>
        </div>
        <div className="mb-4">
          <label
            className="block mb-2 text-sm font-bold text-gray-700"
            htmlFor="lastName"
          >
            Last Name
          </label>
          <input
            type="text"
            placeholder="Last Name"
            {...register("lastName")}
            className={`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
              errors.lastName?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-xs italic text-red-500">
            {errors.lastName?.message}
          </p>
        </div>
        <div className="mb-4">
          <label
            className="block mb-2 text-sm font-bold text-gray-700"
            htmlFor="email"
          >
            Email
          </label>
          <input
            type="email"
            placeholder="Email"
            {...register("email")}
            className={`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
              errors.email?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-xs italic text-red-500">{errors.email?.message}</p>
        </div>
        <div className="mb-4">
          <label
            className="block mb-2 text-sm font-bold text-gray-700"
            htmlFor="password"
          >
            Password
          </label>
          <input
            type="password"
            placeholder="Password"
            {...register("password")}
            className={`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
              errors.password?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-xs italic text-red-500">
            {errors.password?.message}
          </p>
        </div>
        <div className="mb-5">
          <label className="block mb-2 text-sm font-bold text-gray-700">
            Status
          </label>
          <select
            name="status"
            className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none"
            {...register("status")}
          >
            {statusOptions.map((option) => (
              <option
                name={option.name}
                value={option.value}
                key={option.value}
              >
                {option.name}
              </option>
            ))}
          </select>
        </div>
        <div className="mb-5">
          <label className="block mb-2 text-sm font-bold text-gray-700">
            Telephone Service
          </label>
          <select
            name="telephonicService"
            className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none"
            {...register("telephonicService")}
          >
            {telephonicServiceOptions.map((option) => (
              <option
                name={option.name}
                value={option.value}
                key={option.value}
              >
                {option.name}
              </option>
            ))}
          </select>
        </div>
        <div className="mb-5">
          <label className="block mb-2 text-sm font-bold text-gray-700">
            Speech Recognition
          </label>
          <select
            name="speechRecognition"
            className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none"
            {...register("speechRecognition")}
          >
            {speechRecognitionOptions.map((option) => (
              <option
                name={option.name}
                value={option.value}
                key={option.value}
              >
                {option.name}
              </option>
            ))}
          </select>
        </div>
        <div className="mb-5">
          <label className="block mb-2 text-sm font-bold text-gray-700">
            Text-to-Speech
          </label>
          <select
            name="textToSpeech"
            className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none"
            {...register("textToSpeech")}
          >
            {textToSpeechOptions.map((option) => (
              <option
                name={option.name}
                value={option.value}
                key={option.value}
              >
                {option.name}
              </option>
            ))}
          </select>
        </div>
        <div className="mb-5">
          <label className="block mb-2 text-sm font-bold text-gray-700">
            LLM
          </label>
          <select
            name="llm"
            className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none"
            {...register("llm")}
          >
            {llmOptions.map((option) => (
              <option
                name={option.name}
                value={option.value}
                key={option.value}
              >
                {option.name}
              </option>
            ))}
          </select>
        </div>
      </form>
    </div>
  );
};

export default AddCustomerPage;
