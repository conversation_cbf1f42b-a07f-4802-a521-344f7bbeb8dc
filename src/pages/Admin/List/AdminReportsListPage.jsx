import React from "react";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { getNonNullValue } from "Utils/utils";
import { PaginationBar } from "Components/PaginationBar";
import { AddButton } from "Components/AddButton";
import { SkeletonLoader } from "Components/Skeleton";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import { ModalSidebar } from "Components/ModalSidebar";
import { AddAdminUserPage, EditAdminUserPage } from "Src/routes/LazyLoad";
import { XIcon } from "lucide-react";

let sdk = new MkdSDK();

const columns = [
  {
    header: "First Name",
    accessor: "first_name",
  },
  {
    header: "Email",
    accessor: "email",
  },
  {
    header: "Bugs",
    accessor: "query",
  },
];

const AdminReportsListPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [query, setQuery] = React.useState("");
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [searchValue, setSearchValue] = React.useState("");
  const [optionValue, setOptionValue] = React.useState("eq");
  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const navigate = useNavigate();

  const schema = yup.object({
    first_name: yup.string(),
    email: yup.string(),
    query: yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  function updatePageSize(limit) {
    (async function () {
      setPageSize(limit);
      await getData(0, limit);
    })();
  }

  function previousPage() {
    (async function () {
      await getData(currentPage - 1 > 0 ? currentPage - 1 : 0, pageSize);
    })();
  }

  function nextPage() {
    (async function () {
      await getData(
        currentPage + 1 <= pageCount ? currentPage + 1 : 0,
        pageSize
      );
    })();
  }

  const addFilterCondition = (option, selectedValue, inputValue) => {
    const input =
      selectedValue === "eq" && isNaN(inputValue)
        ? `"${inputValue}"`
        : inputValue;
    const condition = `${option},${selectedValue},${input}`;
    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (condition) => !condition.includes(option)
      );
      return [...newConditions, condition];
    });
    setSearchValue(inputValue);
  };

  async function getData(pageNum, limitNum, data) {
    setLoading(true);
    try {
      sdk.setTable("reports");
      const result = await sdk.callJoinRestAPI(
        "reports",
        "user",
        "user_id",
        "id",
        "*",
        [],
        "PAGINATE",
        pageNum,
        limitNum
      );
      if (result) {
        setLoading(false);
      }
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const onSubmit = (data) => {
    getData(0, pageSize, {});
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "bugs",
      },
    });

    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [searchValue, filterConditions, optionValue]);

  return (
    <div className="min-h-screen bg-white">
      <div className="flex items-center justify-between px-8 py-6">
        <form className="relative" onSubmit={handleSubmit(onSubmit)}>
          <div className="flex items-center gap-4 text-gray-700">
            <div
              className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 hover:border-gray-300"
              onClick={() => setOpenFilter(!openFilter)}
            >
              <BiFilterAlt />
              <span>Filters</span>
              {selectedOptions.length > 0 && (
                <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-white">
                  {selectedOptions.length}
                </span>
              )}
            </div>
            <div className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 focus-within:border-gray-300">
              <BiSearch className="text-xl text-gray-400" />
              <input
                type="text"
                placeholder="Search"
                className="border-none p-0 placeholder:text-gray-400 focus:outline-none"
                style={{ boxShadow: "none" }}
                onInput={(e) =>
                  addFilterCondition("query", "cs", e.target?.value)
                }
              />
              <AiOutlineClose className="text-lg text-gray-400 hover:text-gray-600" />
            </div>
          </div>
          {openFilter && (
            <div className="absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-5 shadow-lg">
              <div className="mb-2 flex items-center justify-between">
                <span className="text-lg font-semibold text-gray-700">
                  Filters
                </span>
                <XIcon
                  onClick={() => {
                    setSelectedOptions([]);
                    setFilterConditions([]);
                    setFilterValues({});
                  }}
                  className="cursor-pointer text-lg text-gray-400 hover:text-gray-600"
                />
              </div>
              {selectedOptions?.map((option, index) => (
                <div
                  key={index}
                  className="mb-2 flex w-full items-center justify-between gap-3 text-gray-600"
                >
                  <div className="w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none">
                    {option}
                  </div>
                  <select
                    className="h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0"
                    onChange={(e) => {
                      setOptionValue(e.target.value);
                    }}
                  >
                    <option value="eq">equals</option>
                    <option value="cs">contains</option>
                    <option value="sw">start with</option>
                    <option value="ew">ends with</option>
                    <option value="lt">lower than</option>
                    <option value="le">lower or equal</option>
                    <option value="ge">greater or equal</option>
                    <option value="gt">greater than</option>
                    <option value="bt">between</option>
                    <option value="in">in</option>
                    <option value="is">is null</option>
                  </select>

                  <input
                    type="text"
                    placeholder="Enter value"
                    className=" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none"
                    onChange={(e) =>
                      addFilterCondition(option, optionValue, e.target.value)
                    }
                  />

                  <RiDeleteBin5Line
                    className="cursor-pointer text-2xl text-red-500 hover:text-red-600"
                    onClick={() => {
                      setSelectedOptions((prevOptions) =>
                        prevOptions.filter((op) => op !== option)
                      );
                      setFilterConditions((prevConditions) => {
                        return prevConditions.filter(
                          (condition) => !condition.includes(option)
                        );
                      });
                    }}
                  />
                </div>
              ))}

              <div className="relative flex items-center justify-between font-semibold">
                <div
                  className="flex cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 text-gray-600 hover:bg-gray-200"
                  onClick={() => {
                    setShowFilterOptions(!showFilterOptions);
                  }}
                >
                  <AiOutlinePlus />
                  Add filter
                </div>

                {showFilterOptions && (
                  <div className="absolute top-11 z-10 w-48 rounded-md border border-gray-200 bg-white py-2 shadow-lg">
                    <ul className="flex flex-col">
                      {columns.map((column) => (
                        <li
                          key={column.header}
                          className={`px-4 py-2 ${
                            selectedOptions.includes(column.header)
                              ? "cursor-not-allowed text-gray-400"
                              : "cursor-pointer text-gray-700 hover:bg-gray-50"
                          }`}
                          onClick={() => {
                            if (!selectedOptions.includes(column.header)) {
                              setSelectedOptions((prev) => [
                                ...prev,
                                column.header,
                              ]);
                            }
                            setShowFilterOptions(false);
                          }}
                        >
                          {column.header}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {selectedOptions.length > 0 && (
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedOptions([]);
                      setFilterConditions([]);
                    }}
                    className="py-2 pl-4 text-gray-600 hover:text-gray-800"
                  >
                    Clear all filter
                  </button>
                )}
              </div>
            </div>
          )}
        </form>
      </div>

      {loading ? (
        <SkeletonLoader />
      ) : (
        <div className="px-8">
          <div className="overflow-x-auto rounded-lg border border-gray-200 bg-white shadow">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      {column.header}
                      <span>
                        {column.isSorted
                          ? column.isSortedDesc
                            ? " ▼"
                            : " ▲"
                          : ""}
                      </span>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {data.map((row, i) => (
                  <tr key={i} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                      {row.first_name}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                      {row.email}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                      {row.query}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {!loading && data.length === 0 && (
              <div className="px-6 py-4 text-center text-sm text-gray-500">
                No reports found
              </div>
            )}
          </div>
        </div>
      )}
      <div className="px-8 py-4">
        <PaginationBar
          currentPage={currentPage}
          pageCount={pageCount}
          pageSize={pageSize}
          canPreviousPage={canPreviousPage}
          canNextPage={canNextPage}
          updatePageSize={updatePageSize}
          previousPage={previousPage}
          nextPage={nextPage}
        />
      </div>
    </div>
  );
};

export default AdminReportsListPage;
