import React, { useState, useEffect, useContext } from "react";
import MkdSDK from "Utils/MkdSDK";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { GlobalContext } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { PaginationBar } from "Components/PaginationBar";
import { SkeletonLoader } from "Components/Skeleton";
import { format } from "date-fns";

const sdk = new MkdSDK();

const schema = yup.object({
  userId: yup.string().required("User is required"),
  campaignId: yup.string(),
  startDate: yup.date().required("Start date is required"),
  endDate: yup.date().required("End date is required"),
});

const columns = [
  { header: "Call ID", accessor: "call_id" },
  { header: "User Name", accessor: "first_name" },
  { header: "Campaign Name", accessor: "name" },
  { header: "Duration", accessor: "duration" },
  { header: "Total Cost", accessor: "calculated_cost" },
  { header: "Deepgram", accessor: "deepgram_cost" },
  { header: "ElevenLabs", accessor: "elevenlabs_cost" },
  { header: "Azure", accessor: "azure_cost" },
  { header: "Claude", accessor: "claude_cost" },
  { header: "Twilio", accessor: "twilio_cost" },
  { header: "Date", accessor: "update_at" },
];

const AdminLogsListPage = () => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch } = useContext(AuthContext);
  const [users, setUsers] = useState([]);
  const [campaigns, setCampaigns] = useState([]);
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [pageCount, setPageCount] = useState(0);
  const [dataTotal, setDataTotal] = useState(0);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    globalDispatch({ type: "SETPATH", payload: { path: "logs" } });
    fetchUsers();
    fetchCampaigns();
  }, []);

  const fetchUsers = async () => {
    try {
      sdk.setTable("user");
      const result = await sdk.callRestAPI(
        {
          payload: { role: "user" },
          page: 1,
          limit: 50,
        },
        "GETALL"
      );
      setUsers(result.list);
    } catch (error) {
      console.error("Error fetching users", error);
    }
  };

  const fetchCampaigns = async () => {
    try {
      sdk.setTable("campaign");
      const result = await sdk.callRestAPI({}, "GETALL");
      setCampaigns(result.list);
    } catch (error) {
      console.error("Error fetching campaigns", error);
    }
  };

  const fetchLogs = async (filters) => {
    setLoading(true);
    try {
      const result = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/calls/logs",
        {
          ...filters,
          page: currentPage,
          limit: pageSize,
        },
        "POST"
      );

      const { logs: lg, total, num_pages } = result;
      setLogs(lg);
      setPageCount(num_pages);
      setDataTotal(total);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error("Error fetching logs", error);
      tokenExpireError(dispatch, error.message);
    }
  };

  const calculateTotalCost = () => {
    return logs
      .reduce((acc, log) => acc + parseFloat(log.calculated_cost || 0), 0)
      .toFixed(2);
  };

  const onSubmit = (data) => {
    const filters = {
      user_id: data.userId,
      campaign_id: data.campaignId,
      start_date: format(new Date(data.startDate), "yyyy-MM-dd"),
      end_date: format(new Date(data.endDate), "yyyy-MM-dd"),
    };
    fetchLogs(filters);
  };

  const downloadCSV = () => {
    const csvContent = [
      columns.map((col) => col.header).join(","),
      ...logs.map((log) =>
        columns
          .map((col) => {
            const value = log[col.accessor];
            return typeof value === "number" ? value.toFixed(2) : value;
          })
          .join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = "logs.csv";
    link.click();
  };

  return (
    <div className="px-8 py-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-xl font-semibold">Admin Logs</h1>
        <div className="text-lg font-semibold">
          Total Cost:{" "}
          <span className="text-blue-600">${calculateTotalCost()}</span>
        </div>
      </div>

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="mb-6 flex flex-wrap gap-4"
      >
        {/* Form inputs for User, Campaign, Start Date, End Date */}
        {/* (Same as the previous version) */}
      </form>

      {loading ? (
        <SkeletonLoader />
      ) : (
        <>
          <div className="overflow-x-auto border-b border-gray-200 shadow">
            <table className="min-w-full divide-y divide-gray-200 text-black">
              <thead className="bg-gray-50">
                <tr>
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      {column.header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {logs.map((row, i) => (
                  <tr key={i}>
                    {columns.map((cell, index) => (
                      <td key={index} className="whitespace-nowrap px-6 py-4">
                        {cell.accessor === "update_at"
                          ? format(new Date(row[cell.accessor]), "yyyy-MM-dd")
                          : typeof row[cell.accessor] === "number"
                          ? parseFloat(row[cell.accessor]).toFixed(2)
                          : row[cell.accessor]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
            {logs.length === 0 && (
              <p className="px-10 py-3 text-xl capitalize">No Logs Found</p>
            )}
          </div>
          <button
            onClick={downloadCSV}
            className="mt-4 rounded bg-green-500 px-4 py-2 text-white"
          >
            Download CSV
          </button>
        </>
      )}

      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        updatePageSize={(limit) => setPageSize(limit)}
        previousPage={() => setCurrentPage(currentPage - 1)}
        nextPage={() => setCurrentPage(currentPage + 1)}
      />
    </div>
  );
};

export default AdminLogsListPage;
