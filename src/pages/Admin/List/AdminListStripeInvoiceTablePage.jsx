import React, { useRef } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { useNavigate } from "react-router-dom";
// import { LazyLoad } from "Components/LazyLoad";
import { ModalSidebar } from "Components/ModalSidebar";
import { MkdListTableV2 } from "Components/MkdListTable";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "User Id",
    accessor: "user_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Stripe Id",
    accessor: "stripe_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Object",
    accessor: "object",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Create At",
    accessor: "create_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Update At",
    accessor: "update_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Action",
    accessor: "",
  },
];

const StripeInvoiceListPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const refreshRef = useRef(null);

  const [selectedItems, setSelectedItems] = React.useState([]);

  const onToggleModal = (modal, toggle, ids = []) => {
    switch (modal) {
      case "add":
        setShowAddSidebar(toggle);
        // setSelectedItems(ids);
        break;
      case "edit":
        setShowEditSidebar(toggle);
        setSelectedItems(ids);
        setActiveEditId(ids[0]);
        break;
    }
  };

  return (
    <>
      <>
        <div className="overflow-x-auto  rounded bg-white p-5 shadow">
          <LazyLoad>
            <MkdListTableV2
              columns={columns}
              tableRole={"admin"}
              table={"stripe_invoice"}
              actionId={"id"}
              actions={{
                view: { show: false, action: null, multiple: false },
                edit: {
                  show: true,
                  multiple: false,
                  action: (ids) => onToggleModal("edit", true, ids),
                  multiple: false,
                },
                delete: { show: false, action: null, multiple: false },
                select: { show: false, action: null, multiple: false },
                add: {
                  show: true,
                  action: () => onToggleModal("add", true),
                  multiple: false,
                  children: "Add New",
                  showChildren: true,
                },
                export: { show: false, action: null, multiple: true },
              }}
              actionPosition={`onTable`}
              refreshRef={refreshRef}
            />
          </LazyLoad>
        </div>
      </>

      <LazyLoad>
        <ModalSidebar
          isModalActive={showAddSidebar}
          closeModalFn={() => setShowAddSidebar(false)}
        >
          <AddAdminEmailPage setSidebar={setShowAddSidebar} />
        </ModalSidebar>
      </LazyLoad>

      <LazyLoad>
        <ModalSidebar
          isModalActive={showEditSidebar}
          closeModalFn={() => setShowEditSidebar(false)}
        >
          <EditAdminEmailPage
            activeId={activeEditId}
            setSidebar={setShowEditSidebar}
          />
        </ModalSidebar>
      </LazyLoad>
    </>
  );
};

export default StripeInvoiceListPage;
