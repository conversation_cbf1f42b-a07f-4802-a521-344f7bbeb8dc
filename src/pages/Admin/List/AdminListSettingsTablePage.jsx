import React from "react";
import MkdSDK from "Utils/MkdSDK";
import { useForm } from "react-hook-form";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { SkeletonLoader } from "Components/Skeleton";

let sdk = new MkdSDK();

const schema = yup.object({
  pricing: yup.number().required("Pricing is required"),
  max_active_outbound: yup.number().required("Max active outbound is required"),
  max_retry: yup.number().required("Max retry is required"),
});

const AdminListSettingsTablePage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [id, setId] = React.useState(1);
  const [loading, setLoading] = React.useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      sdk.setTable("setting");
      await sdk.callRestAPI(
        {
          id: id,
          pricing: data.pricing,
          max_active_outbound: data.max_active_outbound,
          max_retry: data.max_retry,
        },
        "PUT"
      );
      showToast(
        globalDispatch,
        "Settings updated successfully",
        4000,
        "success"
      );
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, "Failed to update settings", 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "settings",
      },
    });

    const fetchSettings = async () => {
      try {
        setLoading(true);
        sdk.setTable("setting");
        const result = await sdk.callRestAPI({}, "GETALL");

        if (!result.error && result.list.length > 0) {
          const {
            pricing,
            max_active_outbound,
            max_retry,
            id: settingId,
          } = result.list[0];
          setValue("pricing", pricing);
          setValue("max_active_outbound", max_active_outbound);
          setValue("max_retry", max_retry);
          setId(settingId || 8);
        }
      } catch (error) {
        console.error("Error", error);
        tokenExpireError(dispatch, error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  return (
    <div className="px-8 py-6 min-h-screen bg-white">
      {loading ? (
        <SkeletonLoader />
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          <div className="p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
            <h2 className="mb-6 text-2xl font-semibold text-gray-800">
              General Settings
            </h2>

            <div className="grid gap-6 text-black md:grid-cols-2 lg:grid-cols-3">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Pricing per minute ($)
                </label>
                <input
                  type="number"
                  step="0.01"
                  {...register("pricing")}
                  className={`w-full rounded-md border ${
                    errors.pricing ? "border-red-500" : "border-gray-300"
                  } px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500`}
                  placeholder="0.00"
                />
                {errors.pricing && (
                  <p className="text-sm text-red-500">
                    {errors.pricing.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Max Active Outbounds per User
                </label>
                <input
                  type="number"
                  {...register("max_active_outbound")}
                  className={`w-full rounded-md border ${
                    errors.max_active_outbound
                      ? "border-red-500"
                      : "border-gray-300"
                  } px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500`}
                  placeholder="Enter max active outbounds"
                />
                {errors.max_active_outbound && (
                  <p className="text-sm text-red-500">
                    {errors.max_active_outbound.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Max Retries for Missed Calls
                </label>
                <input
                  type="number"
                  {...register("max_retry")}
                  className={`w-full rounded-md border ${
                    errors.max_retry ? "border-red-500" : "border-gray-300"
                  } px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500`}
                  placeholder="Enter max retries"
                />
                {errors.max_retry && (
                  <p className="text-sm text-red-500">
                    {errors.max_retry.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center rounded-md bg-[#2cc9d5] px-6 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-[#28b8c3] focus:outline-none focus:ring-2 focus:ring-[#2cc9d5] focus:ring-offset-2 disabled:opacity-60"
            >
              {loading ? (
                <>
                  <svg
                    className="mr-2 w-4 h-4 animate-spin"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Updating...
                </>
              ) : (
                "Update Settings"
              )}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default AdminListSettingsTablePage;
