import React from "react";

import { Spinner } from "Assets/svgs";

const NotFoundPage = () => {
  const [loading, setLoading] = React.useState(true);

  console.log(loading);

  React.useEffect(() => {
    const interval = setTimeout(() => {
      setLoading(false);
    }, 5000);
    // return () => clearInterval(interval);
  }, []);

  return (
    <>
      {loading ? (
        <div
          className={`flex justify-center items-center py-5 w-full min-w-full max-w-full max-h-fit min-h-fit`}
        >
          <Spinner size={100} color="#0EA5E9" />
        </div>
      ) : (
        <div className="flex justify-center items-center w-full h-screen text-7xl text-gray-700">
          Not Found
        </div>
      )}
    </>
  );
};

export default NotFoundPage;
