import React, { useState, useEffect, useContext } from "react";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import {
  MdSave,
  MdPerson,
  MdNotifications,
  MdSettings,
  MdIntegrationInstructions,
  MdSecurity,
} from "react-icons/md";

const sdk = new MkdSDK();

const WholesalerSettingsPage = () => {
  const { dispatch } = useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [settings, setSettings] = useState({
    general: {
      company_name: "",
      contact_email: "",
      phone_number: "",
      timezone: "America/New_York",
      default_source_type: "auto_insurance",
    },
    notifications: {
      email_alerts: true,
      sms_alerts: false,
      daily_reports: true,
      weekly_reports: true,
      campaign_status_changes: true,
      lead_processing_alerts: true,
      cost_threshold_alerts: true,
      cost_threshold: 100,
    },
    defaults: {
      max_concurrent_calls: 10,
      default_retry_attempts: 3,
      default_schedule_start: "09:00",
      default_schedule_end: "17:00",
      default_model: "llama-3.3-70b",
      auto_pause_on_errors: true,
      auto_pause_threshold: 10,
    },
    integrations: {
      ringba_api_key: "",
      ringba_endpoint: "https://api.ringba.com/v2",
      twilio_account_sid: "",
      deepgram_api_key: "",
      elevenlabs_api_key: "",
      webhook_url: "",
      webhook_secret: "",
    },
    security: {
      api_key_expiry_days: 90,
      require_ip_whitelist: false,
      allowed_ips: [],
      two_factor_enabled: false,
      session_timeout: 480, // 8 hours in minutes
    },
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const result = await sdk.settingsAPI.get();
      if (!result.error && result.data) {
        setSettings(prev => ({
          ...prev,
          ...result.data,
        }));
      }
    } catch (error) {
      console.error("Error loading settings:", error);
      showToast(dispatch, "Failed to load settings", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async (section) => {
    try {
      setSaving(true);
      const result = await sdk.settingsAPI.update({
        section,
        data: settings[section],
      });
      
      if (!result.error) {
        showToast(dispatch, "Settings saved successfully", 4000, "success");
      } else {
        showToast(dispatch, result.message || "Failed to save settings", 5000, "error");
      }
    } catch (error) {
      console.error("Error saving settings:", error);
      showToast(dispatch, "Failed to save settings", 5000, "error");
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (section, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const tabs = [
    { id: "general", label: "General", icon: <MdPerson /> },
    { id: "notifications", label: "Notifications", icon: <MdNotifications /> },
    { id: "defaults", label: "Defaults", icon: <MdSettings /> },
    { id: "integrations", label: "Integrations", icon: <MdIntegrationInstructions /> },
    { id: "security", label: "Security", icon: <MdSecurity /> },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-2">
          Manage your wholesaler account preferences and configurations
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === "general" && (
            <GeneralSettings
              settings={settings.general}
              onUpdate={(field, value) => updateSetting("general", field, value)}
              onSave={() => saveSettings("general")}
              saving={saving}
            />
          )}

          {activeTab === "notifications" && (
            <NotificationSettings
              settings={settings.notifications}
              onUpdate={(field, value) => updateSetting("notifications", field, value)}
              onSave={() => saveSettings("notifications")}
              saving={saving}
            />
          )}

          {activeTab === "defaults" && (
            <DefaultSettings
              settings={settings.defaults}
              onUpdate={(field, value) => updateSetting("defaults", field, value)}
              onSave={() => saveSettings("defaults")}
              saving={saving}
            />
          )}

          {activeTab === "integrations" && (
            <IntegrationSettings
              settings={settings.integrations}
              onUpdate={(field, value) => updateSetting("integrations", field, value)}
              onSave={() => saveSettings("integrations")}
              saving={saving}
            />
          )}

          {activeTab === "security" && (
            <SecuritySettings
              settings={settings.security}
              onUpdate={(field, value) => updateSetting("security", field, value)}
              onSave={() => saveSettings("security")}
              saving={saving}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// General Settings Component
const GeneralSettings = ({ settings, onUpdate, onSave, saving }) => (
  <div className="space-y-6">
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Company Information</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Company Name
          </label>
          <input
            type="text"
            value={settings.company_name}
            onChange={(e) => onUpdate("company_name", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter company name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Contact Email
          </label>
          <input
            type="email"
            value={settings.contact_email}
            onChange={(e) => onUpdate("contact_email", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter contact email"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Phone Number
          </label>
          <input
            type="tel"
            value={settings.phone_number}
            onChange={(e) => onUpdate("phone_number", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter phone number"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Default Timezone
          </label>
          <select
            value={settings.timezone}
            onChange={(e) => onUpdate("timezone", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="America/New_York">Eastern Time</option>
            <option value="America/Chicago">Central Time</option>
            <option value="America/Denver">Mountain Time</option>
            <option value="America/Los_Angeles">Pacific Time</option>
          </select>
        </div>
      </div>
    </div>

    <div className="flex justify-end">
      <button
        onClick={onSave}
        disabled={saving}
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
      >
        <MdSave />
        {saving ? "Saving..." : "Save Changes"}
      </button>
    </div>
  </div>
);

// Notification Settings Component
const NotificationSettings = ({ settings, onUpdate, onSave, saving }) => (
  <div className="space-y-6">
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Alert Preferences</h3>
      <div className="space-y-4">
        {[
          { key: "email_alerts", label: "Email Alerts", desc: "Receive alerts via email" },
          { key: "sms_alerts", label: "SMS Alerts", desc: "Receive alerts via SMS" },
          { key: "daily_reports", label: "Daily Reports", desc: "Daily performance summaries" },
          { key: "weekly_reports", label: "Weekly Reports", desc: "Weekly analytics reports" },
          { key: "campaign_status_changes", label: "Campaign Status Changes", desc: "Notifications when campaigns start/stop" },
          { key: "lead_processing_alerts", label: "Lead Processing Alerts", desc: "Alerts for lead processing issues" },
          { key: "cost_threshold_alerts", label: "Cost Threshold Alerts", desc: "Alerts when costs exceed threshold" },
        ].map((item) => (
          <label key={item.key} className="flex items-start">
            <input
              type="checkbox"
              checked={settings[item.key]}
              onChange={(e) => onUpdate(item.key, e.target.checked)}
              className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <div className="ml-3">
              <span className="text-sm font-medium text-gray-700">{item.label}</span>
              <p className="text-sm text-gray-500">{item.desc}</p>
            </div>
          </label>
        ))}
      </div>
    </div>

    {settings.cost_threshold_alerts && (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Cost Threshold ($)
        </label>
        <input
          type="number"
          min="0"
          step="0.01"
          value={settings.cost_threshold}
          onChange={(e) => onUpdate("cost_threshold", parseFloat(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter cost threshold"
        />
      </div>
    )}

    <div className="flex justify-end">
      <button
        onClick={onSave}
        disabled={saving}
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
      >
        <MdSave />
        {saving ? "Saving..." : "Save Changes"}
      </button>
    </div>
  </div>
);

// Default Settings Component
const DefaultSettings = ({ settings, onUpdate, onSave, saving }) => (
  <div className="space-y-6">
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Defaults</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Max Concurrent Calls
          </label>
          <input
            type="number"
            min="1"
            max="100"
            value={settings.max_concurrent_calls}
            onChange={(e) => onUpdate("max_concurrent_calls", parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Default Retry Attempts
          </label>
          <input
            type="number"
            min="0"
            max="10"
            value={settings.default_retry_attempts}
            onChange={(e) => onUpdate("default_retry_attempts", parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Default Schedule Start
          </label>
          <input
            type="time"
            value={settings.default_schedule_start}
            onChange={(e) => onUpdate("default_schedule_start", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Default Schedule End
          </label>
          <input
            type="time"
            value={settings.default_schedule_end}
            onChange={(e) => onUpdate("default_schedule_end", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Default AI Model
          </label>
          <select
            value={settings.default_model}
            onChange={(e) => onUpdate("default_model", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="llama-3.3-70b">Llama 3.3 70B</option>
            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
            <option value="gpt-4">GPT-4</option>
          </select>
        </div>
      </div>
    </div>

    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Auto-Management</h3>
      <div className="space-y-4">
        <label className="flex items-start">
          <input
            type="checkbox"
            checked={settings.auto_pause_on_errors}
            onChange={(e) => onUpdate("auto_pause_on_errors", e.target.checked)}
            className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <div className="ml-3">
            <span className="text-sm font-medium text-gray-700">Auto-pause on errors</span>
            <p className="text-sm text-gray-500">Automatically pause campaigns when error threshold is reached</p>
          </div>
        </label>

        {settings.auto_pause_on_errors && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Error Threshold
            </label>
            <input
              type="number"
              min="1"
              max="100"
              value={settings.auto_pause_threshold}
              onChange={(e) => onUpdate("auto_pause_threshold", parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-500 mt-1">Number of consecutive errors before auto-pause</p>
          </div>
        )}
      </div>
    </div>

    <div className="flex justify-end">
      <button
        onClick={onSave}
        disabled={saving}
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
      >
        <MdSave />
        {saving ? "Saving..." : "Save Changes"}
      </button>
    </div>
  </div>
);

// Integration Settings Component
const IntegrationSettings = ({ settings, onUpdate, onSave, saving }) => (
  <div className="space-y-6">
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">API Integrations</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ringba API Key
          </label>
          <input
            type="password"
            value={settings.ringba_api_key}
            onChange={(e) => onUpdate("ringba_api_key", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter Ringba API key"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ringba Endpoint
          </label>
          <input
            type="url"
            value={settings.ringba_endpoint}
            onChange={(e) => onUpdate("ringba_endpoint", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="https://api.ringba.com/v2"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Webhook URL
          </label>
          <input
            type="url"
            value={settings.webhook_url}
            onChange={(e) => onUpdate("webhook_url", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="https://your-domain.com/webhook"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Webhook Secret
          </label>
          <input
            type="password"
            value={settings.webhook_secret}
            onChange={(e) => onUpdate("webhook_secret", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter webhook secret"
          />
        </div>
      </div>
    </div>

    <div className="flex justify-end">
      <button
        onClick={onSave}
        disabled={saving}
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
      >
        <MdSave />
        {saving ? "Saving..." : "Save Changes"}
      </button>
    </div>
  </div>
);

// Security Settings Component
const SecuritySettings = ({ settings, onUpdate, onSave, saving }) => (
  <div className="space-y-6">
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Security Preferences</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            API Key Expiry (days)
          </label>
          <input
            type="number"
            min="1"
            max="365"
            value={settings.api_key_expiry_days}
            onChange={(e) => onUpdate("api_key_expiry_days", parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Session Timeout (minutes)
          </label>
          <input
            type="number"
            min="30"
            max="1440"
            value={settings.session_timeout}
            onChange={(e) => onUpdate("session_timeout", parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>
    </div>

    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Access Control</h3>
      <div className="space-y-4">
        <label className="flex items-start">
          <input
            type="checkbox"
            checked={settings.require_ip_whitelist}
            onChange={(e) => onUpdate("require_ip_whitelist", e.target.checked)}
            className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <div className="ml-3">
            <span className="text-sm font-medium text-gray-700">Require IP Whitelist</span>
            <p className="text-sm text-gray-500">Only allow access from whitelisted IP addresses</p>
          </div>
        </label>

        <label className="flex items-start">
          <input
            type="checkbox"
            checked={settings.two_factor_enabled}
            onChange={(e) => onUpdate("two_factor_enabled", e.target.checked)}
            className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <div className="ml-3">
            <span className="text-sm font-medium text-gray-700">Two-Factor Authentication</span>
            <p className="text-sm text-gray-500">Enable 2FA for additional security</p>
          </div>
        </label>
      </div>
    </div>

    <div className="flex justify-end">
      <button
        onClick={onSave}
        disabled={saving}
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
      >
        <MdSave />
        {saving ? "Saving..." : "Save Changes"}
      </button>
    </div>
  </div>
);

export default WholesalerSettingsPage;
