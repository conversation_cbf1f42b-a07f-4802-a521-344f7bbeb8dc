import React, { useState, useEffect, useContext } from "react";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import {
  MdAttachMoney,
  MdTrendingUp,
  MdTrendingDown,
  MdPhone,
  MdPeople,
  MdDateRange,
} from "react-icons/md";
import Chart from "react-apexcharts";

const sdk = new MkdSDK();

const WholesalerCostTrackingPage = () => {
  const { dispatch } = useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState("30d");
  const [costData, setCostData] = useState({
    overview: {
      totalCost: 0,
      avgCostPerLead: 0,
      avgCostPerCall: 0,
      avgCostPerMinute: 0,
      totalMinutes: 0,
      costChange: 0,
    },
    breakdown: {
      callCosts: 0,
      systemCosts: 0,
      integrationCosts: 0,
      otherCosts: 0,
    },
    trends: {
      labels: [],
      costs: [],
      leads: [],
      calls: [],
    },
    campaigns: [],
  });

  useEffect(() => {
    loadCostData();
  }, [dateRange]);

  const loadCostData = async () => {
    try {
      setLoading(true);
      
      const [costsResult, analyticsResult] = await Promise.all([
        sdk.costTrackingAPI.getCosts({ period: dateRange }),
        sdk.costTrackingAPI.getAnalytics({ period: dateRange }),
      ]);
      
      setCostData({
        overview: costsResult.overview || {},
        breakdown: costsResult.breakdown || {},
        trends: analyticsResult.trends || { labels: [], costs: [], leads: [], calls: [] },
        campaigns: costsResult.campaigns || [],
      });
      
    } catch (error) {
      console.error("Error loading cost data:", error);
      showToast(dispatch, "Error loading cost data", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const CostCard = ({ title, value, subtitle, icon, color, change }) => (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>${value.toFixed(2)}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
          )}
          {change !== undefined && (
            <div className="flex items-center mt-1">
              {change >= 0 ? (
                <MdTrendingUp className="text-red-500 text-sm mr-1" />
              ) : (
                <MdTrendingDown className="text-green-500 text-sm mr-1" />
              )}
              <span className={`text-sm ${change >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                {Math.abs(change).toFixed(1)}%
              </span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
          {icon}
        </div>
      </div>
    </div>
  );

  const lineChartOptions = {
    chart: {
      type: 'line',
      height: 350,
      toolbar: {
        show: false,
      },
    },
    title: {
      text: 'Cost Trends Over Time',
      align: 'left',
    },
    xaxis: {
      categories: costData.trends.labels,
    },
    yaxis: {
      title: {
        text: 'Cost ($)',
      },
      labels: {
        formatter: function(value) {
          return '$' + value.toFixed(2);
        }
      }
    },
    stroke: {
      curve: 'smooth',
      width: 2,
    },
    colors: ['#EF4444'],
    legend: {
      position: 'top',
    },
  };

  const lineChartSeries = [
    {
      name: 'Daily Cost',
      data: costData.trends.costs,
    },
  ];

  const barChartOptions = {
    chart: {
      type: 'bar',
      height: 350,
      toolbar: {
        show: false,
      },
    },
    title: {
      text: 'Cost Breakdown by Category',
      align: 'left',
    },
    xaxis: {
      categories: ['Call Costs', 'System Costs', 'Integration Costs', 'Other Costs'],
    },
    yaxis: {
      title: {
        text: 'Cost ($)',
      },
      labels: {
        formatter: function(value) {
          return '$' + value.toFixed(2);
        }
      }
    },
    colors: ['#EF4444', '#3B82F6', '#10B981', '#F59E0B'],
    legend: {
      show: false,
    },
  };

  const barChartSeries = [
    {
      name: 'Cost ($)',
      data: [
        costData.breakdown.callCosts,
        costData.breakdown.systemCosts,
        costData.breakdown.integrationCosts,
        costData.breakdown.otherCosts,
      ],
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Cost Tracking</h1>
          <p className="text-gray-600 mt-1">Monitor and analyze your campaign costs</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Date Range
          </label>
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Cost Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <CostCard
          title="Total Cost"
          value={costData.overview.totalCost}
          icon={<MdAttachMoney className="text-xl text-red-600" />}
          color="text-red-600"
          change={costData.overview.costChange}
        />
        <CostCard
          title="Cost per Lead"
          value={costData.overview.avgCostPerLead}
          subtitle="Average"
          icon={<MdPeople className="text-xl text-blue-600" />}
          color="text-blue-600"
        />
        <CostCard
          title="Cost per Call"
          value={costData.overview.avgCostPerCall}
          subtitle="Average"
          icon={<MdPhone className="text-xl text-green-600" />}
          color="text-green-600"
        />
        <CostCard
          title="Cost per Minute"
          value={costData.overview.avgCostPerMinute}
          subtitle={`${costData.overview.totalMinutes} total minutes`}
          icon={<MdDateRange className="text-xl text-purple-600" />}
          color="text-purple-600"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cost Trends */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Cost Trends</h2>
          <div className="h-80">
            <Chart
              options={lineChartOptions}
              series={lineChartSeries}
              type="line"
              height={350}
            />
          </div>
        </div>

        {/* Cost Breakdown */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Cost Breakdown</h2>
          <div className="h-80">
            <Chart
              options={barChartOptions}
              series={barChartSeries}
              type="bar"
              height={350}
            />
          </div>
        </div>
      </div>

      {/* Campaign Cost Breakdown */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Campaign Cost Breakdown</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Campaign
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Cost
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Leads
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Calls
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cost per Lead
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cost per Call
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Minutes
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {costData.campaigns.map((campaign) => (
                <tr key={campaign.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {campaign.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${campaign.totalCost.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campaign.leads.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campaign.calls.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${campaign.costPerLead.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${campaign.costPerCall.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campaign.minutes.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Cost Optimization Tips */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Cost Optimization Tips</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-gray-900">Reduce Call Costs</h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Optimize call timing to reduce busy signals</li>
              <li>• Use schedule windows to call during peak hours</li>
              <li>• Implement smart retry logic to avoid repeated failed calls</li>
              <li>• Monitor call duration and optimize scripts</li>
            </ul>
          </div>
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-gray-900">Improve Lead Quality</h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Filter leads by quality score before calling</li>
              <li>• Analyze source performance and focus on best sources</li>
              <li>• Use lead scoring to prioritize high-value leads</li>
              <li>• Implement lead validation to reduce invalid numbers</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WholesalerCostTrackingPage;
