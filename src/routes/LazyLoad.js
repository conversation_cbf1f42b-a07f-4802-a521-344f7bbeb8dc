import { lazy } from "react";

export const AddAdminCmsPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminCmsPage");
  __import.finally(() => {});
  return __import;
});

export const AddAdminEmailPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminEmailPage");
  __import.finally(() => {});
  return __import;
});

export const AddAdminPhotoPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminPhotoPage");
  __import.finally(() => {});
  return __import;
});

export const AdminCmsListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminCmsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEmailListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminEmailListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminPhotoListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminPhotoListPage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminCmsPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/EditAdminCmsPage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminEmailPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/EditAdminEmailPage");
  __import.finally(() => {});
  return __import;
});

export const UserMagicLoginPage = lazy(() => {
  const __import = import("../pages/MagicLogin/UserMagicLoginPage");
  __import.finally(() => {});
  return __import;
});

export const MagicLoginVerifyPage = lazy(() => {
  const __import = import("../pages/MagicLogin/MagicLoginVerifyPage");
  __import.finally(() => {});
  return __import;
});

export const CustomAdminLoginPage = lazy(() => {
  const __import = import("../pages/Admin/Auth/CustomAdminLoginPage");
  __import.finally(() => {});
  return __import;
});

export const CustomAdminSignUpPage = lazy(() => {
  const __import = import("../pages/Admin/Auth/CustomAdminSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const CustomAdminProfilePage = lazy(() => {
  const __import = import("../pages/Admin/View/CustomAdminProfilePage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserLoginPage = lazy(() => {
  const __import = import("../pages/User/Auth/CustomUserLoginPage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserSignUpPage = lazy(() => {
  const __import = import("../pages/User/Auth/CustomUserSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserProfilePage = lazy(() => {
  const __import = import("../pages/User/View/CustomUserProfilePage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserSettingsPage = lazy(() => {
  const __import = import("../pages/User/View/CustomUserSettingsPage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserVideoGeneration = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserVideoGeneration");
  __import.finally(() => {});
  return __import;
});

export const CustomUserOnboardingPage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserOnboardingPage");
  __import.finally(() => {});
  return __import;
});
export const CustomUserTSettingsPage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserTSettingsPage");
  __import.finally(() => {});
  return __import;
});
export const CustomUserSummaryPage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserSummaryPage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserVoicePage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserVoicePage");
  __import.finally(() => {});
  return __import;
});
export const CustomUserSmsPage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserSmsPage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserVideoPage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserVideoPage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserEmbedPage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserEmbedPage");
  __import.finally(() => {});
  return __import;
});

export const UserListProfileTablePage = lazy(() => {
  const __import = import("../pages/User/List/UserListProfileTablePage");
  __import.finally(() => {});
  return __import;
});

export const UserAddProfileTablePage = lazy(() => {
  const __import = import("../pages/User/Add/UserAddProfileTablePage");
  __import.finally(() => {});
  return __import;
});

export const UserEditProfileTablePage = lazy(() => {
  const __import = import("../pages/User/Edit/UserEditProfileTablePage");
  __import.finally(() => {});
  return __import;
});

export const UserViewProfileTablePage = lazy(() => {
  const __import = import("../pages/User/View/UserViewProfileTablePage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserChatPage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserChatPage");
  __import.finally(() => {});
  return __import;
});

export const UserListKnowledgeBankTablePage = lazy(() => {
  const __import = import("../pages/User/List/UserListKnowledgeBankTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserListVoiceTablePage = lazy(() => {
  const __import = import("../pages/User/List/UserListVoiceTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserListAssistantsTablePage = lazy(() => {
  const __import = import("../pages/User/List/UserListAssistantsTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserListNumbersTablePage = lazy(() => {
  const __import = import("../pages/User/List/UserListNumbersTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserListOutboundCallLogsTablePage = lazy(() => {
  const __import = import(
    "../pages/User/List/UserListOutboundCallLogsTablePage"
  );
  __import.finally(() => {});
  return __import;
});
export const UserListInboundCallLogsTablePage = lazy(() => {
  const __import = import(
    "../pages/User/List/UserListInboundCallLogsTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const UserAddKnowledgeBankTablePage = lazy(() => {
  const __import = import("../pages/User/Add/UserAddKnowledgeBankTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserAddAssistantsTablePage = lazy(() => {
  const __import = import("../pages/User/Add/UserAddAssistantsTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserAddCampaignTablePage = lazy(() => {
  const __import = import("../pages/User/Add/UserCampaignTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserAddNumbersTablePage = lazy(() => {
  const __import = import("../pages/User/Add/UserNumbersTablePage");
  __import.finally(() => {});
  return __import;
});

export const UserEditVoiceTablePage = lazy(() => {
  const __import = import("../pages/User/Edit/UserEditVoiceTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserAddScriptsTablePage = lazy(() => {
  const __import = import("../pages/User/Add/UserAddScriptsTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserListScriptsTablePage = lazy(() => {
  const __import = import("../pages/User/List/UserListScriptsTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserEditAssistantTablePage = lazy(() => {
  const __import = import("../pages/User/Edit/UserEditAssistantTablePage");
  __import.finally(() => {});
  return __import;
});
export const UserEditKnowledgeBankTablePage = lazy(() => {
  const __import = import("../pages/User/Edit/UserEditKnowledgeBankTablePage");
  __import.finally(() => {});
  return __import;
});

export const UserViewKnowledgeBankTablePage = lazy(() => {
  const __import = import("../pages/User/View/UserViewKnowledgeBankTablePage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserIntegrationsPage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserIntegrationsPage");
  __import.finally(() => {});
  return __import;
});

export const UserListStripeSubscriptionTablePage = lazy(() => {
  const __import = import(
    "../pages/User/List/UserListStripeSubscriptionTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminStripeSubscriptionsListPage = lazy(() => {
  const __import = import(
    "../pages/Admin/List/AdminStripeSubscriptionsListPage"
  );
  __import.finally(() => {});
  return __import;
});

// export const AdminStripeInvoicesListPageV2 = lazy(() => {
//   const __import = import('../pages/Admin/List/AdminStripeInvoicesListPageV2');
//   __import.finally(() => {});
//   return __import;
// });

export const AdminListStripeSubscriptionTablePage = lazy(() => {
  const __import = import(
    "../pages/Admin/List/AdminListStripeSubscriptionTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripeSubscriptionTablePage = lazy(() => {
  const __import = import(
    "../pages/Admin/Add/AdminAddStripeSubscriptionTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripeSubscriptionTablePage = lazy(() => {
  const __import = import(
    "../pages/Admin/Edit/AdminEditStripeSubscriptionTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripeSubscriptionTablePage = lazy(() => {
  const __import = import(
    "../pages/Admin/View/AdminViewStripeSubscriptionTablePage"
  );
  __import.finally(() => {});
  return __import;
});

// export const AdminListStripeInvoiceTablePage = lazy(() => {
//   const __import = import(
//     '../pages/Admin/List/AdminListStripeInvoiceTablePage'
//   );
//   __import.finally(() => {});
//   return __import;
// });

// export const AdminAddStripeInvoiceTablePage = lazy(() => {
//   const __import = import('../pages/Admin/Add/AdminAddStripeInvoiceTablePage');
//   __import.finally(() => {});
//   return __import;
// });

// export const AdminEditStripeInvoiceTablePage = lazy(() => {
//   const __import = import(
//     '../pages/Admin/Edit/AdminEditStripeInvoiceTablePage'
//   );
//   __import.finally(() => {});
//   return __import;
// });

// export const AdminViewStripeInvoiceTablePage = lazy(() => {
//   const __import = import(
//     '../pages/Admin/View/AdminViewStripeInvoiceTablePage'
//   );
//   __import.finally(() => {});
//   return __import;
// });

export const AddAdminStripePricePage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminStripePricePage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminStripePricePage = lazy(() => {
  const __import = import("../pages/Admin/Edit/EditAdminStripePricePage");
  __import.finally(() => {});
  return __import;
});
export const EditAdminStripeProductsPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/EditAdminStripeProductPage");
  __import.finally(() => {});
  return __import;
});
export const AddAdminStripeProductPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminStripeProductPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripePricesListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminStripePricesListPage");
  __import.finally(() => {});
  return __import;
});
export const AdminStripeProductsListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminStripeProductsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripePriceTablePage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddStripePriceTablePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripePriceTablePage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditStripePriceTablePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripePriceTablePage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewStripePriceTablePage");
  __import.finally(() => {});
  return __import;
});
export const AdminAddKnowledgeBankTablePage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddKnowledgeBankTablePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditKnowledgeBankTablePage = lazy(() => {
  const __import = import(
    "../pages/Admin/Edit/AdminEditKnowledgeBankTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminViewKnowledgeBankTablePage = lazy(() => {
  const __import = import(
    "../pages/Admin/View/AdminViewKnowledgeBankTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const UserForgotPage = lazy(() => {
  const __import = import("../pages/User/Auth/UserForgotPage");
  __import.finally(() => {});
  return __import;
});

export const UserResetPage = lazy(() => {
  const __import = import("../pages/User/Auth/UserResetPage");
  __import.finally(() => {});
  return __import;
});

export const UserDashboardPage = lazy(() => {
  const __import = import("../pages/User/View/UserDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const AdminForgotPage = lazy(() => {
  const __import = import("../pages/Admin/Auth/AdminForgotPage");
  __import.finally(() => {});
  return __import;
});

export const AdminResetPage = lazy(() => {
  const __import = import("../pages/Admin/Auth/AdminResetPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDashboardPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const AdminListSettingsTablePage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListSettingsTablePage");
  __import.finally(() => {});
  return __import;
});

export const AdminUserListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminUserListPage");
  __import.finally(() => {});
  return __import;
});
export const AdminAdminListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminAdminListPage");
  __import.finally(() => {});
  return __import;
});
export const AdminForwardingListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminForwardingListPage");
  __import.finally(() => {});
  return __import;
});
export const AdminReportsListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminReportsListPage");
  __import.finally(() => {});
  return __import;
});
export const AdminInvoicesListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminInvoicesListPage");
  __import.finally(() => {});
  return __import;
});

export const AddAdminUserPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminUserPage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminUserPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/EditAdminUserPage");
  __import.finally(() => {});
  return __import;
});
export const UserAddOutboundCampaignPage = lazy(() => {
  const __import = import("../pages/User/Add/UserAddOutboundCampaignPage");
  __import.finally(() => {});
  return __import;
});
export const UserAddSMSOutboundCampaignPage = lazy(() => {
  const __import = import("../pages/User/Add/UserAddSMSOutboundCampaignPage");
  __import.finally(() => {});
  return __import;
});
export const UserAddSMSInboundCampaignPage = lazy(() => {
  const __import = import("../pages/User/Add/UserAddSMSInboundCampaignPage");
  __import.finally(() => {});
  return __import;
});
export const CustomUserListOutboundCampaignsPage = lazy(() => {
  const __import = import(
    "../pages/User/Custom/CustomUserListOutboundCampaignsPage"
  );
  __import.finally(() => {});
  return __import;
});
export const CustomUserListInboundCampaignsPage = lazy(() => {
  const __import = import(
    "../pages/User/Custom/CustomUserListInboundCampaignsPage"
  );
  __import.finally(() => {});
  return __import;
});
export const CustomUserListOutboundCallLogsPage = lazy(() => {
  const __import = import(
    "../pages/User/Custom/CustomUserListOutboundCallLogsPage"
  );
  __import.finally(() => {});
  return __import;
});
export const CustomUserListTestCallLogsPage = lazy(() => {
  const __import = import(
    "../pages/User/Custom/CustomUserListTestCallLogsPage"
  );
  __import.finally(() => {});
  return __import;
});
export const CustomUserListTestSMSLogsPage = lazy(() => {
  const __import = import("../pages/User/Custom/CustomUserListTestSMSLogsPage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserListInboundCallLogsPage = lazy(() => {
  const __import = import(
    "../pages/User/Custom/CustomUserListInboundCallLogsPage"
  );
  __import.finally(() => {});
  return __import;
});

export const CustomUserListSMSOutboundCampaignsPage = lazy(() => {
  const __import = import(
    "../pages/User/Custom/CustomUserListSMSOutboundCampaignsPage"
  );
  __import.finally(() => {});
  return __import;
});

export const CustomUserListSMSInboundCampaignsPage = lazy(() => {
  const __import = import(
    "../pages/User/Custom/CustomUserListSMSInboundCampaignsPage"
  );
  __import.finally(() => {});
  return __import;
});

export const CustomUserListSMSOutboundLogsPage = lazy(() => {
  const __import = import(
    "../pages/User/Custom/CustomUserListSMSOutboundLogsPage"
  );
  __import.finally(() => {});
  return __import;
});

export const CustomUserListSMSInboundLogsPage = lazy(() => {
  const __import = import(
    "../pages/User/Custom/CustomUserListSMSInboundLogsPage"
  );
  __import.finally(() => {});
  return __import;
});
