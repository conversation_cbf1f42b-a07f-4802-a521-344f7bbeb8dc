import React, { useEffect, useContext, useState, Suspense } from "react";
import { Routes, Route } from "react-router-dom";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";

import PrivateRoute from "./PrivateRoutes";
import PublicRoute from "./PublicRoutes";
import { PublicWrapper } from "Components/PublicWrapper";
import { NotFoundPage } from "Pages/404";
import { SnackBar } from "Components/SnackBar";
import { SessionExpiredModal } from "Components/SessionExpiredModal";

// generatePagesRoutes
import { UserWrapper } from "Components/UserWrapper";
import { AdminWrapper } from "Components/AdminWrapper";
import { WholesalerWrapper } from "Components/WholesalerWrapper";

import {
  AddAdminCmsPage,
  AddAdminEmailPage,
  AddAdminPhotoPage,
  AdminCmsListPage,
  AdminEmailListPage,
  AdminStripeSubscriptionsListPage,
  // AdminStripeInBilling/sListPageV2,
  AdminPhotoListPage,
  AdminForwardingListPage,
  AdminReportsListPage,
  AdminInvoicesListPage,
  EditAdminCmsPage,
  EditAdminEmailPage,
  UserMagicLoginPage,
  MagicLoginVerifyPage,
  AdminListSettingsTablePage,
  CustomAdminLoginPage,
  CustomAdminSignUpPage,
  CustomAdminProfilePage,
  CustomUserLoginPage,
  CustomUserSignUpPage,
  CustomUserProfilePage,
  CustomUserOnboardingPage,
  UserAddCampaignTablePage,
  UserAddNumbersTablePage,
  UserAddAssistantsTablePage,
  UserEditVoiceTablePage,
  UserEditAssistantTablePage,
  UserListScriptsTablePage,
  UserAddScriptsTablePage,
  CustomUserSettingsPage,
  CustomUserVoicePage,
  CustomUserSmsPage,
  CustomUserVideoPage,
  CustomUserEmbedPage,
  UserListProfileTablePage,
  UserAddProfileTablePage,
  UserEditProfileTablePage,
  UserViewProfileTablePage,
  CustomUserChatPage,
  UserListKnowledgeBankTablePage,
  UserAddKnowledgeBankTablePage,
  UserEditKnowledgeBankTablePage,
  UserViewKnowledgeBankTablePage,
  CustomUserListTestCallLogsPage,
  CustomUserListTestSMSLogsPage,
  CustomUserIntegrationsPage,
  CustomUserSummaryPage,
  UserListStripeSubscriptionTablePage,
  AdminListStripeSubscriptionTablePage,
  AdminStripePricesListPage,
  AdminAddStripeSubscriptionTablePage,
  AdminEditStripeSubscriptionTablePage,
  AdminViewStripeSubscriptionTablePage,
  AdminStripeProductsListPage,
  AdminAddStripePriceTablePage,
  AdminEditStripePriceTablePage,
  AdminViewStripePriceTablePage,
  // AdminListKnowledgeBankTablePage,
  // AdminAddKnowledgeBankTablePage,
  // AdminEditKnowledgeBankTablePage,
  // AdminViewKnowledgeBankTablePage,
  UserForgotPage,
  UserResetPage,
  UserDashboardPage,
  AdminForgotPage,
  AdminResetPage,
  AdminDashboardPage,
  AdminUserListPage,
  AdminAdminListPage,
  AddAdminUserPage,
  // AddAdminStripePricePage,
  EditAdminUserPage,
  // EditAdminStripePricePage,
  CustomUserVideoGeneration,
  UserListVoiceTablePage,
  UserListAssistantsTablePage,
  UserListNumbersTablePage,
  CustomUserListOutboundCampaignsPage,
  UserListOutboundCallLogsTablePage,
  UserListInboundCallLogsTablePage,
  CustomUserListInboundCampaignsPage,
  CustomUserListOutboundCallLogsPage,
  CustomUserListInboundCallLogsPage,
  CustomUserTSettingsPage,
  CustomUserListSMSOutboundCampaignsPage,
  CustomUserListSMSInboundCampaignsPage,
  CustomUserListSMSOutboundLogsPage,
  CustomUserListSMSInboundLogsPage,
  WholesalerDashboardPage,
  WholesalerLeadCampaignsPage,
  WholesalerCreateCampaignPage,
  WholesalerApiKeysPage,
  WholesalerAnalyticsPage,
  WholesalerCostTrackingPage,
  WholesalerSettingsPage,
  WholesalerLoginPage,
} from "./LazyLoad";
import LogoutHandler from "Components/LogoutHandler";
import UserListEmailLogsTablePage from "Pages/User/List/UserListEmailLogsTablePage";
import UserViewEmailLogPage from "Pages/User/View/UserViewEmailLogPage";
import CustomUserListEmailLogsPage from "Pages/User/Custom/CustomUserListEmailLogsPage";

export const DynamicWrapper = ({ isAuthenticated, role, children }) => {
  if (!isAuthenticated) {
    return <PublicWrapper>{children}</PublicWrapper>;
  }
  if (isAuthenticated) {
    if (role === "user") {
      return <UserWrapper>{children}</UserWrapper>;
    }

    if (role === "admin") {
      return <AdminWrapper>{children}</AdminWrapper>;
    }

    if (role === "wholesaler") {
      return <WholesalerWrapper>{children}</WholesalerWrapper>;
    }
  }
};

export const NotFound = ({ isAuthenticated, role }) => {
  if (!isAuthenticated) {
    return (
      <PublicWrapper>
        <NotFoundPage />
      </PublicWrapper>
    );
  }
  if (isAuthenticated) {
    if (role === "user") {
      return (
        <UserWrapper>
          <NotFoundPage />
        </UserWrapper>
      );
    }

    if (role === "admin") {
      return (
        <AdminWrapper>
          <NotFoundPage />
        </AdminWrapper>
      );
    }

    if (role === "wholesaler") {
      return (
        <WholesalerWrapper>
          <NotFoundPage />
        </WholesalerWrapper>
      );
    }
  }
};

export default () => {
  const { state } = useContext(AuthContext);
  const {
    state: { isOpen },
    dispatch,
  } = useContext(GlobalContext);
  const [screenSize, setScreenSize] = useState(window.innerWidth);

  function setDimension(e) {
    if (e.currentTarget.innerWidth >= 1024) {
      toggleSideBar(true);
    } else toggleSideBar(false);
    setScreenSize(e.currentTarget.innerWidth);
  }

  // const toTop = () => {
  //   containerRef.current.scrollTo(0, 0);
  // };

  const toggleSideBar = (open) => {
    if (isOpen && screenSize < 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    } else if (!isOpen && screenSize >= 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    }
  };

  useEffect(() => {
    window.addEventListener("resize", setDimension);

    return () => {
      window.removeEventListener("resize", setDimension);
    };
  }, [screenSize]);

  return (
    <div
      onClick={() => {
        isOpen ? toggleSideBar(false) : null;
      }}
      className={`h-screen overflow-x-hidden bg-gradient-to-br from-[#FCF3F9] to-[#EAF8FB]`}
    >
      <Routes>
        <Route
          exact
          path="/admin/add-cms"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/add-cms"}
              element={
                <AdminWrapper>
                  <AddAdminCmsPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-email"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/add-email"}
              element={
                <AdminWrapper>
                  <AddAdminEmailPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/settings"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/settings"}
              element={
                <AdminWrapper>
                  <AdminListSettingsTablePage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/forwarding-numbers"
          element={
            <PrivateRoute
              access="admin"
              path={"admin/forwarding-numbers"}
              element={
                <AdminWrapper>
                  <AdminForwardingListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/reports"
          element={
            <PrivateRoute
              access="admin"
              path={"admin/reports"}
              element={
                <AdminWrapper>
                  <AdminReportsListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/invoices"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/invoices"}
              element={
                <AdminWrapper>
                  <AdminInvoicesListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/add-photo"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/add-photo"}
              element={
                <AdminWrapper>
                  <AddAdminPhotoPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/cms"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/cms"}
              element={
                <AdminWrapper>
                  <AdminCmsListPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/email"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/email"}
              element={
                <AdminWrapper>
                  <AdminEmailListPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/photo"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/photo"}
              element={
                <AdminWrapper>
                  <AdminPhotoListPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-cms/:id"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/edit-cms/:id"}
              element={
                <AdminWrapper>
                  <EditAdminCmsPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-email/:id"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/edit-email/:id"}
              element={
                <AdminWrapper>
                  <EditAdminEmailPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/magic-login/:role"
          element={
            <PublicRoute
              path="/magic-login/:role"
              element={
                <PublicWrapper>
                  <UserMagicLoginPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/magic-login/verify"
          element={
            <PublicRoute
              path="/magic-login/verify"
              element={
                <PublicWrapper>
                  <MagicLoginVerifyPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/login"
          element={
            <PublicRoute
              path="/admin/login"
              element={
                <PublicWrapper>
                  <CustomAdminLoginPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/signup"
          element={
            <PublicRoute
              path="/admin/signup"
              element={
                <PublicWrapper>
                  <CustomAdminSignUpPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/profile"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/profile"}
              element={
                <AdminWrapper>
                  <CustomAdminProfilePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/user/login"
          element={
            <PublicRoute
              path="/user/login"
              element={
                <PublicWrapper>
                  <CustomUserLoginPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/user/signup"
          element={
            <PublicRoute
              path="/user/signup"
              element={
                <PublicWrapper>
                  <CustomUserSignUpPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/onboarding"
          element={
            <PrivateRoute
              access="user"
              path={"/user/onboarding"}
              element={
                <UserWrapper>
                  <CustomUserOnboardingPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/settings"
          element={
            <PrivateRoute
              access="user"
              path={"/user/settings"}
              element={
                <UserWrapper>
                  <CustomUserTSettingsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/campaign-summary"
          element={
            <PrivateRoute
              access="user"
              path={"/user/campaign-summary"}
              element={
                <UserWrapper>
                  <CustomUserSummaryPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/voice"
          element={
            <PrivateRoute
              access="user"
              path={"/user/voice"}
              element={
                <UserWrapper>
                  <CustomUserVoicePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/sms"
          element={
            <PrivateRoute
              access="user"
              path={"/user/sms"}
              element={
                <UserWrapper>
                  <CustomUserSmsPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/video_generation"
          element={
            <PrivateRoute
              access="user"
              path={"/user/video_generation"}
              element={
                <UserWrapper>
                  <CustomUserVideoGeneration />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/video"
          element={
            <PrivateRoute
              access="user"
              path={"/user/video"}
              element={
                <UserWrapper>
                  <CustomUserVideoPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/embed"
          element={
            <PrivateRoute
              access="user"
              path={"/user/embed"}
              element={
                <UserWrapper>
                  <CustomUserEmbedPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/profile"
          element={
            <PrivateRoute
              access="user"
              path="/user/profile"
              element={
                <UserWrapper>
                  <CustomUserProfilePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/settings"
          element={
            <PrivateRoute
              access="user"
              path="/user/settings"
              element={
                <UserWrapper>
                  <CustomUserSettingsPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/add-profile"
          element={
            <PrivateRoute
              access="user"
              path="/user/add-profile"
              element={
                <UserWrapper>
                  <UserAddProfileTablePage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/edit-profile/:id"
          element={
            <PrivateRoute
              access="user"
              path="/user/edit-profile/:id"
              element={
                <UserWrapper>
                  <UserEditProfileTablePage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/view-profile/:id"
          element={
            <PrivateRoute
              access="user"
              path="/user/view-profile/:id"
              element={
                <UserWrapper>
                  <UserViewProfileTablePage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/chat"
          element={
            <PrivateRoute
              access="user"
              path={"/user/chat"}
              element={
                <UserWrapper>
                  <CustomUserChatPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/voice_list"
          element={
            <PrivateRoute
              access="user"
              path="/user/voice_list"
              element={
                <UserWrapper>
                  <UserListVoiceTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/assistants"
          element={
            <PrivateRoute
              access="user"
              path="/user/assistants"
              element={
                <UserWrapper>
                  <UserListAssistantsTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/numbers"
          element={
            <PrivateRoute
              access="user"
              path="/user/numbers"
              element={
                <UserWrapper>
                  <UserListNumbersTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/outbound_campaigns"
          element={
            <PrivateRoute
              access="user"
              path="/user/outbound_campaigns"
              element={
                <UserWrapper>
                  <CustomUserListOutboundCampaignsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/inbound_campaigns"
          element={
            <PrivateRoute
              access="user"
              path="/user/inbound_campaigns"
              element={
                <UserWrapper>
                  <CustomUserListInboundCampaignsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/outbound_call_logs"
          element={
            <PrivateRoute
              access="user"
              path="/user/outbound_call_logs"
              element={
                <UserWrapper>
                  <CustomUserListOutboundCallLogsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/email_logs"
          element={
            <PrivateRoute
              access="user"
              path="/user/email_logs"
              element={
                <UserWrapper>
                  <CustomUserListEmailLogsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/email_logs/:id"
          element={
            <PrivateRoute
              access="user"
              path="/user/email_logs/:id"
              element={
                <UserWrapper>
                  <UserViewEmailLogPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/sms_outbound_campaigns"
          element={
            <PrivateRoute
              access="user"
              path="/user/sms_outbound_campaigns"
              element={
                <UserWrapper>
                  <CustomUserListSMSOutboundCampaignsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/sms_inbound_campaigns"
          element={
            <PrivateRoute
              access="user"
              path="/user/sms_inbound_campaigns"
              element={
                <UserWrapper>
                  <CustomUserListSMSInboundCampaignsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/sms_outbound_logs"
          element={
            <PrivateRoute
              access="user"
              path="/user/sms_outbound_logs"
              element={
                <UserWrapper>
                  <CustomUserListSMSOutboundLogsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/sms_inbound_logs"
          element={
            <PrivateRoute
              access="user"
              path="/user/sms_inbound_logs"
              element={
                <UserWrapper>
                  <CustomUserListSMSInboundLogsPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/test_logs"
          element={
            <PrivateRoute
              access="user"
              path="/user/test_logs"
              element={
                <UserWrapper>
                  <CustomUserListTestCallLogsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/test_sms_logs"
          element={
            <PrivateRoute
              access="user"
              path="/user/test_sms_logs"
              element={
                <UserWrapper>
                  <CustomUserListTestSMSLogsPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/inbound_call_logs"
          element={
            <PrivateRoute
              access="user"
              path="/user/inbound_call_logs"
              element={
                <UserWrapper>
                  <CustomUserListInboundCallLogsPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/add-knowledge_bank"
          element={
            <PrivateRoute
              access="user"
              path="/user/add-knowledge_bank"
              element={
                <UserWrapper>
                  <UserAddKnowledgeBankTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/add-assistant"
          element={
            <PrivateRoute
              access="user"
              path="/user/add-assistant"
              element={
                <UserWrapper>
                  <UserAddAssistantsTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/add-number"
          element={
            <PrivateRoute
              access="user"
              path="/user/add-number"
              element={
                <UserWrapper>
                  <UserAddNumbersTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/add-campaign"
          element={
            <PrivateRoute
              access="user"
              path="/user/add-campaign"
              element={
                <UserWrapper>
                  <UserAddCampaignTablePage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/edit-voice/:id"
          element={
            <PrivateRoute
              access="user"
              path="/user/edit-voice/:id"
              element={
                <UserWrapper>
                  <UserEditVoiceTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/scripts"
          element={
            <PrivateRoute
              access="user"
              path="/user/scripts"
              element={
                <UserWrapper>
                  <UserListScriptsTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/add-script"
          element={
            <PrivateRoute
              access="user"
              path="/user/add-script"
              element={
                <UserWrapper>
                  <UserAddScriptsTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/edit-script/:id"
          element={
            <PrivateRoute
              access="user"
              path="/user/edit-script/:id"
              element={
                <UserWrapper>
                  <UserEditAssistantTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/edit-assistant/:id"
          element={
            <PrivateRoute
              access="user"
              path="/user/edit-assistant/:id"
              element={
                <UserWrapper>
                  <UserEditAssistantTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/edit-knowledge_bank/:id"
          element={
            <PrivateRoute
              access="user"
              path="/user/edit-knowledge_bank/:id"
              element={
                <UserWrapper>
                  <UserEditKnowledgeBankTablePage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/view-knowledge_bank/:id"
          element={
            <PrivateRoute
              access="user"
              path="/user/view-knowledge_bank/:id"
              element={
                <UserWrapper>
                  <UserViewKnowledgeBankTablePage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/integrations"
          element={
            <PrivateRoute
              access="user"
              path={"/user/integrations"}
              element={
                <UserWrapper>
                  <CustomUserIntegrationsPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/stripe_subscription"
          element={
            <PrivateRoute
              access="user"
              path="/user/stripe_subscription"
              element={
                <Suspense fallback={null}>
                  <UserWrapper>
                    <UserListStripeSubscriptionTablePage />
                  </UserWrapper>
                </Suspense>
              }
            />
          }
        />
        <Route
          exact
          path="/user/usage"
          element={
            <PrivateRoute
              access="user"
              path="/user/usage"
              element={
                <UserWrapper>
                  <UserListStripeSubscriptionTablePage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/usages"
          element={
            <PrivateRoute
              access="user"
              path="/user/usages"
              element={
                <UserWrapper>
                  <UserListProfileTablePage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/stripe_product"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/stripe_product"
              element={
                <AdminWrapper>
                  <AdminStripeProductsListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/stripe_price"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/stripe_price"
              element={
                <AdminWrapper>
                  <AdminStripePricesListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/stripe_subscription"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/stripe_subscription"
              element={
                <AdminWrapper>
                  <AdminListStripeSubscriptionTablePage />
                </AdminWrapper>
              }
            />
          }
        />

        {/*
        <Route
          exact
          path="/admin/knowledge_bank"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/knowledge_bank"
              element={
                <AdminWrapper>
                  <AdminListKnowledgeBankTablePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-knowledge_bank"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/add-knowledge_bank"
              element={
                <AdminWrapper>
                  <AdminAddKnowledgeBankTablePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-knowledge_bank/:id"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/edit-knowledge_bank/:id"
              element={
                <AdminWrapper>
                  <AdminEditKnowledgeBankTablePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/view-knowledge_bank/:id"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/view-knowledge_bank/:id"
              element={
                <AdminWrapper>
                  <AdminViewKnowledgeBankTablePage />
                </AdminWrapper>
              }
            />
          }
        /> */}

        <Route
          path="/user/forgot"
          element={
            <PublicRoute
              path="/user/forgot"
              element={
                <PublicWrapper>
                  <UserForgotPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/user/reset"
          element={
            <PublicRoute
              path="/user/reset"
              element={
                <PublicWrapper>
                  <UserResetPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/user/dashboard"
          element={
            <PrivateRoute
              access="user"
              path={"/user/dashboard"}
              element={
                <UserWrapper>
                  <UserDashboardPage />
                </UserWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/forgot"
          element={
            <PublicRoute
              path="/admin/forgot"
              element={
                <PublicWrapper>
                  <AdminForgotPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/reset"
          element={
            <PublicRoute
              path="/admin/reset"
              element={
                <PublicWrapper>
                  <AdminResetPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/dashboard"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/dashboard"}
              element={
                <AdminWrapper>
                  <AdminDashboardPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/customers"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/customers"
              element={
                <AdminWrapper>
                  <AdminUserListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/admins"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/admins"
              element={
                <AdminWrapper>
                  <AdminAdminListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/admins"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/admins"
              element={
                <AdminWrapper>
                  <AdminUserListPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/add-user"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/add-user"
              element={
                <AdminWrapper>
                  <AddAdminUserPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin/edit-user/:id"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/edit-user/:id"}
              element={
                <AdminWrapper>
                  <EditAdminUserPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/user/logout"
          element={
            <PrivateRoute
              access="user"
              path="/user/logout"
              element={
                <UserWrapper>
                  <LogoutHandler />
                </UserWrapper>
              }
            />
          }
        />

        {/* Wholesaler Routes */}
        <Route
          path="/wholesaler/login"
          element={
            <PublicRoute
              path="/wholesaler/login"
              element={
                <PublicWrapper>
                  <WholesalerLoginPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/wholesaler"
          element={
            <PrivateRoute
              access="wholesaler"
              path="/wholesaler"
              element={
                <WholesalerWrapper>
                  <WholesalerDashboardPage />
                </WholesalerWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/wholesaler/dashboard"
          element={
            <PrivateRoute
              access="wholesaler"
              path="/wholesaler/dashboard"
              element={
                <WholesalerWrapper>
                  <WholesalerDashboardPage />
                </WholesalerWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/wholesaler/lead-campaigns"
          element={
            <PrivateRoute
              access="wholesaler"
              path="/wholesaler/lead-campaigns"
              element={
                <WholesalerWrapper>
                  <WholesalerLeadCampaignsPage />
                </WholesalerWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/wholesaler/lead-campaigns/create"
          element={
            <PrivateRoute
              access="wholesaler"
              path="/wholesaler/lead-campaigns/create"
              element={
                <WholesalerWrapper>
                  <WholesalerCreateCampaignPage />
                </WholesalerWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/wholesaler/lead-campaigns/:id/edit"
          element={
            <PrivateRoute
              access="wholesaler"
              path="/wholesaler/lead-campaigns/:id/edit"
              element={
                <WholesalerWrapper>
                  <WholesalerCreateCampaignPage />
                </WholesalerWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/wholesaler/api-keys"
          element={
            <PrivateRoute
              access="wholesaler"
              path="/wholesaler/api-keys"
              element={
                <WholesalerWrapper>
                  <WholesalerApiKeysPage />
                </WholesalerWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/wholesaler/analytics"
          element={
            <PrivateRoute
              access="wholesaler"
              path="/wholesaler/analytics"
              element={
                <WholesalerWrapper>
                  <WholesalerAnalyticsPage />
                </WholesalerWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/wholesaler/cost-tracking"
          element={
            <PrivateRoute
              access="wholesaler"
              path="/wholesaler/cost-tracking"
              element={
                <WholesalerWrapper>
                  <WholesalerCostTrackingPage />
                </WholesalerWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/wholesaler/settings"
          element={
            <PrivateRoute
              access="wholesaler"
              path="/wholesaler/settings"
              element={
                <WholesalerWrapper>
                  <WholesalerSettingsPage />
                </WholesalerWrapper>
              }
            />
          }
        />

        <Route
          path={"*"}
          element={<PublicRoute path={"*"} element={<NotFound {...state} />} />}
        />
      </Routes>
      <SessionExpiredModal />
      <SnackBar />
    </div>
  );
};
