# Lead Campaign Frontend Implementation Guide

## Overview
This guide provides comprehensive documentation for building the frontend interface for the Lead Campaign & Voice AI system. The frontend should support wholesaler users in managing lead campaigns, monitoring performance, and configuring settings.

## System Architecture

### User Roles & Access Control
```javascript
// Role-based routing and permissions
const USER_ROLES = {
  user: ['campaigns:read', 'campaigns:create', 'calls:read'],
  admin: ['*'], // All permissions
  wholesaler: [
    'lead_campaigns:read', 'lead_campaigns:create', 'lead_campaigns:update',
    'lead_ingestion:create', 'lead_analytics:read', 'api_keys:manage',
    'ringba:configure', 'reporting:read', 'cost_tracking:read'
  ]
};
```

### Authentication Flow
```javascript
// Login API integration
const loginUser = async (email, password) => {
  const response = await fetch('/v3/api/custom/voiceoutreach/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  
  const data = await response.json();
  if (data.error === false) {
    localStorage.setItem('auth_token', data.token);
    localStorage.setItem('user_role', data.role);
    localStorage.setItem('user_id', data.user_id);
    return { success: true, role: data.role };
  }
  return { success: false, error: data.message };
};
```

## Page Structure & Navigation

### 1. Dashboard Layout
```
├── Header
│   ├── Logo
│   ├── Navigation Menu
│   ├── User Profile Dropdown
│   └── Notifications
├── Sidebar (Wholesaler Role)
│   ├── Dashboard
│   ├── Lead Campaigns
│   ├── Analytics & Reports
│   ├── API Management
│   ├── Cost Tracking
│   └── Settings
└── Main Content Area
```

### 2. Navigation Menu Structure
```javascript
const navigationMenu = {
  wholesaler: [
    { path: '/dashboard', label: 'Dashboard', icon: 'dashboard' },
    { path: '/lead-campaigns', label: 'Lead Campaigns', icon: 'campaigns' },
    { path: '/analytics', label: 'Analytics', icon: 'chart' },
    { path: '/api-keys', label: 'API Keys', icon: 'key' },
    { path: '/cost-tracking', label: 'Cost Tracking', icon: 'dollar' },
    { path: '/settings', label: 'Settings', icon: 'settings' }
  ],
  user: [
    { path: '/dashboard', label: 'Dashboard', icon: 'dashboard' },
    { path: '/campaigns', label: 'Campaigns', icon: 'campaigns' },
    { path: '/calls', label: 'Calls', icon: 'phone' }
  ]
};
```

## Core Pages Implementation

### 1. Login Page (`/login`)

#### UI Components
```jsx
const LoginPage = () => {
  const [credentials, setCredentials] = useState({ email: '', password: '' });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    const result = await loginUser(credentials.email, credentials.password);
    if (result.success) {
      // Redirect based on role
      if (result.role === 'wholesaler') {
        window.location.href = '/lead-campaigns';
      } else {
        window.location.href = '/dashboard';
      }
    } else {
      setError(result.error);
    }
    setLoading(false);
  };

  return (
    <div className="login-container">
      <form onSubmit={handleLogin}>
        <input 
          type="email" 
          placeholder="Email"
          value={credentials.email}
          onChange={(e) => setCredentials({...credentials, email: e.target.value})}
          required 
        />
        <input 
          type="password" 
          placeholder="Password"
          value={credentials.password}
          onChange={(e) => setCredentials({...credentials, password: e.target.value})}
          required 
        />
        <button type="submit" disabled={loading}>
          {loading ? 'Logging in...' : 'Login'}
        </button>
        {error && <div className="error">{error}</div>}
      </form>
    </div>
  );
};
```

### 2. Lead Campaigns Dashboard (`/lead-campaigns`)

#### API Integration
```javascript
// Fetch campaigns
const fetchCampaigns = async (filters = {}) => {
  const params = new URLSearchParams(filters);
  const response = await fetch(`/v3/api/custom/voiceoutreach/user/lead-campaigns?${params}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
    }
  });
  return await response.json();
};

// Campaign status update
const updateCampaignStatus = async (campaignId, status) => {
  const response = await fetch(`/v3/api/custom/voiceoutreach/user/lead-campaigns/${campaignId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ status })
  });
  return await response.json();
};
```

#### UI Components
```jsx
const CampaignsDashboard = () => {
  const [campaigns, setCampaigns] = useState([]);
  const [filters, setFilters] = useState({ status: '', source_type: '' });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCampaigns();
  }, [filters]);

  const loadCampaigns = async () => {
    setLoading(true);
    const result = await fetchCampaigns(filters);
    if (!result.error) {
      setCampaigns(result.data);
    }
    setLoading(false);
  };

  return (
    <div className="campaigns-dashboard">
      <div className="header">
        <h1>Lead Campaigns</h1>
        <button onClick={() => window.location.href = '/lead-campaigns/create'}>
          Create New Campaign
        </button>
      </div>
      
      <div className="filters">
        <select 
          value={filters.status} 
          onChange={(e) => setFilters({...filters, status: e.target.value})}
        >
          <option value="">All Statuses</option>
          <option value="active">Active</option>
          <option value="paused">Paused</option>
          <option value="completed">Completed</option>
        </select>
        
        <select 
          value={filters.source_type} 
          onChange={(e) => setFilters({...filters, source_type: e.target.value})}
        >
          <option value="">All Types</option>
          <option value="auto_insurance">Auto Insurance</option>
          <option value="health_insurance">Health Insurance</option>
          <option value="life_insurance">Life Insurance</option>
          <option value="home_insurance">Home Insurance</option>
        </select>
      </div>

      <div className="campaigns-grid">
        {campaigns.map(campaign => (
          <CampaignCard key={campaign.id} campaign={campaign} onUpdate={loadCampaigns} />
        ))}
      </div>
    </div>
  );
};
```

### 3. Create/Edit Campaign Page (`/lead-campaigns/create` or `/lead-campaigns/:id/edit`)

#### Form Structure
```jsx
const CampaignForm = ({ campaignId = null }) => {
  const [formData, setFormData] = useState({
    name: '',
    source_slug: '',
    source_type: 'auto_insurance',
    assistant_id: '',
    script_id: '',
    model_selection: 'llama-3.3-70b',
    retry_settings: {
      max_retries: 3,
      retry_intervals: [1, 4, 24],
      retry_conditions: ['no_answer', 'busy']
    },
    schedule_windows: {
      timezone: 'America/New_York',
      windows: [{
        days: [1, 2, 3, 4, 5],
        start: '09:00',
        end: '17:00'
      }]
    },
    ringba_campaign_id: '',
    ringba_api_settings: {
      api_key: '',
      campaign_token: '',
      endpoint: ''
    },
    transfer_phone_numbers: []
  });

  const [assistants, setAssistants] = useState([]);
  const [scripts, setScripts] = useState([]);

  // Load assistants and scripts
  useEffect(() => {
    loadAssistants();
    loadScripts();
    if (campaignId) {
      loadCampaign(campaignId);
    }
  }, [campaignId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const url = campaignId 
      ? `/v3/api/custom/voiceoutreach/user/lead-campaigns/${campaignId}`
      : '/v3/api/custom/voiceoutreach/user/lead-campaigns';
    
    const method = campaignId ? 'PUT' : 'POST';
    
    const response = await fetch(url, {
      method,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();
    if (!result.error) {
      window.location.href = '/lead-campaigns';
    }
  };

  return (
    <form onSubmit={handleSubmit} className="campaign-form">
      {/* Basic Information */}
      <section className="form-section">
        <h3>Basic Information</h3>
        <input
          type="text"
          placeholder="Campaign Name"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
          required
        />
        
        <input
          type="text"
          placeholder="Source Slug"
          value={formData.source_slug}
          onChange={(e) => setFormData({...formData, source_slug: e.target.value})}
          required
        />
        
        <select
          value={formData.source_type}
          onChange={(e) => setFormData({...formData, source_type: e.target.value})}
          required
        >
          <option value="auto_insurance">Auto Insurance</option>
          <option value="health_insurance">Health Insurance</option>
          <option value="life_insurance">Life Insurance</option>
          <option value="home_insurance">Home Insurance</option>
          <option value="general">General</option>
        </select>
      </section>

      {/* AI Configuration */}
      <section className="form-section">
        <h3>AI Configuration</h3>
        <select
          value={formData.assistant_id}
          onChange={(e) => setFormData({...formData, assistant_id: e.target.value})}
          required
        >
          <option value="">Select Assistant</option>
          {assistants.map(assistant => (
            <option key={assistant.id} value={assistant.id}>
              {assistant.assistant_name}
            </option>
          ))}
        </select>
        
        <select
          value={formData.script_id}
          onChange={(e) => setFormData({...formData, script_id: e.target.value})}
        >
          <option value="">Select Script (Optional)</option>
          {scripts.map(script => (
            <option key={script.id} value={script.id}>
              {script.name}
            </option>
          ))}
        </select>
        
        <select
          value={formData.model_selection}
          onChange={(e) => setFormData({...formData, model_selection: e.target.value})}
        >
          <option value="llama-3.3-70b">Llama 3.3 70B</option>
          <option value="claude-3-sonnet">Claude 3 Sonnet</option>
          <option value="gpt-4">GPT-4</option>
        </select>
      </section>

      {/* Schedule Windows */}
      <ScheduleWindowsEditor 
        scheduleWindows={formData.schedule_windows}
        onChange={(windows) => setFormData({...formData, schedule_windows: windows})}
      />

      {/* Retry Settings */}
      <RetrySettingsEditor 
        retrySettings={formData.retry_settings}
        onChange={(settings) => setFormData({...formData, retry_settings: settings})}
      />

      {/* Ringba Configuration */}
      <RingbaConfigEditor 
        ringbaSettings={formData.ringba_api_settings}
        campaignId={formData.ringba_campaign_id}
        transferNumbers={formData.transfer_phone_numbers}
        onChange={(config) => setFormData({...formData, ...config})}
      />

      <button type="submit">
        {campaignId ? 'Update Campaign' : 'Create Campaign'}
      </button>
    </form>
  );
};
```

### 4. Campaign Analytics Page (`/lead-campaigns/:id/analytics`)

#### API Integration
```javascript
// Fetch campaign analytics
const fetchCampaignAnalytics = async (campaignId, dateRange = {}) => {
  const params = new URLSearchParams({
    start_date: dateRange.start || moment().subtract(30, 'days').format('YYYY-MM-DD'),
    end_date: dateRange.end || moment().format('YYYY-MM-DD'),
    ...dateRange.filters
  });

  const response = await fetch(
    `/v3/api/custom/voiceoutreach/user/lead-campaigns/${campaignId}/analytics?${params}`,
    {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    }
  );
  return await response.json();
};

// Fetch source performance
const fetchSourcePerformance = async (campaignId, dateRange = {}) => {
  const params = new URLSearchParams(dateRange);
  const response = await fetch(
    `/v3/api/custom/voiceoutreach/user/lead-campaigns/${campaignId}/sources?${params}`,
    {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    }
  );
  return await response.json();
};
```

#### UI Components
```jsx
const CampaignAnalytics = ({ campaignId }) => {
  const [analytics, setAnalytics] = useState(null);
  const [sourcePerformance, setSourcePerformance] = useState([]);
  const [dateRange, setDateRange] = useState({
    start: moment().subtract(30, 'days').format('YYYY-MM-DD'),
    end: moment().format('YYYY-MM-DD')
  });

  useEffect(() => {
    loadAnalytics();
    loadSourcePerformance();
  }, [campaignId, dateRange]);

  const loadAnalytics = async () => {
    const result = await fetchCampaignAnalytics(campaignId, dateRange);
    if (!result.error) {
      setAnalytics(result.data);
    }
  };

  return (
    <div className="campaign-analytics">
      <div className="header">
        <h1>Campaign Analytics</h1>
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
        />
      </div>

      {analytics && (
        <>
          {/* Key Metrics Cards */}
          <div className="metrics-grid">
            <MetricCard
              title="Total Leads"
              value={analytics.overview.total_leads}
              change={"+12%"}
              trend="up"
            />
            <MetricCard
              title="Calls Made"
              value={analytics.overview.total_calls}
              change={"+8%"}
              trend="up"
            />
            <MetricCard
              title="Answer Rate"
              value={`${analytics.conversion_rates.answer_rate}%`}
              change={"+2.3%"}
              trend="up"
            />
            <MetricCard
              title="Transfer Rate"
              value={`${analytics.conversion_rates.transfer_rate}%`}
              change={"-1.2%"}
              trend="down"
            />
            <MetricCard
              title="Total Revenue"
              value={`$${analytics.overview.total_revenue.toFixed(2)}`}
              change={"+15%"}
              trend="up"
            />
            <MetricCard
              title="Cost Per Lead"
              value={`$${analytics.performance_metrics.cost_per_lead}`}
              change={"-5%"}
              trend="down"
            />
          </div>

          {/* Conversion Funnel */}
          <div className="conversion-funnel">
            <h3>Conversion Funnel</h3>
            <FunnelChart data={[
              { stage: 'Leads Received', value: analytics.overview.total_leads },
              { stage: 'Calls Made', value: analytics.overview.total_calls },
              { stage: 'Calls Answered', value: analytics.overview.human_answers },
              { stage: 'Successful Transfers', value: analytics.overview.successful_transfers }
            ]} />
          </div>

          {/* Daily Trends Chart */}
          <div className="daily-trends">
            <h3>Daily Performance Trends</h3>
            <LineChart
              data={analytics.daily_breakdown}
              xKey="date"
              lines={[
                { key: 'leads_received', label: 'Leads Received', color: '#3b82f6' },
                { key: 'calls_made', label: 'Calls Made', color: '#10b981' },
                { key: 'calls_transferred', label: 'Transfers', color: '#f59e0b' },
                { key: 'daily_revenue', label: 'Revenue ($)', color: '#ef4444' }
              ]}
            />
          </div>

          {/* Source Performance Table */}
          <div className="source-performance">
            <h3>Source Performance</h3>
            <SourcePerformanceTable sources={sourcePerformance} />
          </div>
        </>
      )}
    </div>
  );
};
```

### 5. API Key Management Page (`/api-keys`)

#### API Integration
```javascript
// Fetch API keys
const fetchApiKeys = async () => {
  const response = await fetch('/v3/api/custom/voiceoutreach/user/api-keys', {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
    }
  });
  return await response.json();
};

// Generate new API key
const generateApiKey = async (campaignId, keyName) => {
  const response = await fetch('/v3/api/custom/voiceoutreach/user/api-keys', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ campaign_id: campaignId, key_name: keyName })
  });
  return await response.json();
};

// Revoke API key
const revokeApiKey = async (keyId) => {
  const response = await fetch(`/v3/api/custom/voiceoutreach/user/api-keys/${keyId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
    }
  });
  return await response.json();
};
```

#### UI Components
```jsx
const ApiKeyManagement = () => {
  const [apiKeys, setApiKeys] = useState([]);
  const [campaigns, setCampaigns] = useState([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newKeyData, setNewKeyData] = useState({ campaign_id: '', key_name: '' });

  useEffect(() => {
    loadApiKeys();
    loadCampaigns();
  }, []);

  const handleCreateKey = async (e) => {
    e.preventDefault();
    const result = await generateApiKey(newKeyData.campaign_id, newKeyData.key_name);
    if (!result.error) {
      setApiKeys([...apiKeys, result.data]);
      setShowCreateForm(false);
      setNewKeyData({ campaign_id: '', key_name: '' });
    }
  };

  const handleRevokeKey = async (keyId) => {
    if (confirm('Are you sure you want to revoke this API key?')) {
      const result = await revokeApiKey(keyId);
      if (!result.error) {
        setApiKeys(apiKeys.filter(key => key.id !== keyId));
      }
    }
  };

  return (
    <div className="api-key-management">
      <div className="header">
        <h1>API Key Management</h1>
        <button onClick={() => setShowCreateForm(true)}>
          Generate New API Key
        </button>
      </div>

      {showCreateForm && (
        <div className="create-key-form">
          <form onSubmit={handleCreateKey}>
            <select
              value={newKeyData.campaign_id}
              onChange={(e) => setNewKeyData({...newKeyData, campaign_id: e.target.value})}
              required
            >
              <option value="">Select Campaign</option>
              {campaigns.map(campaign => (
                <option key={campaign.id} value={campaign.id}>
                  {campaign.name}
                </option>
              ))}
            </select>

            <input
              type="text"
              placeholder="Key Name"
              value={newKeyData.key_name}
              onChange={(e) => setNewKeyData({...newKeyData, key_name: e.target.value})}
              required
            />

            <button type="submit">Generate Key</button>
            <button type="button" onClick={() => setShowCreateForm(false)}>
              Cancel
            </button>
          </form>
        </div>
      )}

      <div className="api-keys-table">
        <table>
          <thead>
            <tr>
              <th>Key Name</th>
              <th>Campaign</th>
              <th>API Key</th>
              <th>Status</th>
              <th>Created</th>
              <th>Last Used</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {apiKeys.map(key => (
              <tr key={key.id}>
                <td>{key.key_name}</td>
                <td>{key.campaign_name}</td>
                <td>
                  <code className="api-key">
                    {key.api_key.substring(0, 20)}...
                    <button onClick={() => copyToClipboard(key.api_key)}>
                      Copy
                    </button>
                  </code>
                </td>
                <td>
                  <span className={`status ${key.status}`}>
                    {key.status}
                  </span>
                </td>
                <td>{formatDate(key.created_at)}</td>
                <td>{key.last_used_at ? formatDate(key.last_used_at) : 'Never'}</td>
                <td>
                  <button
                    onClick={() => handleRevokeKey(key.id)}
                    className="revoke-btn"
                  >
                    Revoke
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Usage Instructions */}
      <div className="usage-instructions">
        <h3>API Usage Instructions</h3>
        <div className="code-example">
          <h4>Lead Submission Example:</h4>
          <pre>{`curl -X POST https://your-domain.com/v3/api/custom/voiceoutreach/leads/webhook/leads \\
  -H "x-api-key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '[{
    "phone_number": "+15551234567",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "zip_code": "12345",
    "gender": "male",
    "currently_insured": true,
    "tcpa_call_consent": true,
    "vehicle_count": 2,
    "credit_score": "excellent"
  }]'`}</pre>
        </div>
      </div>
    </div>
  );
};
```

### 6. Settings Page (`/settings`)

#### UI Components
```jsx
const SettingsPage = () => {
  const [settings, setSettings] = useState({
    profile: {
      company_name: '',
      contact_email: '',
      phone_number: '',
      timezone: 'America/New_York'
    },
    notifications: {
      email_alerts: true,
      sms_alerts: false,
      daily_reports: true,
      campaign_status_changes: true
    },
    defaults: {
      max_concurrent_calls: 10,
      default_retry_attempts: 3,
      default_schedule_start: '09:00',
      default_schedule_end: '17:00'
    },
    integrations: {
      ringba_api_key: '',
      twilio_account_sid: '',
      deepgram_api_key: ''
    }
  });

  const handleSave = async (section, data) => {
    const response = await fetch('/v3/api/custom/voiceoutreach/user/settings', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ section, data })
    });

    const result = await response.json();
    if (!result.error) {
      setSettings({...settings, [section]: data});
    }
  };

  return (
    <div className="settings-page">
      <h1>Settings</h1>

      <div className="settings-sections">
        {/* Profile Settings */}
        <SettingsSection
          title="Profile Information"
          data={settings.profile}
          onSave={(data) => handleSave('profile', data)}
        >
          <input
            type="text"
            label="Company Name"
            value={settings.profile.company_name}
            onChange={(value) => setSettings({
              ...settings,
              profile: {...settings.profile, company_name: value}
            })}
          />
          {/* More profile fields */}
        </SettingsSection>

        {/* Notification Settings */}
        <SettingsSection
          title="Notifications"
          data={settings.notifications}
          onSave={(data) => handleSave('notifications', data)}
        >
          <CheckboxGroup
            options={[
              { key: 'email_alerts', label: 'Email Alerts' },
              { key: 'sms_alerts', label: 'SMS Alerts' },
              { key: 'daily_reports', label: 'Daily Reports' },
              { key: 'campaign_status_changes', label: 'Campaign Status Changes' }
            ]}
            values={settings.notifications}
            onChange={(values) => setSettings({
              ...settings,
              notifications: values
            })}
          />
        </SettingsSection>

        {/* Default Settings */}
        <SettingsSection
          title="Default Campaign Settings"
          data={settings.defaults}
          onSave={(data) => handleSave('defaults', data)}
        >
          {/* Default settings form fields */}
        </SettingsSection>

        {/* Integration Settings */}
        <SettingsSection
          title="Integrations"
          data={settings.integrations}
          onSave={(data) => handleSave('integrations', data)}
        >
          <input
            type="password"
            label="Ringba API Key"
            value={settings.integrations.ringba_api_key}
            onChange={(value) => setSettings({
              ...settings,
              integrations: {...settings.integrations, ringba_api_key: value}
            })}
          />
          {/* More integration fields */}
        </SettingsSection>
      </div>
    </div>
  );
};
```

## Specialized Components

### 1. Schedule Windows Editor
```jsx
const ScheduleWindowsEditor = ({ scheduleWindows, onChange }) => {
  const [windows, setWindows] = useState(scheduleWindows.windows || []);
  const [timezone, setTimezone] = useState(scheduleWindows.timezone || 'America/New_York');

  const addWindow = () => {
    const newWindow = {
      days: [1, 2, 3, 4, 5], // Monday to Friday
      start: '09:00',
      end: '17:00'
    };
    const updatedWindows = [...windows, newWindow];
    setWindows(updatedWindows);
    onChange({ timezone, windows: updatedWindows });
  };

  const updateWindow = (index, field, value) => {
    const updatedWindows = windows.map((window, i) =>
      i === index ? { ...window, [field]: value } : window
    );
    setWindows(updatedWindows);
    onChange({ timezone, windows: updatedWindows });
  };

  const removeWindow = (index) => {
    const updatedWindows = windows.filter((_, i) => i !== index);
    setWindows(updatedWindows);
    onChange({ timezone, windows: updatedWindows });
  };

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="schedule-windows-editor">
      <h3>Schedule Windows</h3>

      <div className="timezone-selector">
        <label>Timezone:</label>
        <select
          value={timezone}
          onChange={(e) => {
            setTimezone(e.target.value);
            onChange({ timezone: e.target.value, windows });
          }}
        >
          <option value="America/New_York">Eastern Time</option>
          <option value="America/Chicago">Central Time</option>
          <option value="America/Denver">Mountain Time</option>
          <option value="America/Los_Angeles">Pacific Time</option>
        </select>
      </div>

      {windows.map((window, index) => (
        <div key={index} className="schedule-window">
          <div className="days-selector">
            <label>Days:</label>
            <div className="day-checkboxes">
              {dayNames.map((day, dayIndex) => (
                <label key={dayIndex}>
                  <input
                    type="checkbox"
                    checked={window.days.includes(dayIndex)}
                    onChange={(e) => {
                      const newDays = e.target.checked
                        ? [...window.days, dayIndex]
                        : window.days.filter(d => d !== dayIndex);
                      updateWindow(index, 'days', newDays);
                    }}
                  />
                  {day}
                </label>
              ))}
            </div>
          </div>

          <div className="time-range">
            <label>Start Time:</label>
            <input
              type="time"
              value={window.start}
              onChange={(e) => updateWindow(index, 'start', e.target.value)}
            />

            <label>End Time:</label>
            <input
              type="time"
              value={window.end}
              onChange={(e) => updateWindow(index, 'end', e.target.value)}
            />
          </div>

          <button onClick={() => removeWindow(index)}>Remove</button>
        </div>
      ))}

      <button onClick={addWindow}>Add Schedule Window</button>
    </div>
  );
};
```

### 2. Retry Settings Editor
```jsx
const RetrySettingsEditor = ({ retrySettings, onChange }) => {
  const [settings, setSettings] = useState({
    max_retries: retrySettings.max_retries || 3,
    retry_intervals: retrySettings.retry_intervals || [1, 4, 24],
    retry_conditions: retrySettings.retry_conditions || ['no_answer', 'busy']
  });

  const updateSettings = (field, value) => {
    const newSettings = { ...settings, [field]: value };
    setSettings(newSettings);
    onChange(newSettings);
  };

  const updateInterval = (index, value) => {
    const newIntervals = [...settings.retry_intervals];
    newIntervals[index] = parseInt(value);
    updateSettings('retry_intervals', newIntervals);
  };

  const addInterval = () => {
    updateSettings('retry_intervals', [...settings.retry_intervals, 24]);
  };

  const removeInterval = (index) => {
    const newIntervals = settings.retry_intervals.filter((_, i) => i !== index);
    updateSettings('retry_intervals', newIntervals);
  };

  return (
    <div className="retry-settings-editor">
      <h3>Retry Settings</h3>

      <div className="max-retries">
        <label>Maximum Retry Attempts:</label>
        <input
          type="number"
          min="0"
          max="10"
          value={settings.max_retries}
          onChange={(e) => updateSettings('max_retries', parseInt(e.target.value))}
        />
      </div>

      <div className="retry-intervals">
        <label>Retry Intervals (hours):</label>
        {settings.retry_intervals.map((interval, index) => (
          <div key={index} className="interval-input">
            <span>Attempt {index + 1}:</span>
            <input
              type="number"
              min="1"
              value={interval}
              onChange={(e) => updateInterval(index, e.target.value)}
            />
            <span>hours</span>
            {settings.retry_intervals.length > 1 && (
              <button onClick={() => removeInterval(index)}>Remove</button>
            )}
          </div>
        ))}
        <button onClick={addInterval}>Add Interval</button>
      </div>

      <div className="retry-conditions">
        <label>Retry Conditions:</label>
        <div className="condition-checkboxes">
          {['no_answer', 'busy', 'failed', 'voicemail'].map(condition => (
            <label key={condition}>
              <input
                type="checkbox"
                checked={settings.retry_conditions.includes(condition)}
                onChange={(e) => {
                  const newConditions = e.target.checked
                    ? [...settings.retry_conditions, condition]
                    : settings.retry_conditions.filter(c => c !== condition);
                  updateSettings('retry_conditions', newConditions);
                }}
              />
              {condition.replace('_', ' ').toUpperCase()}
            </label>
          ))}
        </div>
      </div>
    </div>
  );
};
```

### 3. Ringba Configuration Editor
```jsx
const RingbaConfigEditor = ({ ringbaSettings, campaignId, transferNumbers, onChange }) => {
  const [config, setConfig] = useState({
    ringba_campaign_id: campaignId || '',
    ringba_api_settings: ringbaSettings || {
      api_key: '',
      campaign_token: '',
      endpoint: 'https://api.ringba.com/v2'
    },
    transfer_phone_numbers: transferNumbers || []
  });

  const [testResult, setTestResult] = useState(null);
  const [testing, setTesting] = useState(false);

  const updateConfig = (field, value) => {
    const newConfig = { ...config, [field]: value };
    setConfig(newConfig);
    onChange(newConfig);
  };

  const updateApiSettings = (field, value) => {
    const newSettings = { ...config.ringba_api_settings, [field]: value };
    updateConfig('ringba_api_settings', newSettings);
  };

  const addTransferNumber = () => {
    const newNumbers = [...config.transfer_phone_numbers, {
      number: '',
      priority: config.transfer_phone_numbers.length + 1,
      active: true
    }];
    updateConfig('transfer_phone_numbers', newNumbers);
  };

  const updateTransferNumber = (index, field, value) => {
    const newNumbers = config.transfer_phone_numbers.map((num, i) =>
      i === index ? { ...num, [field]: value } : num
    );
    updateConfig('transfer_phone_numbers', newNumbers);
  };

  const removeTransferNumber = (index) => {
    const newNumbers = config.transfer_phone_numbers.filter((_, i) => i !== index);
    updateConfig('transfer_phone_numbers', newNumbers);
  };

  const testConnection = async () => {
    setTesting(true);
    try {
      const response = await fetch('/v3/api/custom/voiceoutreach/user/ringba/test', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config.ringba_api_settings)
      });

      const result = await response.json();
      setTestResult(result);
    } catch (error) {
      setTestResult({ success: false, error: error.message });
    }
    setTesting(false);
  };

  return (
    <div className="ringba-config-editor">
      <h3>Ringba Integration</h3>

      <div className="api-settings">
        <h4>API Configuration</h4>

        <input
          type="text"
          placeholder="Campaign ID"
          value={config.ringba_campaign_id}
          onChange={(e) => updateConfig('ringba_campaign_id', e.target.value)}
        />

        <input
          type="password"
          placeholder="API Key"
          value={config.ringba_api_settings.api_key}
          onChange={(e) => updateApiSettings('api_key', e.target.value)}
        />

        <input
          type="text"
          placeholder="Campaign Token"
          value={config.ringba_api_settings.campaign_token}
          onChange={(e) => updateApiSettings('campaign_token', e.target.value)}
        />

        <input
          type="url"
          placeholder="API Endpoint"
          value={config.ringba_api_settings.endpoint}
          onChange={(e) => updateApiSettings('endpoint', e.target.value)}
        />

        <button onClick={testConnection} disabled={testing}>
          {testing ? 'Testing...' : 'Test Connection'}
        </button>

        {testResult && (
          <div className={`test-result ${testResult.success ? 'success' : 'error'}`}>
            {testResult.success ? '✅ Connection successful' : `❌ ${testResult.error}`}
          </div>
        )}
      </div>

      <div className="transfer-numbers">
        <h4>Transfer Phone Numbers</h4>

        {config.transfer_phone_numbers.map((number, index) => (
          <div key={index} className="transfer-number">
            <input
              type="tel"
              placeholder="+1234567890"
              value={number.number}
              onChange={(e) => updateTransferNumber(index, 'number', e.target.value)}
            />

            <input
              type="number"
              placeholder="Priority"
              min="1"
              value={number.priority}
              onChange={(e) => updateTransferNumber(index, 'priority', parseInt(e.target.value))}
            />

            <label>
              <input
                type="checkbox"
                checked={number.active}
                onChange={(e) => updateTransferNumber(index, 'active', e.target.checked)}
              />
              Active
            </label>

            <button onClick={() => removeTransferNumber(index)}>Remove</button>
          </div>
        ))}

        <button onClick={addTransferNumber}>Add Transfer Number</button>
      </div>
    </div>
  );
};
```

## Data Visualization Components

### 1. Metric Cards
```jsx
const MetricCard = ({ title, value, change, trend, icon }) => (
  <div className="metric-card">
    <div className="metric-header">
      <span className="metric-title">{title}</span>
      {icon && <i className={`icon ${icon}`}></i>}
    </div>
    <div className="metric-value">{value}</div>
    {change && (
      <div className={`metric-change ${trend}`}>
        <span className={`trend-icon ${trend}`}>
          {trend === 'up' ? '↗' : '↘'}
        </span>
        {change}
      </div>
    )}
  </div>
);
```

### 2. Line Chart Component
```jsx
const LineChart = ({ data, xKey, lines, height = 300 }) => {
  const chartRef = useRef();

  useEffect(() => {
    if (!data || data.length === 0) return;

    // Use Chart.js or similar library
    const ctx = chartRef.current.getContext('2d');
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: data.map(item => item[xKey]),
        datasets: lines.map(line => ({
          label: line.label,
          data: data.map(item => item[line.key]),
          borderColor: line.color,
          backgroundColor: line.color + '20',
          tension: 0.4
        }))
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }, [data, lines]);

  return (
    <div className="line-chart">
      <canvas ref={chartRef} height={height}></canvas>
    </div>
  );
};
```

### 3. Source Performance Table
```jsx
const SourcePerformanceTable = ({ sources }) => {
  const [sortField, setSortField] = useState('total_revenue');
  const [sortDirection, setSortDirection] = useState('desc');

  const sortedSources = [...sources].sort((a, b) => {
    const aValue = a.metrics[sortField] || 0;
    const bValue = b.metrics[sortField] || 0;
    return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
  });

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  return (
    <div className="source-performance-table">
      <table>
        <thead>
          <tr>
            <th onClick={() => handleSort('source_name')}>Source</th>
            <th onClick={() => handleSort('total_leads')}>Leads</th>
            <th onClick={() => handleSort('total_calls')}>Calls</th>
            <th onClick={() => handleSort('human_answers')}>Answers</th>
            <th onClick={() => handleSort('successful_transfers')}>Transfers</th>
            <th onClick={() => handleSort('total_revenue')}>Revenue</th>
            <th onClick={() => handleSort('total_costs')}>Costs</th>
            <th onClick={() => handleSort('profit')}>Profit</th>
            <th>ROI</th>
          </tr>
        </thead>
        <tbody>
          {sortedSources.map(source => (
            <tr key={source.source_id}>
              <td>{source.source_name}</td>
              <td>{source.metrics.total_leads}</td>
              <td>{source.metrics.total_calls}</td>
              <td>{source.metrics.human_answers}</td>
              <td>{source.metrics.successful_transfers}</td>
              <td>${source.metrics.total_revenue.toFixed(2)}</td>
              <td>${source.metrics.total_costs.toFixed(2)}</td>
              <td className={source.metrics.profit >= 0 ? 'profit' : 'loss'}>
                ${source.metrics.profit.toFixed(2)}
              </td>
              <td className={source.economics.roi >= 0 ? 'positive' : 'negative'}>
                {source.economics.roi}%
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
```

## Utility Functions

### 1. API Helper Functions
```javascript
// Base API configuration
const API_BASE = '/v3/api/custom/voiceoutreach';

const apiRequest = async (endpoint, options = {}) => {
  const token = localStorage.getItem('auth_token');
  const defaultHeaders = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  const response = await fetch(`${API_BASE}${endpoint}`, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers
    }
  });

  if (!response.ok) {
    if (response.status === 401) {
      // Redirect to login
      window.location.href = '/login';
      return;
    }
    throw new Error(`API Error: ${response.status}`);
  }

  return await response.json();
};

// Specific API functions
export const leadCampaignAPI = {
  list: (filters) => apiRequest(`/user/lead-campaigns?${new URLSearchParams(filters)}`),
  get: (id) => apiRequest(`/user/lead-campaigns/${id}`),
  create: (data) => apiRequest('/user/lead-campaigns', { method: 'POST', body: JSON.stringify(data) }),
  update: (id, data) => apiRequest(`/user/lead-campaigns/${id}`, { method: 'PUT', body: JSON.stringify(data) }),
  delete: (id) => apiRequest(`/user/lead-campaigns/${id}`, { method: 'DELETE' }),
  analytics: (id, params) => apiRequest(`/user/lead-campaigns/${id}/analytics?${new URLSearchParams(params)}`),
  sources: (id, params) => apiRequest(`/user/lead-campaigns/${id}/sources?${new URLSearchParams(params)}`),
  export: (id, params) => apiRequest(`/user/lead-campaigns/${id}/export?${new URLSearchParams(params)}`)
};
```

### 2. Form Validation
```javascript
const validateCampaignForm = (formData) => {
  const errors = {};

  if (!formData.name?.trim()) {
    errors.name = 'Campaign name is required';
  }

  if (!formData.source_slug?.trim()) {
    errors.source_slug = 'Source slug is required';
  }

  if (!formData.assistant_id) {
    errors.assistant_id = 'Assistant selection is required';
  }

  if (!formData.source_type) {
    errors.source_type = 'Source type is required';
  }

  // Validate schedule windows
  if (!formData.schedule_windows?.windows?.length) {
    errors.schedule_windows = 'At least one schedule window is required';
  }

  // Validate retry settings
  if (formData.retry_settings?.max_retries < 0 || formData.retry_settings?.max_retries > 10) {
    errors.retry_settings = 'Max retries must be between 0 and 10';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
```

### 3. Date/Time Utilities
```javascript
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatPercentage = (value) => {
  return `${parseFloat(value).toFixed(2)}%`;
};
```

## Styling Guidelines

### 1. CSS Variables
```css
:root {
  /* Colors */
  --primary-color: #3b82f6;
  --secondary-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --success-color: #10b981;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;

  /* Text Colors */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
}
```

### 2. Component Styles
```css
/* Metric Cards */
.metric-card {
  background: var(--bg-primary);
  border: 1px solid var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--text-primary);
  margin: var(--spacing-sm) 0;
}

.metric-change.up {
  color: var(--success-color);
}

.metric-change.down {
  color: var(--danger-color);
}

/* Form Styles */
.campaign-form {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.form-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  border: 1px solid var(--bg-tertiary);
  border-radius: var(--radius-md);
}

.form-section h3 {
  margin-top: 0;
  color: var(--text-primary);
  border-bottom: 1px solid var(--bg-tertiary);
  padding-bottom: var(--spacing-sm);
}

/* Table Styles */
.source-performance-table table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.source-performance-table th,
.source-performance-table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--bg-tertiary);
}

.source-performance-table th {
  background: var(--bg-secondary);
  font-weight: 600;
  cursor: pointer;
}

.source-performance-table th:hover {
  background: var(--bg-tertiary);
}
```

## Security Considerations

### 1. Authentication
- Store JWT tokens securely in localStorage
- Implement token refresh mechanism
- Clear tokens on logout
- Validate user role on each protected route

### 2. API Security
- Always include Authorization header
- Validate API responses
- Handle 401/403 errors appropriately
- Sanitize user inputs

### 3. Data Protection
- Mask sensitive data (API keys, phone numbers)
- Implement copy-to-clipboard for API keys
- Use HTTPS for all API calls
- Validate form inputs client-side and server-side

## Performance Optimization

### 1. Code Splitting
```javascript
// Lazy load components
const CampaignAnalytics = lazy(() => import('./components/CampaignAnalytics'));
const ApiKeyManagement = lazy(() => import('./components/ApiKeyManagement'));

// Use Suspense for loading states
<Suspense fallback={<LoadingSpinner />}>
  <CampaignAnalytics campaignId={campaignId} />
</Suspense>
```

### 2. Data Caching
```javascript
// Use React Query or SWR for data caching
const { data: campaigns, isLoading, error } = useQuery(
  ['campaigns', filters],
  () => leadCampaignAPI.list(filters),
  {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000 // 10 minutes
  }
);
```

### 3. Pagination
```javascript
// Implement pagination for large datasets
const [pagination, setPagination] = useState({
  page: 1,
  limit: 20,
  total: 0
});

const loadMore = () => {
  setPagination(prev => ({
    ...prev,
    page: prev.page + 1
  }));
};
```

This comprehensive frontend guide provides everything needed to build a complete Lead Campaign management interface that integrates seamlessly with the backend system we've implemented.
```
```
```
