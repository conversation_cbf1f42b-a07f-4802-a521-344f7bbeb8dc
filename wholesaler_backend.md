# Lead Campaign & Voice AI System

## Overview
This system implements a comprehensive Lead Campaign management platform with Voice AI capabilities, designed for wholesalers to manage lead ingestion, automated calling, call transfers, and revenue tracking with Ringba integration.

## Key Features

### 🎯 Lead Campaign Management
- **Wholesaler Role**: New user role with specific permissions for lead management
- **Campaign Configuration**: Source tracking, retry settings, schedule windows
- **API Key Management**: Secure API keys for lead ingestion with rate limiting
- **Multi-Vertical Support**: Auto insurance, health insurance, life insurance, home insurance

### 📞 Automated Voice AI Processing
- **Lead Queue System**: Automated processing based on campaign criteria
- **Schedule Compliance**: Respects time windows and timezone settings
- **Retry Logic**: Configurable retry attempts with exponential backoff
- **Concurrency Control**: Manages multiple simultaneous calls

### 💰 Ringba Integration & Revenue Tracking
- **RTB (Real-Time Bidding)**: Ping/post workflow for call transfers
- **Payment Synchronization**: Daily sync of payment data from Ringba
- **Revenue Attribution**: Accurate revenue tracking per lead and source

### 📊 Advanced Analytics & Reporting
- **Campaign Analytics**: Conversion rates, call volumes, revenue metrics
- **Source Performance**: ROI analysis by lead source
- **Cost Tracking**: Penny-accurate cost attribution per lead
- **Data Export**: CSV export for detailed analysis

### 💸 Detailed Cost Tracking
- **Service Breakdown**: Deepgram, Claude, ElevenLabs, Twilio costs
- **Penny Accuracy**: 4-decimal precision cost tracking
- **Performance Metrics**: Cost per lead, revenue per lead, ROI

## System Architecture

### Database Schema
```
voiceoutreach_lead_campaigns     - Campaign configuration
voiceoutreach_lead_queue         - Incoming leads awaiting processing
voiceoutreach_lead_api_keys      - API key management
voiceoutreach_ringba_transactions - Ringba payment tracking
voiceoutreach_lead_costs         - Detailed cost breakdown
voiceoutreach_lead_analytics     - Performance analytics
voiceoutreach_api_usage_logs     - API usage monitoring
```

### API Endpoints

#### Lead Campaign Management
- `POST /v3/api/custom/voiceoutreach/user/lead-campaigns` - Create campaign
- `GET /v3/api/custom/voiceoutreach/user/lead-campaigns` - List campaigns
- `GET /v3/api/custom/voiceoutreach/user/lead-campaigns/:id` - Get campaign details
- `PUT /v3/api/custom/voiceoutreach/user/lead-campaigns/:id` - Update campaign
- `DELETE /v3/api/custom/voiceoutreach/user/lead-campaigns/:id` - Archive campaign

#### Lead Ingestion
- `POST /v3/api/custom/voiceoutreach/leads/webhook/leads` - Submit leads
- `GET /v3/api/custom/voiceoutreach/leads/webhook/leads/status/:leadId` - Check lead status

#### Analytics & Reporting
- `GET /v3/api/custom/voiceoutreach/user/lead-campaigns/:id/analytics` - Campaign analytics
- `GET /v3/api/custom/voiceoutreach/user/lead-campaigns/:id/sources` - Source performance
- `GET /v3/api/custom/voiceoutreach/user/lead-campaigns/:id/export` - Export data

## Installation & Setup

### 1. Database Setup
```bash
mysql -u username -p database_name < setup_lead_campaign_system.sql
```

### 2. Dependencies
```bash
npm install node-cron moment-timezone
```

### 3. Environment Variables
```env
RINGBA_API_URL=https://api.ringba.com/v2
DEEPGRAM_API_KEY=your_deepgram_key
CLAUDE_API_KEY=your_claude_key
ELEVENLABS_API_KEY=your_elevenlabs_key
```

### 4. Cron Jobs
The system automatically starts these background processes:
- **Lead Queue Processor**: Runs every minute
- **Ringba Payment Sync**: Runs daily at 2 AM

## Usage Examples

### Creating a Lead Campaign
```javascript
const campaignData = {
  name: "Auto Insurance Campaign",
  source_slug: "auto-leads-source-1",
  source_type: "auto_insurance",
  assistant_id: 123,
  retry_settings: {
    max_retries: 3,
    retry_intervals: [1, 4, 24], // hours
    retry_conditions: ["no_answer", "busy"]
  },
  schedule_windows: {
    timezone: "America/New_York",
    windows: [{
      days: [1, 2, 3, 4, 5], // Monday-Friday
      start: "09:00",
      end: "17:00"
    }]
  },
  ringba_api_settings: {
    api_key: "your_ringba_api_key",
    campaign_token: "your_campaign_token"
  }
};
```

### Submitting Leads
```javascript
const leadData = {
  phone_number: "+15551234567",
  first_name: "John",
  last_name: "Doe",
  email: "<EMAIL>",
  zip_code: "12345",
  gender: "male",
  currently_insured: true,
  tcpa_call_consent: true,
  vehicle_count: 2,
  credit_score: "excellent",
  jornaya_lead_id: "lead_123456"
};

// Submit via API
fetch('/v3/api/custom/voiceoutreach/leads/webhook/leads', {
  method: 'POST',
  headers: {
    'x-api-key': 'your_api_key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify([leadData])
});
```

## Lead Processing Workflow

1. **Lead Ingestion**: Leads submitted via webhook API
2. **Validation**: Required fields and data quality checks
3. **Queue Placement**: Leads added to processing queue with priority
4. **Schedule Check**: System waits for appropriate time window
5. **Call Initiation**: Voice AI calls the lead
6. **Qualification**: AI qualifies the lead based on script
7. **Transfer Decision**: If qualified, ping Ringba for transfer
8. **Call Transfer**: Transfer to highest bidder or configured number
9. **Payment Tracking**: Sync payment data from Ringba
10. **Analytics Update**: Update campaign and source metrics

## Monitoring & Alerts

### Health Checks
- Queue processing status
- API endpoint availability
- Database connectivity
- External service integration

### Key Metrics
- Lead processing rate
- Call answer rate
- Transfer success rate
- Cost per lead
- Revenue per lead
- ROI by source

## Security Features

### Authentication
- JWT-based user authentication
- Role-based access control (user, admin, wholesaler)
- API key authentication for webhooks

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- Rate limiting on API endpoints
- Secure API key storage

## Performance Optimization

### Database
- Optimized indexes for query performance
- Partitioning for large datasets
- Connection pooling

### Processing
- Concurrent call handling (configurable limits)
- Efficient queue processing
- Background job optimization

## Troubleshooting

### Common Issues
1. **Leads not processing**: Check schedule windows and queue status
2. **API authentication errors**: Verify API key status and permissions
3. **Ringba integration failures**: Check API credentials and network connectivity
4. **Cost tracking discrepancies**: Verify service usage data accuracy

### Debug Commands
```bash
# Test lead queue processor
node cronjobs/leadQueueProcessor.js

# Run system tests
node test/test_lead_campaign_system.js

# Check database schema
mysql -e "SHOW TABLES LIKE 'voiceoutreach_lead_%'"
```

## Support & Documentation

### API Documentation
- Detailed endpoint documentation with examples
- Authentication guide
- Error code reference

### Integration Guides
- Ringba setup and configuration
- Lead source integration
- Custom script development

### Best Practices
- Lead quality optimization
- Cost management strategies
- Performance tuning guidelines

## Version History

### v1.0.0 (Current)
- Initial Lead Campaign system implementation
- Wholesaler role and permissions
- Ringba integration
- Cost tracking system
- Analytics and reporting

## Contributing

1. Follow existing code patterns
2. Add tests for new functionality
3. Update documentation
4. Test with sample data before deployment

## License
Proprietary - VoiceOutreach Backend System
